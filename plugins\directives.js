// 注册全局指令

import Vue from 'vue';
import ClickOutSide from 'vue-v-clickoutside';

// v-clickoutside
Vue.directive('clickoutside', ClickOutSide);

// v-backtop
Vue.directive('backtop', (el) => {
  el.style.visibility = 'hidden';
  const docEle = document.documentElement || document.body;

  window.document.addEventListener('scroll', () => {
    el.style.visibility = docEle.scrollTop > 250 ? 'visible' : 'hidden';
  });

  el.addEventListener('click', () => {
    let step = 0;
    const timer = setInterval(() => {
      step += 100;
      docEle.scrollTop -= step;
      docEle.scrollTop === 0 && clearInterval(timer);
    }, 30);
  });
});

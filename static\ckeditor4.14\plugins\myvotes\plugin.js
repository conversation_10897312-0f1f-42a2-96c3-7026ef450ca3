CKEDITOR.plugins.add("myvotes", {
  icons: "myvotes",
  init: function (editor) {
    editor.addCommand("myvotes", {
      exec: function () {
        // 在 window 上触发事件
        var myEvent = new CustomEvent("ckeditormyvotesclick");
        if (window.dispatchEvent) {
          window.dispatchEvent(myEvent);
        } else {
          window.fireEvent(myEvent);
        }
      },
    });
    editor.ui.addButton("myvotes", {
      label: "投票",
      command: "myvotes",
      toolbar: "insert",
    });
  },
});

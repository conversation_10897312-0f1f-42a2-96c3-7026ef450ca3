<template>
  <div class="comment-input-wrapper">
    <div class="comment-content-input-box">
      <!-- 如果没有登录显示: 请先登录 -->
      <div v-if="!islogin" class="login-tips text-gray flex jc-center ai-center">
        <span>{{ $t('components.comment_input.first_please') }}</span>
        <button class="lv-icon mar-left-5 mar-right-5 btn lv-0" @click="toggleLoginLayer({ show: true })">
          <span>{{ $t('components.comment_input.login') }}</span>
        </button>
        <span>{{ $t('components.comment_input.then_publish') }}</span>
      </div>
      <div class="input-box">
        <textarea
          v-model="content"
          :maxlength="articleNum"
          class="comment-content-input"
          :placeholder="pleaseholder || $t('detail.publish_replay')"
        ></textarea>
        <span class="text-counter fs-xs text-gray">{{ articleNum - content.length }}</span>
      </div>
    </div>
    <!-- 底部按钮 -->
    <div
      class="comment-input-btns"
      :style="{
        'padding-top': btnBoxTopPad,
        'padding-bottom': btnBoxBottomPad
      }"
    >
      <!-- 是否匿名 -->
      <slot name="isShowName2"></slot>
      <!-- 第一种按钮 -->
      <el-upload
        v-if="showImageBtn"
        ref="commentimg"
        type="file"
        accept="image/*"
        class="flex-inline"
        :action="actionURL"
        :data="uploadImageJSONData"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-success="onUploadSuccess"
        :on-error="onUploadError"
      >
        <button slot="trigger" class="btn img-btn btn-gray">
          <span>{{ $t('detail.btn_img') }}</span>
          <div v-show="showLoading || commentPrviewImg" class="image-preview btn-preview-box tail-arrow-top">
            <svg
              class="close-preview-btn"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              viewBox="0 0 800 800"
              @click.stop="removeCommentImg"
            >
              <path
                d="M447.5,400l307.2-312c7.2-7.3,11.1-16.9,11.1-27.1c0-10.2-4-19.8-11.1-27.1c-7.4-7.5-17.4-11.7-28.1-11.7
	c-10.7,0-20.7,4.2-28.1,11.7L400,336.9L101.4,33.7C93.9,26.1,84,22,73.3,22s-20.7,4.2-28.1,11.7c-14.7,14.9-14.7,39.3,0,54.2
	L352.5,400L45.3,712.1c-7.2,7.3-11.1,16.9-11.1,27.1c0,10.2,4,19.8,11.1,27.1c7.4,7.5,17.4,11.7,28.1,11.7
	c10.7,0,20.7-4.2,28.1-11.7L400,463.1l298.6,303.3c7.4,7.5,17.4,11.7,28.1,11.7s20.7-4.2,28.1-11.7c14.7-14.9,14.7-39.3,0-54.2
	L447.5,400z"
              />
            </svg>
            <Loading v-show="showLoading" />
            <img :src="commentPrviewImg" />
          </div>
        </button>
      </el-upload>
      <!-- 第二种按钮,该按钮与发帖的图片一样的功能 -->
      <el-upload
        v-if="showOtherImageBtn"
        ref="commentimg"
        type="file"
        accept="image/*"
        class="flex-inline"
        :action="actionURL"
        :data="uploadImageJSONData"
        :show-file-list="false"
        :before-upload="beforeOtherUpload"
        :on-success="contentImgUploadSuccess"
        :on-error="onUploadError"
      >
        <div slot="trigger" class="img-box">
          <div class="imgWrap img-box">
            <img src="@/assets/images/tupian.png" alt="" />
          </div>
        </div>
      </el-upload>
      <!-- 第一种表情包 -->
      <button v-if="showEmojiBtn" class="btn emoji-btn btn-gray" @click="showEmojiPanel = true">
        <span>{{ $t('detail.btn_emoji') }}</span>
        <div :class="{ show: showEmojiPanel }" class="comment-input-emoji tail-arrow-top hide">
          <svg
            class="close-preview-btn"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            viewBox="0 0 800 800"
            @click.stop="showEmojiPanel = false"
          >
            <path
              d="M447.5,400l307.2-312c7.2-7.3,11.1-16.9,11.1-27.1c0-10.2-4-19.8-11.1-27.1c-7.4-7.5-17.4-11.7-28.1-11.7
	c-10.7,0-20.7,4.2-28.1,11.7L400,336.9L101.4,33.7C93.9,26.1,84,22,73.3,22s-20.7,4.2-28.1,11.7c-14.7,14.9-14.7,39.3,0,54.2
	L352.5,400L45.3,712.1c-7.2,7.3-11.1,16.9-11.1,27.1c0,10.2,4,19.8,11.1,27.1c7.4,7.5,17.4,11.7,28.1,11.7
	c10.7,0,20.7-4.2,28.1-11.7L400,463.1l298.6,303.3c7.4,7.5,17.4,11.7,28.1,11.7s20.7-4.2,28.1-11.7c14.7-14.9,14.7-39.3,0-54.2
	L447.5,400z"
            />
          </svg>
          <EmojiPanel @input-emoji="inputEmoji" />
        </div>
      </button>
      <!-- 第二种表情包 -->
      <button v-else class="btn emoji-btn btn2" @click="showEmojiPanel = true">
        <div class="imgWrap img-box">
          <img src="@/assets/images/biaoqing.png" alt="" />
        </div>
        <div :class="{ show: showEmojiPanel }" class="comment-input-emoji tail-arrow-top hide">
          <svg
            class="close-preview-btn"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            viewBox="0 0 800 800"
            @click.stop="showEmojiPanel = false"
          >
            <path
              d="M447.5,400l307.2-312c7.2-7.3,11.1-16.9,11.1-27.1c0-10.2-4-19.8-11.1-27.1c-7.4-7.5-17.4-11.7-28.1-11.7
	c-10.7,0-20.7,4.2-28.1,11.7L400,336.9L101.4,33.7C93.9,26.1,84,22,73.3,22s-20.7,4.2-28.1,11.7c-14.7,14.9-14.7,39.3,0,54.2
	L352.5,400L45.3,712.1c-7.2,7.3-11.1,16.9-11.1,27.1c0,10.2,4,19.8,11.1,27.1c7.4,7.5,17.4,11.7,28.1,11.7
	c10.7,0,20.7-4.2,28.1-11.7L400,463.1l298.6,303.3c7.4,7.5,17.4,11.7,28.1,11.7s20.7-4.2,28.1-11.7c14.7-14.9,14.7-39.3,0-54.2
	L447.5,400z"
            />
          </svg>
          <EmojiPanel @input-emoji="inputEmoji" />
        </div>
      </button>
      <!-- 投票按钮 -->
      <button v-if="showVoteBtn" class="btn emoji-btn btn3" @click="showVoteDialog = true">
        <div class="imgWrap img-box" @click="showVoteDialog = true">
          <img src="@/assets/images/toupiao.png" alt="" />
        </div>
      </button>
      <!-- 附加功能 -->
      <button v-if="showAddBtn" class="btn emoji-btn btn4" @click="showAddDialog = true">
        <div class="flex">
          <div class="imgWrap img-box1" @click="showAddDialog = true">
            <img src="@/assets/images/add.png" alt="" />
          </div>
          <span class="img-text">{{ $t('guildhall.add_skill') }}</span>
        </div>
      </button>
      <slot name="cacheDrafts"></slot>
      <slot name="isShowName"></slot>
      <button class="btn publish-btn btn-primary" @click="confirmSubmit">
        {{ btnContent }}
      </button>
    </div>
    <!-- 投票  -->
    <el-dialog :visible="showVoteDialog" :close-on-click-modal="false" top="10vh" width="580px" class="votes-dialog" @close="closeVoteLayer">
      <p slot="title" class="fs-ml vote-title text-dark">
        <span>{{ $t('write.votes') }}</span>
      </p>

      <!-- 投票: 标题 -->
      <div class="item">
        <p class="sub-title">
          {{ $t('write.vote_title') }}
          <span class="require">*</span>
        </p>
        <div class="flex">
          <input v-model="vote.title" type="text" class="input-control" max="20" maxlength="20" :placeholder="$t('write.input_vote_title')" />
          <span class="flex ai-center text-counter">{{ 20 - vote.title.length }}</span>
        </div>
      </div>

      <!-- 投票: 类型 -->
      <div class="item">
        <div class="sub-title">
          <span>{{ $t('write.vote_type') }}</span>
          <span class="require mar-right-20">*</span>
          <div class="checkboxs flex-inline ai-center">
            <p class="mar-right-20 flex" @click="isMultipleChoose = false">
              <span class="radio-btn" :class="{ checked: !isMultipleChoose }"></span>
              <span>{{ $t('write.single_select') }}</span>
            </p>
            <p class="mar-right-20 flex" @click="isMultipleChoose = true">
              <span class="radio-btn" :class="{ checked: isMultipleChoose }"></span>
              <span>{{ $t('write.multiple_select') }}</span>
            </p>
            <p v-show="isMultipleChoose" class="mar-right-20" @click="isMultipleChoose = true">
              <input
                v-model="vote.max_choices"
                :placeholder="$t('write.max_select_options')"
                type="number"
                min="2"
                class="input-control"
                style="min-width: 120px"
                :max="vote.options.length"
              />
            </p>
          </div>
        </div>
      </div>

      <!-- 投票: 时长 -->
      <div class="item">
        <div class="sub-title">
          <span>{{ $t('write.vote_expried_time') }}</span>
          <span class="require mar-right-20">*</span>
          <div class="checkboxs flex-inline">
            <p class="mar-right-20 flex" @click="vote.expiration = 3">
              <span class="radio-btn" :class="{ checked: vote.expiration === 3 }"></span>
              <span>3 {{ $t('write.day') }}</span>
            </p>
            <p class="mar-right-20 flex" @click="vote.expiration = 5">
              <span class="radio-btn" :class="{ checked: vote.expiration === 5 }"></span>
              <span>7 {{ $t('write.day') }}</span>
            </p>
            <p class="mar-right-20 flex" @click="vote.expiration = 7">
              <span class="radio-btn" :class="{ checked: vote.expiration === 7 }"></span>
              <span>30 {{ $t('write.day') }}</span>
            </p>
          </div>
        </div>
      </div>

      <!-- 投票: 选项 -->
      <div class="vote-items">
        <div class="item" style="marigin: 0; padding:0">
          <p class="sub-title">
            {{ $t('write.vote_options') }}
            <span class="require">*</span>
          </p>
          <div v-for="(item, index) in vote.options" :key="index" style="margin-bottom: 10px" class="flex">
            <input v-model="vote.options[index]" type="text" class="input-control" maxlength="12" :placeholder="$t('write.input_option_title')" />
            <p class="flex ai-center text-counter">{{ 12 - item.length }}</p>
            <button class="flex btn btn-del ai-center text-counter" @click="delVoteItem(index)">
              <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 552.7 576">
                <path
                  d="M656.8,191.1h-513c-10.8,0-19.6,9.9-19.6,22.1s8.8,22.1,19.6,22.1h31.7V565.2c0,67.4,48.7,122.3,108.6,122.3H516.5c59.9,0,108.6-54.9,108.6-122.3V235.3h31.7c10.8,0,19.6-9.9,19.6-22.1S667.6,191.1,656.8,191.1ZM311.4,648.2H284c-40.7,0-73.7-37.2-73.7-83V235.3H311.5V648.2Zm143.9,0H345.2V235.3H455.3V648.2Zm134.9-83c0,45.8-33.1,83-73.7,83H489.1V235.3H590.3V565.2Z"
                  transform="translate(-123.95 -111.75)"
                />
                <path
                  class="cls-1"
                  d="M357.6,156.8h85.3a22.4,22.4,0,1,0,0-44.8H357.6a22.4,22.4,0,0,0,0,44.8Z"
                  transform="translate(-123.95 -111.75)"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
      <div class="item">
        <button class="btn text-primary" @click="addVoteItem">{{ $t('write.add_options') }}</button>
      </div>

      <!-- 投票: 底部按钮 -->
      <p slot="footer" class="dialog-footer flex ai-center jc-center">
        <button class="btn btn-publish btn-primary" @click="confirmVote">{{ $t('write.confirm') }}</button>
      </p>
    </el-dialog>
    <!-- 附加功能 -->
    <el-dialog :visible="showAddDialog" :close-on-click-modal="false" top="10vh" width="440px" class="votes-dialog" @close="closeAddLayer">
      <p slot="title" class="fs-ml vote-title text-dark">
        <span>{{ $t('guildhall.add_skill') }}</span>
      </p>
      <!-- 第一行 -->
      <div class="item1">
        <div class="sub-title">
          <div class="checkboxs flex-inline">
            <p class="mar-right-80 flex" @click="changeOne">
              <span class="radio-btn" :class="{ checked: add.only_passer === true }"></span>
              <span>{{ $t('guildhall.only_payer') }}</span>
            </p>
            <!-- <p class="flex" @click="changeTwo">
              <span class="radio-btn" :class="{ checked2: add.expiration2 === true }"></span>
              <span>匿名</span>
            </p> -->
          </div>
        </div>
      </div>
      <!-- 后面行 -->
      <div class="item1">
        <div class="sub-title">
          <div class="checkboxs">
            <p class="mar-rt flex" @click="changeThree">
              <span class="radio-btn" :class="{ checked: add.hasPay === true }"></span>
              <span>{{ $t('guildhall.content_clock') }}</span>
            </p>
            <div v-if="add.hasPay">
              <div class="flex input-box">
                <p class="sub-title add-title" style="text-align: end;">
                  {{ $t('guildhall.pay_bin') }}
                  <span class="require">*</span>
                </p>
                <div class="add-box">
                  <input v-model="add.need_pay.coin" type="number" class="input-control" min="1" :placeholder="$t('write.input_integer')" />
                  <p class="flex ai-center text-counter">{{ $t('guildhall.one') }}</p>
                </div>
              </div>
              <div class="flex input-box">
                <p class="sub-title add-title" style="text-align: end;">
                  {{ $t('guildhall.pay_coin') }}
                  <span class="require">*</span>
                </p>
                <div class="add-box">
                  <input
                    v-model="add.need_pay.node"
                    type="text"
                    class="input-control"
                    maxlength="12"
                    :placeholder="$t('write.input_100_integer')"
                    @blur="check_node()"
                  />
                  <p class="flex ai-center text-counter">%</p>
                </div>
              </div>
            </div>
            <p class="flex" style="margin-bottom: 19px;" @click="changeFour">
              <span class="radio-btn" :class="{ checked2: add.hasReward === true }" style="display: table;width: 15.5px;"></span>
              <span
                >{{ $t('guildhall.pay_content_1') }}<span style="color:#39d7d9">{{ all_coin }}</span
                >{{ $t('guildhall.pay_content_2') }}</span
              >
            </p>
            <div v-if="add.hasReward">
              <div class="flex input-box">
                <p class="sub-title add-title" style="text-align: end;">
                  {{ $t('guildhall.get_coin') }}
                  <span class="require">*</span>
                </p>
                <div class="add-box">
                  <input
                    v-model="add.reward.coin"
                    type="text"
                    class="input-control"
                    maxlength="12"
                    :placeholder="$t('guildhall.input_1000')"
                    @blur="check_reward(0)"
                  />
                  <p class="flex ai-center text-counter">{{ $t('guildhall.one') }}</p>
                </div>
              </div>
              <div class="flex input-box">
                <p class="sub-title add-title" style="text-align: end;">
                  {{ $t('guildhall.count_get') }}
                  <span class="require">*</span>
                </p>
                <div class="add-box">
                  <input
                    v-model="add.reward.stock"
                    type="text"
                    class="input-control"
                    maxlength="12"
                    :placeholder="$t('guildhall.input_1000')"
                    @blur="check_reward(1)"
                  />
                  <p class="flex ai-center text-counter">{{ $t('guildhall.times') }}</p>
                </div>
              </div>
              <div class="flex input-box">
                <p class="sub-title add-title" style="text-align: end;">
                  {{ $t('guildhall.every_get') }}
                  <span class="require">*</span>
                </p>
                <div class="add-box">
                  <input
                    v-model="add.reward.num"
                    type="text"
                    class="input-control"
                    maxlength="12"
                    :placeholder="$t('guildhall.input_100')"
                    @blur="check_reward(2)"
                  />
                  <p class="flex ai-center text-counter">{{ $t('guildhall.times') }}</p>
                </div>
              </div>
              <div class="flex input-box">
                <p class="sub-title add-title" style="text-align: end;">
                  {{ $t('guildhall.get_count') }}
                  <span class="require">*</span>
                </p>
                <div class="add-box">
                  <input
                    v-model="add.reward.odds"
                    type="text"
                    class="input-control"
                    maxlength="12"
                    :placeholder="$t('guildhall.input_100')"
                    @blur="check_reward(3)"
                  />
                  <p class="flex ai-center text-counter">%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 投票: 底部按钮 -->
      <p slot="footer" class="dialog-footer flex ai-center jc-center">
        <button class="btn btn-publish btn-primary" @click="confirmAdd">{{ $t('guildhall.save') }}</button>
      </p>
    </el-dialog>
  </div>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';
import EmojiPanel from '@/components/EmojiPanel.vue';
import Loading from '@/components/Loading.vue';

export default {
  components: {
    EmojiPanel,
    Loading
  },
  props: {
    pleaseholder: {
      type: String,
      default: ''
    },
    islogin: {
      // 未登录
      type: Boolean,
      default: false
    },
    showImageBtn: {
      // 是否显示第一种图片按钮
      type: Boolean,
      default: true
    },
    showOtherImageBtn: {
      // 是否显示第二种图片按钮
      type: Boolean,
      default: false
    },
    showEmojiBtn: {
      // 是否显示表情按钮
      type: Boolean,
      default: true
    },
    showVoteBtn: {
      // 是否显示投票按钮
      type: Boolean,
      default: false
    },
    showAddBtn: {
      // 时候显示附加功能按钮
      type: Boolean,
      default: false
    },
    // btnTextLang: {
    //   // "确认功能" 按钮上的文字的多语言索引
    //   type: String,
    //   default: 'detail.btn_publish'
    // },
    btnBoxTopPad: {
      // 按钮容器上面的 padding
      type: String,
      default: '0'
    },
    btnBoxBottomPad: {
      // 按钮容器下面的 padding
      type: String,
      default: '0'
    },
    uploadUrl: {
      // 上传参数
      type: String,
      default: ''
    },
    uploadSuccess: {
      // 上传成功的回调
      type: Function,
      default: Function.prototype
    },
    uploadError: {
      // 上传失败的回调
      type: Function,
      default: Function.prototype
    },
    // 按钮名字
    btnContent: {
      type: String,
      default: '发布'
    },
    // 文章字数
    articleNum: {
      type: Number,
      default: 10000
    }
  },
  data() {
    return {
      showLoading: false,
      content: '',
      images: {},
      commentPrviewImg: '',
      uploadFormData: { security_key: '', md5: '' },
      showEmojiPanel: false,
      showVoteDialog: false,
      showAddDialog: false,
      vote: {
        // 投票
        title: '',
        max_choices: '',
        expiration: 3,
        options: ['', '']
      },
      add: {
        // 附加功能
        only_passer: false,
        // expiration2: false,
        hasPay: false,
        hasReward: false,
        need_pay: {
          coin: '',
          node: ''
        },
        reward: {
          coin: '',
          stock: '',
          num: '',
          odds: ''
        }
      },
      isMultipleChoose: true,
      article: {
        poll: {}
      },
      content1: '',
      all_coin: '?', // 回复奖励帖总额
      contentImages: [] // 已经存在或者上传成功的文章内容图片 {id, url, name}
    };
  },
  computed: {
    ...mapGetters('login', ['loginUser', 'isLogin']),
    actionURL() {
      return process.env.VUE_APP_UPLOAD_URL + this.uploadUrl;
    },
    uploadImageJSONData() {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.uploadFormData.security_key = this.loginUser.security_key;
      const data = this.apiParamsGenerator(this.uploadFormData, true);
      return data;
    }
  },
  watch: {
    content(val) {
      this.setLocalStorageAndTime('inputContent', val, window);
    },
    // 回复奖励帖总额,实时计算
    add: {
      handler() {
        if (this.add.reward.coin && this.add.reward.stock) {
          this.all_coin = Number(this.add.reward.coin) * Number(this.add.reward.stock);
        } else {
          this.all_coin = '?';
        }
      },
      deep: true
    }
  },
  mounted() {
    this.content1 = this.getLocalStorageAndTime('inputContent', 1, window);
    if (this.content1) {
      this.content = this.content1;
    }
  },
  methods: {
    ...mapMutations('login', ['toggleLoginLayer']),
    setLocalStorageAndTime(key, value, window) {
      window.localStorage.setItem(key, JSON.stringify({ data: value, time: new Date().getTime() }));
    },
    getLocalStorageAndTime(key, exp, window) {
      // 用showAddBtn字段来识别改组件是需要缓存文章的组件
      if (this.showAddBtn) {
        // 获取数据
        const data = window.localStorage.getItem(key);
        if (!data) return null;
        const dataObj = JSON.parse(data);
        const nowtime = parseInt(((new Date().getTime() - dataObj.time) / 1000 / 60) % 60);
        // 与过期时间比较
        if (nowtime >= exp) {
          // 过期删除返回null
          window.localStorage.removeItem(key);
          return null;
        } else {
          return dataObj.data;
        }
      }
    },
    // 确认提交
    confirmSubmit() {
      if (this.showLoading) {
        return this.$message.error(this.$t('tips.file_is_uploading'));
      }
      // eslint-disable-next-line prefer-const
      let { content, images } = this;
      for (const key in images) {
        content += `[img]${key}[/img]`;
      }
      this.$emit('confirm', { content, images });

      this.images = {};
      this.commentPrviewImg = '';
      this.showEmojiPanel = false;
    },

    // 输入表情
    inputEmoji(emoji) {
      this.content += emoji.code;
    },

    // 清空内容: 在父组件中发布成功后调用
    clearContent() {
      this.content = '';
      if (this.vote) {
        this.vote = {
          // 投票
          title: '',
          max_choices: '',
          expiration: 3,
          options: ['', '']
        };
      }
      if (this.add) {
        this.add = {
          // 附加功能
          only_passer: false,
          // expiration2: false,
          hasPay: false,
          hasReward: false,
          need_pay: {
            coin: '',
            node: ''
          },
          reward: {
            coin: '',
            stock: '',
            num: '',
            odds: ''
          }
        };
        this.all_coin = '?';
      }
    },
    // 添加图片到内容中去
    addImageContent(url) {
      this.content += `\n[img]${url}[/img]\n`;
    },
    // 先压缩在上传
    beforeUpload(file) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        if (!this.isLogin) {
          this.toggleLoginLayer({ show: true });
          return reject(file);
        }
        this.showLoading = true;
        const minFile = await this.imageCompression(file, 0.5);

        // 获取文件的 md5 和登录的用户信息
        const md5 = await this.getFileMD5(minFile);

        // 判断文件是否存在, 如果存在就不需要上传了
        const res = await this.$axios.$post('/upload/get-discuss-image', {
          md5
        });

        if (res.code === 0 && res.data && res.data.res_id) {
          this.commentPrviewImg = res.data.res_url;
          this.images = { [res.data.res_id]: res.data.res_id };
          this.showLoading = false;
          return reject(minFile);
        }

        this.uploadFormData.md5 = md5;
        resolve(minFile);
      });
    },
    // 上传之前处理图片: 压缩获取文件md5(发帖上传图片按钮)
    beforeOtherUpload(file) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        // 是否超过允许的最大大小 10M
        if (file.size > 10485760) {
          this.$message.error(this.$t('write.file_too_max'));
          return reject(file);
        }

        // 压缩图片(如果大于1MB就压缩到1M以下)
        const minFile = await this.imageCompression(file);
        // 获取文件md5 判断文件是否存在, 存在就不上传
        const md5 = await this.getFileMD5(minFile);
        const res = await this.$axios.$post('/upload/get-article-image', { md5 });
        if (res.code === 0 && res.data) {
          this.contentImgUploadSuccess(res, { raw: file });
          return reject(minFile);
        }

        // 如果图片不存在就组合数据上传文件
        this.uploadFormData.md5 = md5;
        return resolve(minFile);
      });
    },
    // 文章正文内容上传成功后
    contentImgUploadSuccess(res, file) {
      if (res.code === 0) {
        const { res_id: id, res_url: url } = res.data;
        const img = this.contentImages.find((item) => item.id === id);
        if (!img) {
          this.contentImages.push({ id, url, name: file.raw.name });
          this.$emit('images', this.contentImages);
        }
      }
    },

    // 移除图片(中断正在上传的, 清空 images)
    removeCommentImg() {
      this.$refs.commentimg.abort();
      this.images = {};
      this.showLoading = false;
      this.commentPrviewImg = '';
    },

    // 图片上传成功
    onUploadSuccess(res, files) {
      if (res.code === 0 && res.data && res.data.res_id) {
        this.showLoading = false;
        this.images = { [res.data.res_id]: res.data.res_id };
        this.commentPrviewImg = res.data.res_url;
        this.uploadSuccess();
      }
    },

    // 文件上传失败
    onUploadError() {
      this.$message.error(this.$t('tips.file_upload_fail'));
      this.showLoading = false;
      this.uploadError();
    },
    // 取消投票
    closeVoteLayer() {
      this.hasVotes = false;
      this.showVoteDialog = false;
    },
    closeAddLayer() {
      this.showAddDialog = false;
    },
    // 投票: 确定
    confirmVote() {
      this.hasVotes = true;
      const { vote } = this;
      if (!vote.title.trim()) {
        this.$message.error(this.$t('write.input_vote_title'));
        return;
      }

      vote.options = vote.options.filter((item) => item.trim());

      if (!this.isMultipleChoose) {
        vote.max_choices = 1;
      } else {
        if (!vote.max_choices) {
          this.$message.error(this.$t('write.inpu_max_select'));
          return;
        }
        if (vote.max_choices < 2 || vote.max_choices > vote.options.length) {
          this.$message.error(this.$t('write.vote_multiple_select'));
          return;
        }
      }

      if (!this.vote.expiration) {
        this.$message.error(this.$t('write.input_vote_time'));
      }
      this.article.poll = vote;
      this.$emit('poll', this.article.poll);
      this.showVoteDialog = false;
    },

    // 增加投票选项
    addVoteItem() {
      for (const item of this.vote.options) {
        if (!item.trim()) {
          this.$message.error(this.$t('write.input_then_add'));
          return;
        }
      }
      if (this.vote.options.length < 20) {
        this.vote.options.push('');
      } else {
        this.$message.info(this.$t('write.vote_max_20'));
      }
    },

    // 删除投票选项
    delVoteItem(index) {
      if (this.vote.options.length > 2) {
        this.vote.options = this.vote.options.filter((i, k) => k !== index);
      } else {
        this.$message.info(this.$t('write.vote_min_2'));
      }
    },

    // 附加功能全部方法
    changeOne() {
      this.add.only_passer = !this.add.only_passer;
    },
    // changeTwo() {
    //   this.add.expiration2 = !this.add.expiration2;
    // },
    changeThree() {
      this.add.hasPay = !this.add.hasPay;
    },
    changeFour() {
      // this.$message.warning('暂未启用');
      this.add.hasReward = !this.add.hasReward;
    },
    // 检查数据
    checkData() {
      const { add } = this;
      if (add.hasPay) {
        // 如果需要付费就判断, 不需要就不判断
        if (!add.need_pay.node) {
          this.$message.error(this.$t('write.coin_node'));
          return false;
        }
        if (!add.need_pay.coin) {
          this.$message.error(this.$t('write.coin_price'));
          return false;
        }
        add.need_pay.node = Number(add.need_pay.node);
        add.need_pay.coin = Number(add.need_pay.coin);
      }
      if (add.hasReward) {
        // 如果有回复奖励就判断，没有就不需要判断
        if (!add.reward.coin || !add.reward.stock || !add.reward.num || !add.reward.odds) {
          this.$message.error(this.$t('guildhall.input_all_data'));
          return false;
        }
        add.reward.coin = Number(add.reward.coin);
        add.reward.stock = Number(add.reward.stock);
        add.reward.num = Number(add.reward.num);
        add.reward.odds = Number(add.reward.odds);
        // 检查当前轻币
        if (this.loginUser && this.loginUser.balance && add.reward.coin * add.reward.stock > this.loginUser.balance.coin) {
          this.$message.error(this.$t('guildhall.coin_no'));
          return false;
        }
        // add.reward.end_time = '';
      }
      return true;
    },
    // pay_info校验
    check_node() {
      const { node } = this.add.need_pay;
      if (node % 10 !== 0 || node < 0 || node > 90) {
        this.$message.warning(`${this.$t('write.input_100_integer')}`);
        this.add.need_pay.node = '';
      }
    },
    // reward校验
    check_reward(type) {
      if (type === 0) {
        const { coin } = this.add.reward;
        if (coin > 1000 || !/(^[1-9]\d*$)/.test(coin)) {
          this.$message.warning(`${this.$t('guildhall.input_1000_integer')}`);
          this.add.reward.coin = '';
        }
      } else if (type === 1) {
        const { stock } = this.add.reward;
        if (stock > 1000 || !/(^[1-9]\d*$)/.test(stock)) {
          this.$message.warning(`${this.$t('guildhall.input_1000_integer')}`);
          this.add.reward.stock = '';
        }
      } else if (type === 2) {
        const { num } = this.add.reward;
        if (num > 100 || !/(^[1-9]\d*$)/.test(num)) {
          this.$message.warning(`${this.$t('guildhall.input_100_integer')}`);
          this.add.reward.num = '';
        }
      } else {
        const { odds } = this.add.reward;
        if (odds > 100 || !/(^[1-9]\d*$)/.test(odds)) {
          this.$message.warning(`${this.$t('guildhall.input_100_integer')}`);
          this.add.reward.odds = '';
        }
      }
    },
    confirmAdd() {
      const { add } = this;
      if (this.checkData()) {
        this.showAddDialog = false;
        this.$emit('additional', add);
        this.$message.success('保存成功');
      }
    }
  }
};
</script>

<style lang="scss" scope>
.comment-input-wrapper {
  width: 100%;
  // 输入评论框容器
  .comment-content-input-box {
    width: 100%;
    height: 100px;
    position: relative;
    .login-tips {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      background: $gray-f7;
    }
    .input-box {
      position: relative;
      width: 100%;
      height: 100%;
      .comment-content-input {
        width: 100%;
        height: 100%;
        border: 1px solid $gray-white;
        resize: none;
        box-sizing: border-box;
        padding: 10px;
        border-radius: 5px;
        background: #fafafa;
      }
      .text-counter {
        position: absolute;
        bottom: 5px;
        right: 5px;
        text-align: center;
      }
    }
  }
  // 输入评论按钮
  .comment-input-btns {
    // padding-top: 30px;
    // padding-bottom: 20px;
    position: relative;
    .btn {
      width: 70px;
      height: 30px;
      line-height: 30px;
      border-radius: 30px;
      margin-right: 20px;
      &.publish-btn {
        margin: 0;
        float: right;
      }
      @mixin preview-box {
        z-index: 10;
        position: absolute;
        box-sizing: border-box;
        background-color: $white;
        border: 1px solid $gray-white;
        box-shadow: 0px 0px 15px 0px rgba(1, 1, 1, 0.1);
        border: 1px solid $gray-white;
        border-radius: 5px;
        padding: 30px 15px 15px 15px;
      }
      .close-preview-btn {
        position: absolute;
        fill: $gray;
        width: 12px;
        height: 12px;
        top: 10px;
        right: 10px;
      }
      &.img-btn {
        position: relative;
        .image-preview {
          @include preview-box;
          top: 150%;
          left: 50%;
          transform: translateX(-50%);
          width: 135px;
          max-width: 135px;
          // padding: 30px 15px 15px 15px;
          img {
            width: 100%;
            vertical-align: top;
          }
        }
      }
      &.emoji-btn {
        position: relative;
        .comment-input-emoji {
          @include preview-box;

          top: 150%;
          left: 30%;
          width: 400px;
          height: 310px;
          &::after {
            left: 15px;
          }
        }
      }
    }
    .btn2 {
      width: 20px;
      height: 20px;
      margin-left: 16px;
      margin-right: 16px;
    }
    .btn3 {
      width: 20px;
      height: 20px;
      margin-right: 16px;
    }
    .btn4 {
      width: 85px;
      height: 12px;
      margin-right: 16px;
      vertical-align: text-top;
    }
  }
  .votes-dialog {
    .el-dialog__body {
      padding: 30px 20px 0;
    }
    .btn-publish {
      width: 130px;
      height: 35px;
      border-radius: 35px;
      text-align: center;
      margin: 0 auto;
    }
    .item {
      position: relative;
      .contents-files {
        padding-bottom: 20px;
      }
      padding-bottom: 30px;
      width: 100%;
      box-sizing: border-box;
      .collection-intro {
        height: 60px;
        padding: 10px 15px;
        resize: none;
      }
      .text-counter {
        min-width: 20px;
        font-size: 13px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        &.btn-del {
          .svg-icon {
            height: 13px;
            fill: $gray;
          }
        }
      }
      .require {
        color: #f00;
      }
      .article-title {
        max-width: 500px;
      }
      .sub-title {
        margin-bottom: 15px;
        .new-collection {
          background: none;
        }
      }
      .add-title {
        width: 40%;
        text-align: end;
      }
      .mar-right-20 {
        margin-right: 20px;
      }
      .mar-rt {
        margin-right: 80px;
        margin-bottom: 19px;
      }
      .select {
        width: 250px;
        height: 30px;
        border: 1px solid $gray-white;
        padding: 0 15px;
        border-radius: 5px;
        color: $dark;
        appearance: none;
        background-color: #fff;
        background-image: url(../assets/images/down_arrow.svg);
        background-repeat: no-repeat;
        background-size: 12px auto;
        background-position: 95% 55%;
        &.padrig-30 {
          padding-right: 30px !important;
        }
      }
      .select-banner {
        width: 160px;
        height: 100px;
        background: rgba(250, 250, 250, 1);
        border: 1px solid rgba(238, 238, 238, 1);
        border-radius: 5px;
        background-size: 40px 40px;
        overflow: hidden;
        .svg-icon {
          width: 30px;
          fill: #b2b2b2;
        }
        &.vertical {
          width: 160px;
          height: 225px;
        }
        fill: #ccc;
        .cover-img {
          width: 100%;
          object-fit: cover;
        }
      }
    }
    .item1 {
      position: relative;
      .contents-files {
        padding-bottom: 20px;
      }
      padding-bottom: 4px;
      width: 100%;
      box-sizing: border-box;
      .collection-intro {
        height: 60px;
        padding: 10px 15px;
        resize: none;
      }
      .text-counter {
        min-width: 20px;
        font-size: 13px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        &.btn-del {
          .svg-icon {
            height: 13px;
            fill: $gray;
          }
        }
      }
      .require {
        color: #f00;
      }
      .article-title {
        max-width: 500px;
      }
      .sub-title {
        margin-bottom: 15px;
        .new-collection {
          background: none;
        }
      }
      .add-box {
        text-align: end;
        width: 77%;
        display: flex;
        margin-left: 11px;
      }
      .add-title {
        width: 40%;
      }
      .input-control {
        width: 100%;
      }
      .mar-right-20 {
        margin-right: 20px;
      }
      .mar-right-80 {
        margin-right: 80px;
      }
      .mar-rt {
        margin-right: 80px;
        margin-bottom: 19px;
      }
      .select {
        width: 250px;
        height: 30px;
        border: 1px solid $gray-white;
        padding: 0 15px;
        border-radius: 5px;
        color: $dark;
        appearance: none;
        background-color: #fff;
        background-image: url(../assets/images/down_arrow.svg);
        background-repeat: no-repeat;
        background-size: 12px auto;
        background-position: 95% 55%;
        &.padrig-30 {
          padding-right: 30px !important;
        }
      }
      .select-banner {
        width: 160px;
        height: 100px;
        background: rgba(250, 250, 250, 1);
        border: 1px solid rgba(238, 238, 238, 1);
        border-radius: 5px;
        background-size: 40px 40px;
        overflow: hidden;
        .svg-icon {
          width: 30px;
          fill: #b2b2b2;
        }
        &.vertical {
          width: 160px;
          height: 225px;
        }
        fill: #ccc;
        .cover-img {
          width: 100%;
          object-fit: cover;
        }
      }
    }
    .extra-options,
    .checkboxs {
      .radio-btn {
        width: 15px;
        height: 15px;
        background: #dfdfdf;
        margin-right: 15px;
        border-radius: 50%;
        cursor: pointer;
        &.checked {
          background: $primary;
        }
        &.checked2 {
          background: $primary;
        }
      }
      .input-box {
        margin-bottom: 10px;
        margin-left: 30px;
        align-items: baseline;
        justify-content: space-between;
      }
      .payinfo {
        padding: 15px 0;
        .left {
          width: 140px;
          text-align: right;
        }
        .right {
          padding-left: 15px;
          input {
            width: 250px;
          }
        }
      }
    }
  }
}
.img-box {
  width: 20px;
  height: 20px;
}
.img-box1 {
  width: 12px;
  height: 12px;
  margin-right: 6px;
}
.img-text {
  height: 15px;
  line-height: 19px;
  font-size: 15px;
  color: #3ad8d9;
}
</style>

<template>
  <!-- -----| 粉丝/关注页|----- -->
  <div class="fans-page flex jc-between">
    <!-- 侧边栏 -->
    <ProfileSide
      :is-me="isMe"
      :uid="user.uid"
      :level="user.level.level"
      :level-name="user.level.name"
      :username="user.nickname"
      :avatar="user.avatar"
      :fans="user.followers"
      :follows="user.following"
      :articles="user.articles"
      :btn-text="btnText"
      @btn-click="clickHandler"
    />

    <!-- 右边: 右边信息 -->
    <div v-infinite-scroll="loadMore" class="scroll-container">
      <h3 class="fs-ml">{{ title }}</h3>

      <!-- 列表 -->
      <div v-for="item in users" :key="item.uid" class="fans-item">
        <FansItem
          :followed="item.followed"
          :avatar="item.avatar"
          :nickname="item.nickname"
          :sign="item.sign"
          :is-me="isMe"
          @follow-click="toggleFollow($event, item.uid)"
        />
      </div>

      <!-- 加载中 -->
      <Loading v-show="loading" style="max-height: 100px; padding-top: 30px;" />
      <!-- 没有更多 -->
      <p v-if="noMore" class="no-more">{{ this.$t('fans.no_more') }} ╮(╯▽╰)╭</p>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import ProfileSide from '@/components/ProfileSide.vue';
import FansItem from '@/components/FansItem.vue';
import Loading from '@/components/Loading.vue';

export default {
  layout: 'profile',
  components: {
    ProfileSide,
    FansItem,
    Loading
  },

  async fetch({ params, store }) {
    const uid = Number(params.id);
    await store.dispatch('profile/getUserinfo', { uid });
  },

  async asyncData({ params, query, $axios }) {
    const uid = Number(params.id);
    const data = {
      uid,
      isFansMode: true,
      url: '/api/user/get-followers'
    };

    if (query.type === 'stars') {
      data.url = '/api/user/get-following';
      data.isFansMode = false;
    }

    const res = await $axios.$post(data.url, { uid });

    if (res.code === 0) {
      data.users = res.data;
    }

    return data;
  },

  data: () => ({
    loading: false,
    url: '',
    noMore: false,
    users: [],
    page: 1,
    isFansMode: false,
    uid: 0
  }),

  computed: {
    ...mapGetters('login', ['isLogin', 'loginUser']),
    ...mapState('profile', ['user']),
    isMe() {
      if (!this.isLogin) {
        return false;
      }
      return this.uid === this.loginUser.uid;
    },
    btnText() {
      // 已经关注显示: "取消关注" 未关注显示: "关注"
      if (this.user.followed) {
        return this.$t('components.btn_unfollow');
      } else {
        return this.$t('components.btn_follow');
      }
    },
    title() {
      const user = this.isMe ? this.$t('fans.mine') : this.$t('fans.his');
      const type = this.isFansMode ? this.$t('fans.fans') : this.$t('fans.stars');
      return `${user}${type}`;
    }
  },

  methods: {
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore) return;
      this.page += 1;
      this.loading = true;
      this.$axios
        .$post(this.url, {
          uid: this.uid,
          page: this.page
        })
        .then((res) => {
          this.loading = false;
          if (res.code === 0) {
            this.users = this.users.concat(res.data);
            if (res.data.length === 0) {
              --this.page;
              this.noMore = true;
            }
          }
        });
    },

    // 切换关注状态
    toggleFollow(act, uid) {
      this.toggleFollowState(uid, act, (successed) => {
        if (!successed) return;
        this.users = this.users.filter((item) => item.uid !== uid);
      });
    },

    // 侧边栏按钮点击(如果是自己: 修改资料, 不是: 关注)
    clickHandler() {
      if (this.isMe) {
        this.toPage(`/settings/${this.loginUser.uid}`);
        return;
      }
      const act = this.user.followed ? 1 : 0;
      this.toggleFollowState(this.user.uid, act, (res) => {
        res &&
          this.$store.dispatch('profile/setUserByKey', {
            key: 'followed',
            val: !this.user.followed
          });
      });
    }
  },

  head() {
    const title = this.title + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
.fans-page {
  margin-top: 30px;
  .scroll-container {
    width: 910px;
    height: 800px;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 30px 15px;
    background: #fff;
    .fans-item {
      padding: 25px 0;
      border-bottom: 1px solid $gray-white;
    }
    .no-more {
      padding-top: 30px;
      line-height: 30px;
      text-align: center;
      color: $gray;
    }
  }
}
</style>

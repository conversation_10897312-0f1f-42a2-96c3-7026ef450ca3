/* SCEditor v2.1.3 | (C) 2017, <PERSON> | sceditor.com/license */

!function(){"use strict";function e(e,t){return typeof t===e}var be=e.bind(null,"string"),xe=e.bind(null,"undefined"),we=e.bind(null,"function"),o=e.bind(null,"number");function t(e){return!Object.keys(e).length}function Ce(e,t){for(var n=e===!!e,o=n?2:1,r=n?t:e,i=!!n&&e;o<arguments.length;o++){var a=arguments[o];for(var l in a){var c=a[l];if(!xe(c)){var s=null!==c&&"object"==typeof c&&Object.getPrototypeOf(c)===Object.prototype,u=Array.isArray(c);r[l]=i&&(s||u)?Ce(!0,r[l]||(u?[]:{}),c):c}}}return r}function ke(e,t){var n=e.indexOf(t);-1<n&&e.splice(n,1)}function Se(t,n){if(Array.isArray(t)||"length"in t&&o(t.length))for(var e=0;e<t.length;e++)n(e,t[e]);else Object.keys(t).forEach(function(e){n(e,t[e])})}var i={},Ee=1,Te=3;function a(e){return e=parseFloat(e),isFinite(e)?e:0}function De(e,t,n){var o=(n||document).createElement(e);return Se(t||{},function(e,t){"style"===e?o.style.cssText=t:e in o?o[e]=t:o.setAttribute(e,t)}),o}function Me(e,t){for(var n=e||{};(n=n.parentNode)&&!/(9|11)/.test(n.nodeType);)if(!t||We(n,t))return n}function Ne(e,t){return We(e,t)?e:Me(e,t)}function Re(e){e.parentNode&&e.parentNode.removeChild(e)}function Fe(e,t){e.appendChild(t)}function He(e,t){return e.querySelectorAll(t)}var _e=!0;function ze(n,e,o,r,i){e.split(" ").forEach(function(e){var t;be(o)?(t=r["_sce-event-"+e+o]||function(e){for(var t=e.target;t&&t!==n;){if(We(t,o))return void r.call(t,e);t=t.parentNode}},r["_sce-event-"+e+o]=t):(t=o,i=r),n.addEventListener(e,t,i||!1)})}function Oe(n,e,o,r,i){e.split(" ").forEach(function(e){var t;be(o)?t=r["_sce-event-"+e+o]:(t=o,i=r),n.removeEventListener(e,t,i||!1)})}function Ae(e,t,n){if(arguments.length<3)return e.getAttribute(t);null==n?r(e,t):e.setAttribute(t,n)}function r(e,t){e.removeAttribute(t)}function Be(e){Pe(e,"display","none")}function Ie(e){Pe(e,"display","")}function Le(e){Je(e)?Be(e):Ie(e)}function Pe(n,e,t){if(arguments.length<3){if(be(e))return 1===n.nodeType?getComputedStyle(n)[e]:null;Se(e,function(e,t){Pe(n,e,t)})}else{var o=(t||0===t)&&!isNaN(t);n.style[e]=o?t+"px":t}}function Ve(e,t,n){var o=arguments.length,r={};if(e.nodeType===Ee){if(1===o)return Se(e.attributes,function(e,t){/^data\-/i.test(t.name)&&(r[t.name.substr(5)]=t.value)}),r;if(2===o)return Ae(e,"data-"+t);Ae(e,"data-"+t,String(n))}}function We(e,t){var n=!1;return e&&e.nodeType===Ee&&(n=(e.matches||e.msMatchesSelector||e.webkitMatchesSelector).call(e,t)),n}function je(e,t){return t.parentNode.insertBefore(e,t)}function l(e){return e.className.trim().split(/\s+/)}function qe(e,t){return We(e,"."+t)}function Ue(e,t){var n=l(e);n.indexOf(t)<0&&n.push(t),e.className=n.join(" ")}function $e(e,t){var n=l(e);ke(n,t),e.className=n.join(" ")}function Ye(e,t,n){(n=xe(n)?!qe(e,t):n)?Ue(e,t):$e(e,t)}function Ke(e,t){if(xe(t)){var n=getComputedStyle(e),o=a(n.paddingLeft)+a(n.paddingRight),r=a(n.borderLeftWidth)+a(n.borderRightWidth);return e.offsetWidth-o-r}Pe(e,"width",t)}function Xe(e,t){if(xe(t)){var n=getComputedStyle(e),o=a(n.paddingTop)+a(n.paddingBottom),r=a(n.borderTopWidth)+a(n.borderBottomWidth);return e.offsetHeight-o-r}Pe(e,"height",t)}function Ge(e,t,n){var o;we(window.CustomEvent)?o=new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}):(o=e.ownerDocument.createEvent("CustomEvent")).initCustomEvent(t,!0,!0,n),e.dispatchEvent(o)}function Je(e){return!!e.getClientRects().length}function Qe(e,t,n,o,r){for(e=r?e.lastChild:e.firstChild;e;){var i=r?e.previousSibling:e.nextSibling;if(!n&&!1===t(e)||!o&&!1===Qe(e,t,n,o,r)||n&&!1===t(e))return!1;e=i}}function Ze(e,t,n,o){Qe(e,t,n,o,!0)}function et(e,t){var n=(t=t||document).createDocumentFragment(),o=De("div",{},t);for(o.innerHTML=e;o.firstChild;)Fe(n,o.firstChild);return n}function tt(e){return e&&(!We(e,"p,div")||e.className||Ae(e,"style")||!t(Ve(e)))}function nt(e,t){var n=De(t,{},e.ownerDocument);for(Se(e.attributes,function(e,t){try{Ae(n,t.name,t.value)}catch(e){}});e.firstChild;)Fe(n,e.firstChild);return e.parentNode.replaceChild(n,e),n}var c="|body|hr|p|div|h1|h2|h3|h4|h5|h6|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|blockquote|center|";function ot(e){return!!/11?|9/.test(e.nodeType)&&"|iframe|area|base|basefont|br|col|frame|hr|img|input|wbr|isindex|link|meta|param|command|embed|keygen|source|track|object|".indexOf("|"+e.nodeName.toLowerCase()+"|")<0}function rt(e,t){var n,o=(e||{}).nodeType||Te;return o!==Ee?o===Te:"code"===(n=e.tagName.toLowerCase())?!t:c.indexOf("|"+n+"|")<0}function s(e,t){t.style.cssText=e.style.cssText+t.style.cssText}function it(e){Qe(e,function(e){var t,n,o=!rt(e,!0);if(o&&rt(e.parentNode,!0)){var r=function(e){for(;rt(e.parentNode,!0);)e=e.parentNode;return e}(e),i=u(r,e),a=e;s(r,a),je(i,r),je(a,r)}if(o&&We(e,"ul,ol")&&We(e.parentNode,"ul,ol")){var l=(t="li",n=e.previousElementSibling,t&&n?We(n,t)?n:null:n);l||je(l=De("li"),e),Fe(l,e)}})}function d(e,t){return e?(t?e.previousSibling:e.nextSibling)||d(e.parentNode,t):null}function at(e){var t,n,o,r,i,a,l,c=Pe(e,"whiteSpace"),s=/line$/i.test(c),u=e.firstChild;if(!/pre(\-wrap)?$/i.test(c))for(;u;){if(a=u.nextSibling,t=u.nodeValue,(n=u.nodeType)===Ee&&u.firstChild&&at(u),n===Te){for(o=d(u),r=d(u,!0),l=!1;qe(r,"sceditor-ignore");)r=d(r,!0);if(rt(u)&&r){for(i=r;i.lastChild;)for(i=i.lastChild;qe(i,"sceditor-ignore");)i=d(i,!0);l=i.nodeType===Te?/[\t\n\r ]$/.test(i.nodeValue):!rt(i)}t=t.replace(/\u200B/g,""),r&&rt(r)&&!l||(t=t.replace(s?/^[\t ]+/:/^[\t\n\r ]+/,"")),o&&rt(o)||(t=t.replace(s?/[\t ]+$/:/[\t\n\r ]+$/,"")),t.length?u.nodeValue=t.replace(s?/[\t ]+/g:/[\t\n\r ]+/g," "):Re(u)}u=a}}function u(e,t){var n=e.ownerDocument.createRange();return n.setStartBefore(e),n.setEndAfter(t),n.extractContents()}function lt(e){for(var t=0,n=0;e;)t+=e.offsetLeft,n+=e.offsetTop,e=e.offsetParent;return{left:t,top:n}}function f(e,t){var n,o,r=e.style;if(i[t]||(i[t]=t.replace(/^-ms-/,"ms-").replace(/-(\w)/g,function(e,t){return t.toUpperCase()})),o=r[t=i[t]],"textAlign"===t){if(n=r.direction,o=o||Pe(e,t),Pe(e.parentNode,t)===o||"block"!==Pe(e,"display")||We(e,"hr,th"))return"";if(/right/i.test(o)&&"rtl"===n||/left/i.test(o)&&"ltr"===n)return""}return o}var n,p,m,ct={toolbar:"bold,italic,underline,strike,subscript,superscript|left,center,right,justify|font,size,color,removeformat|cut,copy,pastetext|bulletlist,orderedlist,indent,outdent|table|code,quote|horizontalrule,image,email,link,unlink|emoticon,youtube,date,time|ltr,rtl|print,maximize,source",toolbarExclude:null,style:"jquery.sceditor.default.css",fonts:"Arial,Arial Black,Comic Sans MS,Courier New,Georgia,Impact,Sans-serif,Serif,Times New Roman,Trebuchet MS,Verdana",colors:"#000000,#44B8FF,#1E92F7,#0074D9,#005DC2,#00369B,#b3d5f4|#444444,#C3FFFF,#9DF9FF,#7FDBFF,#68C4E8,#419DC1,#d9f4ff|#666666,#72FF84,#4CEA5E,#2ECC40,#17B529,#008E02,#c0f0c6|#888888,#FFFF44,#FFFA1E,#FFDC00,#E8C500,#C19E00,#fff5b3|#aaaaaa,#FFC95F,#FFA339,#FF851B,#E86E04,#C14700,#ffdbbb|#cccccc,#FF857A,#FF5F54,#FF4136,#E82A1F,#C10300,#ffc6c3|#eeeeee,#FF56FF,#FF30DC,#F012BE,#D900A7,#B20080,#fbb8ec|#ffffff,#F551FF,#CF2BE7,#B10DC9,#9A00B2,#9A00B2,#e8b6ef",locale:Ae(document.documentElement,"lang")||"en",charset:"utf-8",emoticonsCompat:!1,emoticonsEnabled:!0,emoticonsRoot:"",emoticons:{dropdown:{":)":"emoticons/smile.png",":angel:":"emoticons/angel.png",":angry:":"emoticons/angry.png","8-)":"emoticons/cool.png",":'(":"emoticons/cwy.png",":ermm:":"emoticons/ermm.png",":D":"emoticons/grin.png","<3":"emoticons/heart.png",":(":"emoticons/sad.png",":O":"emoticons/shocked.png",":P":"emoticons/tongue.png",";)":"emoticons/wink.png"},more:{":alien:":"emoticons/alien.png",":blink:":"emoticons/blink.png",":blush:":"emoticons/blush.png",":cheerful:":"emoticons/cheerful.png",":devil:":"emoticons/devil.png",":dizzy:":"emoticons/dizzy.png",":getlost:":"emoticons/getlost.png",":happy:":"emoticons/happy.png",":kissing:":"emoticons/kissing.png",":ninja:":"emoticons/ninja.png",":pinch:":"emoticons/pinch.png",":pouty:":"emoticons/pouty.png",":sick:":"emoticons/sick.png",":sideways:":"emoticons/sideways.png",":silly:":"emoticons/silly.png",":sleeping:":"emoticons/sleeping.png",":unsure:":"emoticons/unsure.png",":woot:":"emoticons/w00t.png",":wassat:":"emoticons/wassat.png"},hidden:{":whistling:":"emoticons/whistling.png",":love:":"emoticons/wub.png"}},width:null,height:null,resizeEnabled:!0,resizeMinWidth:null,resizeMinHeight:null,resizeMaxHeight:null,resizeMaxWidth:null,resizeHeight:!0,resizeWidth:!0,dateFormat:"year-month-day",toolbarContainer:null,enablePasteFiltering:!1,disablePasting:!1,readOnly:!1,rtl:!1,autofocus:!1,autofocusEnd:!0,autoExpand:!1,autoUpdate:!1,spellcheck:!0,runWithoutWysiwygSupport:!1,startInSourceMode:!1,id:null,plugins:"",zIndex:null,bbcodeTrim:!1,disableBlockRemove:!1,parserOptions:{},dropDownCss:{}},g=navigator.userAgent,h=function(){for(var e=3,t=document,n=t.createElement("div"),o=n.getElementsByTagName("i");n.innerHTML="\x3c!--[if gt IE "+ ++e+"]><i></i><![endif]--\x3e",o[0];);return t.documentMode&&t.all&&window.atob&&(e=10),4===e&&t.documentMode&&(e=11),4<e?e:void 0}(),st="-ms-ime-align"in document.documentElement.style,ut=/iPhone|iPod|iPad| wosbrowser\//i.test(g),dt=((m=document.createElement("div")).contentEditable=!0,"contentEditable"in document.documentElement&&"true"===m.contentEditable&&(p=/Opera Mobi|Opera Mini/i.test(g),/Android/i.test(g)&&(p=!0,/Safari/.test(g)&&(p=!(n=/Safari\/(\d+)/.exec(g))||!n[1]||n[1]<534)),/ Silk\//i.test(g)&&(p=!(n=/AppleWebKit\/(\d+)/.exec(g))||!n[1]||n[1]<534),ut&&(p=/OS [0-4](_\d)+ like Mac/i.test(g)),/Firefox/i.test(g)&&(p=!1),/OneBrowser/i.test(g)&&(p=!1),"UCWEB"===navigator.vendor&&(p=!1),h<=9&&(p=!0),!p)),v=/^(https?|s?ftp|mailto|spotify|skype|ssh|teamspeak|tel):|(\/\/)|data:image\/(png|bmp|gif|p?jpe?g);/i;function ft(e){return e.replace(/([\-.*+?^=!:${}()|\[\]\/\\])/g,"\\$1")}function pt(e,t){if(!e)return e;var n={"&":"&amp;","<":"&lt;",">":"&gt;","  ":"&nbsp; ","\r\n":"<br />","\r":"<br />","\n":"<br />"};return!1!==t&&(n['"']="&#34;",n["'"]="&#39;",n["`"]="&#96;"),e=e.replace(/ {2}|\r\n|[&<>\r\n'"`]/g,function(e){return n[e]||e})}var y={html:'<!DOCTYPE html><html{attrs}><head><style>.ie * {min-height: auto !important} .ie table td {height:15px} @supports (-ms-ime-align:auto) { * { min-height: auto !important; } }</style><meta http-equiv="Content-Type" content="text/html;charset={charset}" /><link rel="stylesheet" type="text/css" href="{style}" /></head><body contenteditable="true" {spellcheck}><p></p></body></html>',toolbarButton:'<a class="sceditor-button sceditor-button-{name}" data-sceditor-command="{name}" unselectable="on"><div unselectable="on">{dispName}</div></a>',emoticon:'<img src="{url}" data-sceditor-emoticon="{key}" alt="{key}" title="{tooltip}" />',fontOpt:'<a class="sceditor-font-option" href="#" data-font="{font}"><font face="{font}">{font}</font></a>',sizeOpt:'<a class="sceditor-fontsize-option" data-size="{size}" href="#"><font size="{size}">{size}</font></a>',pastetext:'<div><label for="txt">{label}</label> <textarea cols="20" rows="7" id="txt"></textarea></div><div><input type="button" class="button" value="{insert}" /></div>',table:'<div><label for="rows">{rows}</label><input type="text" id="rows" value="2" /></div><div><label for="cols">{cols}</label><input type="text" id="cols" value="2" /></div><div><input type="button" class="button" value="{insert}" /></div>',image:'<div><label for="link">{url}</label> <input type="text" id="image" dir="ltr" placeholder="https://" /></div><div><label for="width">{width}</label> <input type="text" id="width" size="2" dir="ltr" /></div><div><label for="height">{height}</label> <input type="text" id="height" size="2" dir="ltr" /></div><div><input type="button" class="button" value="{insert}" /></div>',email:'<div><label for="email">{label}</label> <input type="text" id="email" dir="ltr" /></div><div><label for="des">{desc}</label> <input type="text" id="des" /></div><div><input type="button" class="button" value="{insert}" /></div>',link:'<div><label for="link">{url}</label> <input type="text" id="link" dir="ltr" placeholder="https://" /></div><div><label for="des">{desc}</label> <input type="text" id="des" /></div><div><input type="button" class="button" value="{ins}" /></div>',youtubeMenu:'<div><label for="link">{label}</label> <input type="text" id="link" dir="ltr" placeholder="https://" /></div><div><input type="button" class="button" value="{insert}" /></div>',youtube:'<iframe width="560" height="315" frameborder="0" allowfullscreen src="https://www.youtube.com/embed/{id}?wmode=opaque&start={time}" data-youtube-id="{id}"></iframe>'};function mt(e,t,n){var o=y[e];return Object.keys(t).forEach(function(e){o=o.replace(new RegExp(ft("{"+e+"}"),"g"),t[e])}),n&&(o=et(o)),o}var b=h&&h<11;function x(e){if("mozHidden"in document)for(var t,n=e.getBody();n;){if((t=n).firstChild)t=t.firstChild;else{for(;t&&!t.nextSibling;)t=t.parentNode;t&&(t=t.nextSibling)}3===n.nodeType&&/[\n\r\t]+/.test(n.nodeValue)&&(/^pre/.test(Pe(n.parentNode,"whiteSpace"))||Re(n)),n=t}}var gt={bold:{exec:"bold",tooltip:"Bold",shortcut:"Ctrl+B"},italic:{exec:"italic",tooltip:"Italic",shortcut:"Ctrl+I"},underline:{exec:"underline",tooltip:"Underline",shortcut:"Ctrl+U"},strike:{exec:"strikethrough",tooltip:"Strikethrough"},subscript:{exec:"subscript",tooltip:"Subscript"},superscript:{exec:"superscript",tooltip:"Superscript"},left:{state:function(e){if(e&&3===e.nodeType&&(e=e.parentNode),e){var t="ltr"===Pe(e,"direction"),n=Pe(e,"textAlign");return"left"===n||n===(t?"start":"end")}},exec:"justifyleft",tooltip:"Align left"},center:{exec:"justifycenter",tooltip:"Center"},right:{state:function(e){if(e&&3===e.nodeType&&(e=e.parentNode),e){var t="ltr"===Pe(e,"direction"),n=Pe(e,"textAlign");return"right"===n||n===(t?"end":"start")}},exec:"justifyright",tooltip:"Align right"},justify:{exec:"justifyfull",tooltip:"Justify"},font:{_dropDown:function(t,e,n){var o=De("div");ze(o,"click","a",function(e){n(Ve(this,"font")),t.closeDropDown(!0),e.preventDefault()}),t.opts.fonts.split(",").forEach(function(e){Fe(o,mt("fontOpt",{font:e},!0))}),t.createDropDown(e,"font-picker",o)},exec:function(e){var t=this;gt.font._dropDown(t,e,function(e){t.execCommand("fontname",e)})},tooltip:"Font Name"},size:{_dropDown:function(t,e,n){var o=De("div");ze(o,"click","a",function(e){n(Ve(this,"size")),t.closeDropDown(!0),e.preventDefault()});for(var r=1;r<=7;r++)Fe(o,mt("sizeOpt",{size:r},!0));t.createDropDown(e,"fontsize-picker",o)},exec:function(e){var t=this;gt.size._dropDown(t,e,function(e){t.execCommand("fontsize",e)})},tooltip:"Font Size"},color:{_dropDown:function(t,e,n){var o=De("div"),r="",i=gt.color;i._htmlCache||(t.opts.colors.split("|").forEach(function(e){r+='<div class="sceditor-color-column">',e.split(",").forEach(function(e){r+='<a href="#" class="sceditor-color-option" style="background-color: '+e+'" data-color="'+e+'"></a>'}),r+="</div>"}),i._htmlCache=r),Fe(o,et(i._htmlCache)),ze(o,"click","a",function(e){n(Ve(this,"color")),t.closeDropDown(!0),e.preventDefault()}),t.createDropDown(e,"color-picker",o)},exec:function(e){var t=this;gt.color._dropDown(t,e,function(e){t.execCommand("forecolor",e)})},tooltip:"Font Color"},removeformat:{exec:"removeformat",tooltip:"Remove Formatting"},cut:{exec:"cut",tooltip:"Cut",errorMessage:"Your browser does not allow the cut command. Please use the keyboard shortcut Ctrl/Cmd-X"},copy:{exec:"copy",tooltip:"Copy",errorMessage:"Your browser does not allow the copy command. Please use the keyboard shortcut Ctrl/Cmd-C"},paste:{exec:"paste",tooltip:"Paste",errorMessage:"Your browser does not allow the paste command. Please use the keyboard shortcut Ctrl/Cmd-V"},pastetext:{exec:function(e){var t,n=De("div"),o=this;Fe(n,mt("pastetext",{label:o._("Paste your text inside the following box:"),insert:o._("Insert")},!0)),ze(n,"click",".button",function(e){(t=He(n,"#txt")[0].value)&&o.wysiwygEditorInsertText(t),o.closeDropDown(!0),e.preventDefault()}),o.createDropDown(e,"pastetext",n)},tooltip:"Paste Text"},bulletlist:{exec:function(){x(this),this.execCommand("insertunorderedlist")},tooltip:"Bullet list"},orderedlist:{exec:function(){x(this),this.execCommand("insertorderedlist")},tooltip:"Numbered list"},indent:{state:function(e,t){var n,o,r;return We(t,"li")?0:We(t,"ul,ol,menu")&&(o=(n=this.getRangeHelper().selectedRange()).startContainer.parentNode,r=n.endContainer.parentNode,o!==o.parentNode.firstElementChild||We(r,"li")&&r!==r.parentNode.lastElementChild)?0:-1},exec:function(){var e=this.getRangeHelper().getFirstBlockParent();this.focus(),Ne(e,"ul,ol,menu")&&this.execCommand("indent")},tooltip:"Add indent"},outdent:{state:function(e,t){return Ne(t,"ul,ol,menu")?0:-1},exec:function(){Ne(this.getRangeHelper().getFirstBlockParent(),"ul,ol,menu")&&this.execCommand("outdent")},tooltip:"Remove one indent"},table:{exec:function(e){var r=this,i=De("div");Fe(i,mt("table",{rows:r._("Rows:"),cols:r._("Cols:"),insert:r._("Insert")},!0)),ze(i,"click",".button",function(e){var t=Number(He(i,"#rows")[0].value),n=Number(He(i,"#cols")[0].value),o="<table>";0<t&&0<n&&(o+=Array(t+1).join("<tr>"+Array(n+1).join("<td>"+(b?"":"<br />")+"</td>")+"</tr>"),o+="</table>",r.wysiwygEditorInsertHtml(o),r.closeDropDown(!0),e.preventDefault())}),r.createDropDown(e,"inserttable",i)},tooltip:"Insert a table"},horizontalrule:{exec:"inserthorizontalrule",tooltip:"Insert a horizontal rule"},code:{exec:function(){this.wysiwygEditorInsertHtml("<code>",(b?"":"<br />")+"</code>")},tooltip:"Code"},image:{_dropDown:function(t,e,n,o){var r=De("div");Fe(r,mt("image",{url:t._("URL:"),width:t._("Width (optional):"),height:t._("Height (optional):"),insert:t._("Insert")},!0));var i=He(r,"#image")[0];i.value=n,ze(r,"click",".button",function(e){i.value&&o(i.value,He(r,"#width")[0].value,He(r,"#height")[0].value),t.closeDropDown(!0),e.preventDefault()}),t.createDropDown(e,"insertimage",r)},exec:function(e){var r=this;gt.image._dropDown(r,e,"",function(e,t,n){var o="";t&&(o+=' width="'+t+'"'),n&&(o+=' height="'+n+'"'),r.wysiwygEditorInsertHtml("<img"+o+' src="'+e+'" />')})},tooltip:"Insert an image"},email:{_dropDown:function(n,e,o){var r=De("div");Fe(r,mt("email",{label:n._("E-mail:"),desc:n._("Description (optional):"),insert:n._("Insert")},!0)),ze(r,"click",".button",function(e){var t=He(r,"#email")[0].value;t&&o(t,He(r,"#des")[0].value),n.closeDropDown(!0),e.preventDefault()}),n.createDropDown(e,"insertemail",r)},exec:function(e){var n=this;gt.email._dropDown(n,e,function(e,t){n.focus(),!n.getRangeHelper().selectedHtml()||t?n.wysiwygEditorInsertHtml('<a href="mailto:'+e+'">'+(t||e)+"</a>"):n.execCommand("createlink","mailto:"+e)})},tooltip:"Insert an email"},link:{_dropDown:function(t,e,n){var o=De("div");Fe(o,mt("link",{url:t._("URL:"),desc:t._("Description (optional):"),ins:t._("Insert")},!0));var r=He(o,"#link")[0];function i(e){r.value&&n(r.value,He(o,"#des")[0].value),t.closeDropDown(!0),e.preventDefault()}ze(o,"click",".button",i),ze(o,"keypress",function(e){13===e.which&&r.value&&i(e)},_e),t.createDropDown(e,"insertlink",o)},exec:function(e){var n=this;gt.link._dropDown(n,e,function(e,t){n.focus(),t||!n.getRangeHelper().selectedHtml()?(t=t||e,n.wysiwygEditorInsertHtml('<a href="'+e+'">'+t+"</a>")):n.execCommand("createlink",e)})},tooltip:"Insert a link"},unlink:{state:function(){return Ne(this.currentNode(),"a")?0:-1},exec:function(){var e=Ne(this.currentNode(),"a");if(e){for(;e.firstChild;)je(e.firstChild,e);Re(e)}},tooltip:"Unlink"},quote:{exec:function(e,t,n){var o="<blockquote>",r="</blockquote>";t?(o=o+(n=n?"<cite>"+n+"</cite>":"")+t+r,r=null):""===this.getRangeHelper().selectedHtml()&&(r=(b?"":"<br />")+r),this.wysiwygEditorInsertHtml(o,r)},tooltip:"Insert a Quote"},emoticon:{exec:function(f){var p=this,m=function(e){var t,n,o=p.opts,r=o.emoticonsRoot||"",i=o.emoticonsCompat,a=p.getRangeHelper(),l=i&&" "!==a.getOuterText(!0,1)?" ":"",c=i&&" "!==a.getOuterText(!1,1)?" ":"",s=De("div"),u=De("div"),d=Ce({},o.emoticons.dropdown,e?o.emoticons.more:{});return Fe(s,u),n=Math.sqrt(Object.keys(d).length),ze(s,"click","img",function(e){p.insert(l+Ae(this,"alt")+c,null,!1).closeDropDown(!0),e.preventDefault()}),Se(d,function(e,t){Fe(u,De("img",{src:r+(t.url||t),alt:e,title:t.tooltip||e})),u.children.length>=n&&(u=De("div"),Fe(s,u))}),!e&&o.emoticons.more&&(Fe(t=De("a",{className:"sceditor-more"}),document.createTextNode(p._("More"))),ze(t,"click",function(e){p.createDropDown(f,"more-emoticons",m(!0)),e.preventDefault()}),Fe(s,t)),s};p.createDropDown(f,"emoticons",m(!1))},txtExec:function(e){gt.emoticon.exec.call(this,e)},tooltip:"Insert an emoticon"},youtube:{_dropDown:function(i,e,a){var l=De("div");Fe(l,mt("youtubeMenu",{label:i._("Video URL:"),insert:i._("Insert")},!0)),ze(l,"click",".button",function(e){var t=He(l,"#link")[0].value,n=t.match(/(?:v=|v\/|embed\/|youtu.be\/)(.{11})/),o=t.match(/[&|?](?:star)?t=((\d+[hms]?){1,3})/),r=0;o&&Se(o[1].split(/[hms]/),function(e,t){""!==t&&(r=60*r+Number(t))}),n&&/^[a-zA-Z0-9_\-]{11}$/.test(n[1])&&a(n[1],r),i.closeDropDown(!0),e.preventDefault()}),i.createDropDown(e,"insertlink",l)},exec:function(e){var n=this;gt.youtube._dropDown(n,e,function(e,t){n.wysiwygEditorInsertHtml(mt("youtube",{id:e,time:t}))})},tooltip:"Insert a YouTube video"},date:{_date:function(e){var t=new Date,n=t.getYear(),o=t.getMonth()+1,r=t.getDate();return n<2e3&&(n=1900+n),o<10&&(o="0"+o),r<10&&(r="0"+r),e.opts.dateFormat.replace(/year/i,n).replace(/month/i,o).replace(/day/i,r)},exec:function(){this.insertText(gt.date._date(this))},txtExec:function(){this.insertText(gt.date._date(this))},tooltip:"Insert current date"},time:{_time:function(){var e=new Date,t=e.getHours(),n=e.getMinutes(),o=e.getSeconds();return t<10&&(t="0"+t),n<10&&(n="0"+n),o<10&&(o="0"+o),t+":"+n+":"+o},exec:function(){this.insertText(gt.time._time())},txtExec:function(){this.insertText(gt.time._time())},tooltip:"Insert current time"},ltr:{state:function(e,t){return t&&"ltr"===t.style.direction},exec:function(){var e=this.getRangeHelper(),t=e.getFirstBlockParent();(this.focus(),t&&!We(t,"body")||(this.execCommand("formatBlock","p"),(t=e.getFirstBlockParent())&&!We(t,"body")))&&Pe(t,"direction","ltr"===Pe(t,"direction")?"":"ltr")},tooltip:"Left-to-Right"},rtl:{state:function(e,t){return t&&"rtl"===t.style.direction},exec:function(){var e=this.getRangeHelper(),t=e.getFirstBlockParent();(this.focus(),t&&!We(t,"body")||(this.execCommand("formatBlock","p"),(t=e.getFirstBlockParent())&&!We(t,"body")))&&Pe(t,"direction","rtl"===Pe(t,"direction")?"":"rtl")},tooltip:"Right-to-Left"},print:{exec:"print",tooltip:"Print"},maximize:{state:function(){return this.maximize()},exec:function(){this.maximize(!this.maximize())},txtExec:function(){this.maximize(!this.maximize())},tooltip:"Maximize",shortcut:"Ctrl+Shift+M"},source:{state:function(){return this.sourceMode()},exec:function(){this.toggleSourceMode()},txtExec:function(){this.toggleSourceMode()},tooltip:"View source",shortcut:"Ctrl+Shift+S"},ignore:{}},w={};function ht(i){var r=this,a=[],l=function(e){return"signal"+e.charAt(0).toUpperCase()+e.slice(1)},e=function(e,t){e=[].slice.call(e);var n,o,r=l(e.shift());for(n=0;n<a.length;n++)if(r in a[n]&&(o=a[n][r].apply(i,e),t))return o};r.call=function(){e(arguments,!1)},r.callOnlyFirst=function(){return e(arguments,!0)},r.hasHandler=function(e){var t=a.length;for(e=l(e);t--;)if(e in a[t])return!0;return!1},r.exists=function(e){return e in w&&("function"==typeof(e=w[e])&&"object"==typeof e.prototype)},r.isRegistered=function(e){if(r.exists(e))for(var t=a.length;t--;)if(a[t]instanceof w[e])return!0;return!1},r.register=function(e){return!(!r.exists(e)||r.isRegistered(e))&&(e=new w[e],a.push(e),"init"in e&&e.init.call(i),!0)},r.deregister=function(e){var t,n=a.length,o=!1;if(!r.isRegistered(e))return o;for(;n--;)a[n]instanceof w[e]&&(o=!0,"destroy"in(t=a.splice(n,1)[0])&&t.destroy.call(i));return o},r.destroy=function(){for(var e=a.length;e--;)"destroy"in a[e]&&a[e].destroy.call(i);a=[],i=null}}ht.plugins=w;var C=h&&h<11,k=function(e,t,n){var o,r,i,a,l,c="",s=e.startContainer,u=e.startOffset;for(s&&3!==s.nodeType&&(s=s.childNodes[u],u=0),i=a=u;n>c.length&&s&&3===s.nodeType;)o=s.nodeValue,r=n-c.length,l&&(a=o.length,i=0),l=s,t?(u=i=Math.max(a-r,0),c=o.substr(i,a-i)+c,s=l.previousSibling):(u=i+(a=Math.min(r,o.length)),c+=o.substr(i,a),s=l.nextSibling);return{node:l||s,offset:u,text:c}};function vt(i,e){var a,l,c=e||i.contentDocument||i.document,s="sceditor-start-marker",u="sceditor-end-marker",y=this;y.insertHTML=function(e,t){var n,o;if(!y.selectedRange())return!1;for(t&&(e+=y.selectedHtml()+t),o=De("p",{},c),n=c.createDocumentFragment(),o.innerHTML=e;o.firstChild;)Fe(n,o.firstChild);y.insertNode(n)},l=function(e,t,n){var o,r=c.createDocumentFragment();if("string"==typeof e?(t&&(e+=y.selectedHtml()+t),r=et(e)):(Fe(r,e),t&&(Fe(r,y.selectedRange().extractContents()),Fe(r,t))),o=r.lastChild){for(;!rt(o.lastChild,!0);)o=o.lastChild;if(ot(o)?o.lastChild||Fe(o,document.createTextNode("​")):o=r,y.removeMarkers(),Fe(o,a(s)),Fe(o,a(u)),n){var i=De("div");return Fe(i,r),i.innerHTML}return r}},y.insertNode=function(e,t){var n=l(e,t),o=y.selectedRange(),r=o.commonAncestorContainer;if(!n)return!1;o.deleteContents(),r&&3!==r.nodeType&&!ot(r)?je(n,r):o.insertNode(n),y.restoreRange()},y.cloneSelected=function(){var e=y.selectedRange();if(e)return e.cloneRange()},y.selectedRange=function(){var e,t,n=i.getSelection();if(n){if(n.rangeCount<=0){for(t=c.body;t.firstChild;)t=t.firstChild;(e=c.createRange()).setStartBefore(t),n.addRange(e)}return 0<n.rangeCount&&(e=n.getRangeAt(0)),e}},y.hasSelection=function(){var e=i.getSelection();return e&&0<e.rangeCount},y.selectedHtml=function(){var e,t=y.selectedRange();return t?(Fe(e=De("p",{},c),t.cloneContents()),e.innerHTML):""},y.parentNode=function(){var e=y.selectedRange();if(e)return e.commonAncestorContainer},y.getFirstBlockParent=function(e){var t=function(e){return rt(e,!0)&&(e=e?e.parentNode:null)?t(e):e};return t(e||y.parentNode())},y.insertNodeAt=function(e,t){var n=y.selectedRange(),o=y.cloneSelected();if(!o)return!1;o.collapse(e),o.insertNode(t),y.selectRange(n)},a=function(e){y.removeMarker(e);var t=De("span",{id:e,className:"sceditor-selection sceditor-ignore",style:"display:none;line-height:0"},c);return t.innerHTML=" ",t},y.insertMarkers=function(){var e=y.selectedRange(),t=a(s);y.removeMarkers(),y.insertNodeAt(!0,t),e&&e.collapsed?t.parentNode.insertBefore(a(u),t.nextSibling):y.insertNodeAt(!1,a(u))},y.getMarker=function(e){return c.getElementById(e)},y.removeMarker=function(e){var t=y.getMarker(e);t&&Re(t)},y.removeMarkers=function(){y.removeMarker(s),y.removeMarker(u)},y.saveRange=function(){y.insertMarkers()},y.selectRange=function(e){var t,n=i.getSelection(),o=e.endContainer;if(!C&&e.collapsed&&o&&!rt(o,!0)){for(t=o.lastChild;t&&We(t,".sceditor-ignore");)t=t.previousSibling;if(We(t,"br")){var r=c.createRange();r.setEndAfter(t),r.collapse(!1),y.compare(e,r)&&(e.setStartBefore(t),e.collapse(!0))}}n&&(y.clear(),n.addRange(e))},y.restoreRange=function(){var e,t=y.selectedRange(),n=y.getMarker(s),o=y.getMarker(u);if(!n||!o||!t)return!1;e=n.nextSibling===o,(t=c.createRange()).setStartBefore(n),t.setEndAfter(o),e&&t.collapse(!0),y.selectRange(t),y.removeMarkers()},y.selectOuterText=function(e,t){var n,o,r=y.cloneSelected();if(!r)return!1;r.collapse(!1),n=k(r,!0,e),o=k(r,!1,t),r.setStart(n.node,n.offset),r.setEnd(o.node,o.offset),y.selectRange(r)},y.getOuterText=function(e,t){var n=y.cloneSelected();return n?(n.collapse(!e),k(n,e,t).text):""},y.replaceKeyword=function(e,t,n,o,r,i){n||e.sort(function(e,t){return e[0].length-t[0].length});var a,l,c,s,u,d,f,p,m="(^|[\\s    ])",g=e.length,h=r?1:0,v=o||e[g-1][0].length;for(r&&v++,i=i||"",u=(a=y.getOuterText(!0,v)).length,a+=i,t&&(a+=y.getOuterText(!1,v));g--;)if(p=(f=e[g][0]).length,s=Math.max(0,u-p-h),c=-1,r?(l=a.substr(s).match(new RegExp(m+ft(f)+m)))&&(c=l.index+s+l[1].length):c=a.indexOf(f,s),-1<c&&c<=u&&u<=c+p+h)return d=u-c,y.selectOuterText(d,p-d-(/^\S/.test(i)?1:0)),y.insertHTML(e[g][1]),!0;return!1},y.compare=function(e,t){return t||(t=y.selectedRange()),e&&t?0===e.compareBoundaryPoints(Range.END_TO_END,t)&&0===e.compareBoundaryPoints(Range.START_TO_START,t):!e&&!t},y.clear=function(){var e=i.getSelection();e&&(e.removeAllRanges?e.removeAllRanges():e.empty&&e.empty())}}var yt=window,bt=document,xt=h,wt=xt&&xt<11,Ct=/^image\/(p?jpe?g|gif|png|bmp)$/i;function kt(l,e){var a,w,u,c,i,m,d,s,f,p,g,h,t,v,r,y,b,x,C,n,o,k,S,E,T,D,M,N,R,F,H,_,z,O,A,B,I,L,P,V,W,j,q,U,$,Y,K,X,G,J,Q,Z,ee,te,ne,oe,re,ie,ae,le,ce,se,ue,de=this,fe={},pe=[],me=[],ge={},he={},ve={};de.commands=Ce(!0,{},e.commands||gt);var ye=de.opts=Ce(!0,{},ct,e);de.opts.emoticons=e.emoticons||ct.emoticons,M=function(){l._sceditor=de,ye.locale&&"en"!==ye.locale&&z(),je(w=De("div",{className:"sceditor-container"}),l),Pe(w,"z-index",ye.zIndex),xt&&Ue(w,"ie ie"+xt),n=l.required,l.required=!1;var e=kt.formats[ye.format];"init"in(a=e?new e:{})&&a.init.call(de),_(),L(),O(),H(),A(),B(),dt||de.toggleSourceMode(),J();var t=function(){Oe(yt,"load",t),ye.autofocus&&ne(),ue(),Z(),r.call("ready"),"onReady"in a&&a.onReady.call(de)};ze(yt,"load",t),"complete"===bt.readyState&&t()},_=function(){var e=ye.plugins;e=e?e.toString().split(","):[],r=new ht(de),e.forEach(function(e){r.register(e.trim())})},z=function(){var e;(t=kt.locale[ye.locale])||(e=ye.locale.split("-"),t=kt.locale[e[0]]),t&&t.dateFormat&&(ye.dateFormat=t.dateFormat)},H=function(){s=De("textarea"),c=De("iframe",{frameborder:0,allowfullscreen:!0}),ye.startInSourceMode?(Ue(w,"sourceMode"),Be(c)):(Ue(w,"wysiwygMode"),Be(s)),ye.spellcheck||Ae(w,"spellcheck","false"),"https:"===yt.location.protocol&&Ae(c,"src","javascript:false"),Fe(w,c),Fe(w,s),de.dimensions(ye.width||Ke(l),ye.height||Xe(l));var e=xt?"ie ie"+xt:"";e+=ut?" ios":"",(d=c.contentDocument).open(),d.write(mt("html",{attrs:' class="'+e+'"',spellcheck:ye.spellcheck?"":'spellcheck="false"',charset:ye.charset,style:ye.style})),d.close(),m=d.body,i=c.contentWindow,de.readOnly(!!ye.readOnly),(ut||st||xt)&&(Xe(m,"100%"),xt||ze(m,"touchend",de.focus));var t=Ae(l,"tabindex");Ae(s,"tabindex",t),Ae(c,"tabindex",t),v=new vt(i),Be(l),de.val(l.value);var n=ye.placeholder||Ae(l,"placeholder");n&&(s.placeholder=n,Ae(m,"placeholder",n))},A=function(){ye.autoUpdate&&(ze(m,"blur",se),ze(s,"blur",se)),null===ye.rtl&&(ye.rtl="rtl"===Pe(s,"direction")),de.rtl(!!ye.rtl),ye.autoExpand&&(ze(m,"load",ue,_e),ze(m,"input keyup",ue)),ye.resizeEnabled&&I(),Ae(w,"id",ye.id),de.emoticons(ye.emoticonsEnabled)},B=function(){var e=l.form,t="compositionstart compositionend",n="keydown keyup keypress focus blur contextmenu",o="onselectionchange"in d?"selectionchange":"keyup focus blur contextmenu mouseup touchend click";ze(bt,"click",X),e&&(ze(e,"reset",U),ze(e,"submit",de.updateOriginal,_e)),ze(m,"keypress",q),ze(m,"keydown",W),ze(m,"keydown",j),ze(m,"keyup",Z),ze(m,"blur",le),ze(m,"keyup",ce),ze(m,"paste",P),ze(m,t,Y),ze(m,o,ee),ze(m,n,K),ye.emoticonsCompat&&yt.getSelection&&ze(m,"keyup",re),ze(m,"blur",function(){de.val()||Ue(m,"placeholder")}),ze(m,"focus",function(){$e(m,"placeholder")}),ze(s,"blur",le),ze(s,"keyup",ce),ze(s,"keydown",W),ze(s,t,Y),ze(s,n,K),ze(d,"mousedown",$),ze(d,o,ee),ze(d,"beforedeactivate keyup mouseup",F),ze(d,"keyup",Z),ze(d,"focus",function(){p=null}),ze(w,"selectionchanged",te),ze(w,"selectionchanged",J),ze(w,"selectionchanged valuechanged nodechanged pasteraw paste",K)},O=function(){var i,a=de.commands,l=(ye.toolbarExclude||"").split(","),e=ye.toolbar.split("|");u=De("div",{className:"sceditor-toolbar",unselectable:"on"}),ye.icons in kt.icons&&(D=new kt.icons[ye.icons]),Se(e,function(e,t){i=De("div",{className:"sceditor-group"}),Se(t.split(","),function(e,t){var n,o,r=a[t];if(r&&!(-1<l.indexOf(t))){if(o=r.shortcut,n=mt("toolbarButton",{name:t,dispName:de._(r.name||r.tooltip||t)},!0).firstChild,D&&D.create)D.create(t)&&(je(D.create(t),n.firstChild),Ue(n,"has-icon"));n._sceTxtMode=!!r.txtExec,n._sceWysiwygMode=!!r.exec,Ye(n,"disabled",!r.exec),ze(n,"click",function(e){qe(n,"disabled")||R(n,r),J(),e.preventDefault()}),ze(n,"mousedown",function(e){de.closeDropDown(),e.preventDefault()}),r.tooltip&&Ae(n,"title",de._(r.tooltip)+(o?" ("+o+")":"")),o&&de.addShortcut(o,t),r.state?me.push({name:t,state:r.state}):be(r.exec)&&me.push({name:t,state:r.exec}),Fe(i,n),he[t]=n}}),i.firstChild&&Fe(u,i)}),Fe(ye.toolbarContainer||w,u)},I=function(){var o,r,i,a,t,n,e=De("div",{className:"sceditor-grip"}),l=De("div",{className:"sceditor-resize-cover"}),c="touchmove mousemove",s="touchcancel touchend mouseup",u=0,d=0,f=0,p=0,m=0,g=0,h=Ke(w),v=Xe(w),y=!1,b=de.rtl();if(o=ye.resizeMinHeight||v/1.5,r=ye.resizeMaxHeight||2.5*v,i=ye.resizeMinWidth||h/1.25,a=ye.resizeMaxWidth||1.25*h,t=function(e){"touchmove"===e.type?(e=yt.event,f=e.changedTouches[0].pageX,p=e.changedTouches[0].pageY):(f=e.pageX,p=e.pageY);var t=g+(p-d),n=b?m-(f-u):m+(f-u);0<a&&a<n&&(n=a),0<i&&n<i&&(n=i),ye.resizeWidth||(n=!1),0<r&&r<t&&(t=r),0<o&&t<o&&(t=o),ye.resizeHeight||(t=!1),(n||t)&&de.dimensions(n,t),e.preventDefault()},n=function(e){y&&(y=!1,Be(l),$e(w,"resizing"),Oe(bt,c,t),Oe(bt,s,n),e.preventDefault())},D&&D.create){var x=D.create("grip");x&&(Fe(e,x),Ue(e,"has-icon"))}Fe(w,e),Fe(w,l),Be(l),ze(e,"touchstart mousedown",function(e){"touchstart"===e.type?(e=yt.event,u=e.touches[0].pageX,d=e.touches[0].pageY):(u=e.pageX,d=e.pageY),m=Ke(w),g=Xe(w),y=!0,Ue(w,"resizing"),Ie(l),ze(bt,c,t),ze(bt,s,n),e.preventDefault()})},L=function(){var e=ye.emoticons,n=ye.emoticonsRoot||"";e&&(ve=Ce({},e.more,e.dropdown,e.hidden)),Se(ve,function(e,t){ve[e]=mt("emoticon",{key:e,url:n+(t.url||t),tooltip:t.tooltip||e}),ye.emoticonsEnabled&&pe.push(De("img",{src:n+(t.url||t)}))})},ne=function(){var e,t,n=m.firstChild,o=!!ye.autofocusEnd;if(Je(w)){if(de.sourceMode())return t=o?s.value.length:0,void s.setSelectionRange(t,t);if(at(m),o)for((n=m.lastChild)||(n=De("p",{},d),Fe(m,n));n.lastChild;)n=n.lastChild,!wt&&We(n,"br")&&n.previousSibling&&(n=n.previousSibling);e=d.createRange(),ot(n)?e.selectNodeContents(n):(e.setStartBefore(n),o&&e.setStartAfter(n)),e.collapse(!o),v.selectRange(e),x=e,o&&(m.scrollTop=m.scrollHeight),de.focus()}},de.readOnly=function(e){return"boolean"!=typeof e?!s.readonly:(m.contentEditable=!e,s.readonly=!e,G(e),de)},de.rtl=function(e){var t=e?"rtl":"ltr";return"boolean"!=typeof e?"rtl"===Ae(s,"dir"):(Ae(m,"dir",t),Ae(s,"dir",t),$e(w,"rtl"),$e(w,"ltr"),Ue(w,t),D&&D.rtl&&D.rtl(e),de)},G=function(n){var o=de.inSourceMode()?"_sceTxtMode":"_sceWysiwygMode";Se(he,function(e,t){Ye(t,"disabled",n||!t[o])})},de.width=function(e,t){return e||0===e?(de.dimensions(e,null,t),de):Ke(w)},de.dimensions=function(e,t,n){return t=!(!t&&0!==t)&&t,!1===(e=!(!e&&0!==e)&&e)&&!1===t?{width:de.width(),height:de.height()}:(!1!==e&&(!1!==n&&(ye.width=e),Ke(w,e)),!1!==t&&(!1!==n&&(ye.height=t),Xe(w,t)),de)},de.height=function(e,t){return e||0===e?(de.dimensions(null,e,t),de):Xe(w)},de.maximize=function(e){var t="sceditor-maximize";return xe(e)?qe(w,t):((e=!!e)&&(E=yt.pageYOffset),Ye(bt.documentElement,t,e),Ye(bt.body,t,e),Ye(w,t,e),de.width(e?"100%":ye.width,!1),de.height(e?"100%":ye.height,!1),e||yt.scrollTo(0,E),ue(),de)},ue=function(){ye.autoExpand&&!S&&(S=setTimeout(de.expandToContent,200))},de.expandToContent=function(e){if(!de.maximize()){if(clearTimeout(S),S=!1,!k){var t=ye.resizeMinHeight||ye.height||Xe(l);k={min:t,max:ye.resizeMaxHeight||2*t}}var n=bt.createRange();n.selectNodeContents(m);var o=n.getBoundingClientRect(),r=d.documentElement.clientHeight-1,i=o.bottom-o.top,a=de.height()+1+(i-r);e||-1===k.max||(a=Math.min(a,k.max)),de.height(Math.ceil(Math.max(a,k.min)))}},de.destroy=function(){if(r){r.destroy(),r=p=v=null,f&&Re(f),Oe(bt,"click",X);var e=l.form;e&&(Oe(e,"reset",U),Oe(e,"submit",de.updateOriginal)),Re(s),Re(u),Re(w),delete l._sceditor,Ie(l),l.required=n}},de.createDropDown=function(e,t,n,o){var r,i="sceditor-"+t;de.closeDropDown(!0),f&&qe(f,i)||(!1!==o&&Se(He(n,":not(input):not(textarea)"),function(e,t){t.nodeType===Ee&&Ae(t,"unselectable","on")}),r=Ce({top:e.offsetTop,left:e.offsetLeft,marginTop:e.clientHeight},ye.dropDownCss),Pe(f=De("div",{className:"sceditor-dropdown "+i}),r),Fe(f,n),Fe(w,f),ze(f,"click focusin",function(e){e.stopPropagation()}),setTimeout(function(){if(f){var e=He(f,"input,textarea")[0];e&&e.focus()}}))},X=function(e){3!==e.which&&f&&!e.defaultPrevented&&(se(),de.closeDropDown())},P=function(e){var t,n,o=xt||st,r=m,i=e.clipboardData;if(i&&!o){var a={},l=i.types,c=i.items;e.preventDefault();for(var s=0;s<l.length;s++){if(yt.FileReader&&c&&Ct.test(c[s].type))return t=i.items[s].getAsFile(),n=void 0,(n=new FileReader).onload=function(e){V({html:'<img src="'+e.target.result+'" />'})},void n.readAsDataURL(t);a[l[s]]=i.getData(l[s])}a.text=a["text/plain"],a.html=a["text/html"],V(a)}else if(!T){var u=r.scrollTop;for(v.saveRange(),T=bt.createDocumentFragment();r.firstChild;)Fe(T,r.firstChild);setTimeout(function(){var e=r.innerHTML;r.innerHTML="",Fe(r,T),r.scrollTop=u,T=!1,v.restoreRange(),V({html:e})},0)}},V=function(e){var t=De("div",{},d);r.call("pasteRaw",e),Ge(w,"pasteraw",e),e.html?(t.innerHTML=e.html,it(t)):t.innerHTML=pt(e.text||"");var n={val:t.innerHTML};"fragmentToSource"in a&&(n.val=a.fragmentToSource(n.val,d,y)),r.call("paste",n),Ge(w,"paste",n),"fragmentToHtml"in a&&(n.val=a.fragmentToHtml(n.val,y)),r.call("pasteHtml",n),de.wysiwygEditorInsertHtml(n.val,null,!0)},de.closeDropDown=function(e){f&&(Re(f),f=null),!0===e&&de.focus()},de.wysiwygEditorInsertHtml=function(e,t,n){var o,r,i,a=Xe(c);de.focus(),!n&&Ne(b,"code")||(v.insertHTML(e,t),v.saveRange(),N(),Ie(o=He(m,"#sceditor-end-marker")[0]),r=m.scrollTop,i=lt(o).top+1.5*o.offsetHeight-a,Be(o),(r<i||i+a<r)&&(m.scrollTop=i),ae(!1),v.restoreRange(),Z())},de.wysiwygEditorInsertText=function(e,t){de.wysiwygEditorInsertHtml(pt(e),pt(t))},de.insertText=function(e,t){return de.inSourceMode()?de.sourceEditorInsertText(e,t):de.wysiwygEditorInsertText(e,t),de},de.sourceEditorInsertText=function(e,t){var n,o,r=s.selectionStart,i=s.selectionEnd;n=s.scrollTop,s.focus(),o=s.value,t&&(e+=o.substring(r,i)+t),s.value=o.substring(0,r)+e+o.substring(i,o.length),s.selectionStart=r+e.length-(t?t.length:0),s.selectionEnd=s.selectionStart,s.scrollTop=n,s.focus(),ae()},de.getRangeHelper=function(){return v},de.sourceEditorCaret=function(e){return s.focus(),e?(s.selectionStart=e.start,s.selectionEnd=e.end,this):{start:s.selectionStart,end:s.selectionEnd}},de.val=function(e,t){return be(e)?(de.inSourceMode()?de.setSourceEditorValue(e):(!1!==t&&"toHtml"in a&&(e=a.toHtml(e)),de.setWysiwygEditorValue(e)),de):de.inSourceMode()?de.getSourceEditorValue(!1):de.getWysiwygEditorValue(t)},de.insert=function(e,t,n,o,r){if(de.inSourceMode())return de.sourceEditorInsertText(e,t),de;if(t){var i=v.selectedHtml();!1!==n&&"fragmentToSource"in a&&(i=a.fragmentToSource(i,d,y)),e+=i+t}return!1!==n&&"fragmentToHtml"in a&&(e=a.fragmentToHtml(e,y)),!1!==n&&!0===r&&(e=e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")),de.wysiwygEditorInsertHtml(e),de},de.getWysiwygEditorValue=function(e){for(var t,n=De("div",{},d),o=m.childNodes,r=0;r<o.length;r++)Fe(n,o[r].cloneNode(!0));return Fe(m,n),it(n),Re(n),t=n.innerHTML,!1!==e&&a.hasOwnProperty("toSource")&&(t=a.toSource(t,d)),t},de.getBody=function(){return m},de.getContentAreaContainer=function(){return c},de.getSourceEditorValue=function(e){var t=s.value;return!1!==e&&"toHtml"in a&&(t=a.toHtml(t)),t},de.setWysiwygEditorValue=function(e){e||(e="<p>"+(xt?"":"<br />")+"</p>"),m.innerHTML=e,N(),Z(),ae(),ue()},de.setSourceEditorValue=function(e){s.value=e,ae()},de.updateOriginal=function(){l.value=de.val()},N=function(){var e,s,u,d,t,f,p;ye.emoticonsEnabled&&(e=m,s=ve,u=ye.emoticonsCompat,d=e.ownerDocument,t="(^|\\s| | | | |$)",f=[],p={},Me(e,"code")||(Se(s,function(e){p[e]=new RegExp(t+ft(e)+t),f.push(e)}),f.sort(function(e,t){return t.length-e.length}),function e(t){for(t=t.firstChild;t;){if(t.nodeType!==Ee||We(t,"code")||e(t),t.nodeType===Te)for(var n=0;n<f.length;n++){var o=t.nodeValue,r=f[n],i=u?o.search(p[r]):o.indexOf(r);if(-1<i){var a=o.indexOf(r,i),l=et(s[r],d),c=o.substr(a+r.length);l.appendChild(d.createTextNode(c)),t.nodeValue=o.substr(0,a),t.parentNode.insertBefore(l,t.nextSibling)}}t=t.nextSibling}}(e)))},de.inSourceMode=function(){return qe(w,"sourceMode")},de.sourceMode=function(e){var t=de.inSourceMode();return"boolean"!=typeof e?t:((t&&!e||!t&&e)&&de.toggleSourceMode(),de)},de.toggleSourceMode=function(){var e=de.inSourceMode();!dt&&e||(e||(v.saveRange(),v.clear()),de.blur(),e?de.setWysiwygEditorValue(de.getSourceEditorValue()):de.setSourceEditorValue(de.getWysiwygEditorValue()),p=null,Le(s),Le(c),Ye(w,"wysiwygMode",e),Ye(w,"sourceMode",!e),G(),J())},Q=function(){return s.focus(),s.value.substring(s.selectionStart,s.selectionEnd)},R=function(e,t){de.inSourceMode()?t.txtExec&&(Array.isArray(t.txtExec)?de.sourceEditorInsertText.apply(de,t.txtExec):t.txtExec.call(de,e,Q())):t.exec&&(we(t.exec)?t.exec.call(de,e):de.execCommand(t.exec,t.hasOwnProperty("execParam")?t.execParam:null))},F=function(){xt&&(p=v.selectedRange())},de.execCommand=function(e,t){var n=!1,o=de.commands[e];if(de.focus(),!Ne(v.parentNode(),"code")){try{n=d.execCommand(e,!1,t)}catch(e){}!n&&o&&o.errorMessage&&alert(de._(o.errorMessage)),J()}},ee=function(){function e(){if(i.getSelection()&&i.getSelection().rangeCount<=0)x=null;else if(v&&!v.compare(x)){if((x=v.cloneSelected())&&x.collapsed){var e=x.startContainer,t=x.startOffset;for(t&&e.nodeType!==Te&&(e=e.childNodes[t]);e&&e.parentNode!==m;)e=e.parentNode;e&&rt(e,!0)&&(v.saveRange(),n=d,Qe(m,function(e){rt(e,!0)?(o||je(o=De("p",{},n),e),e.nodeType===Te&&""===e.nodeValue||Fe(o,e)):o=null},!1,!0),v.restoreRange())}Ge(w,"selectionchanged")}var n,o;C=!1}C||(C=!0,"onselectionchange"in d?e():setTimeout(e,100))},te=function(){var e,t=v.parentNode();y!==t&&(e=y,y=t,b=v.getFirstBlockParent(t),Ge(w,"nodechanged",{oldNode:e,newNode:y}))},de.currentNode=function(){return y},de.currentBlockNode=function(){return b},J=function(){var e,t,n="active",o=d,r=de.sourceMode();if(de.readOnly())Se(He(u,n),function(e,t){$e(t,n)});else{r||(t=v.parentNode(),e=v.getFirstBlockParent(t));for(var i=0;i<me.length;i++){var a=0,l=he[me[i].name],c=me[i].state,s=r&&!l._sceTxtMode||!r&&!l._sceWysiwygMode;if(be(c)){if(!r)try{-1<(a=o.queryCommandEnabled(c)?0:-1)&&(a=o.queryCommandState(c)?1:0)}catch(e){}}else s||(a=c.call(de,t,e));Ye(l,"disabled",s||a<0),Ye(l,n,0<a)}D&&D.update&&D.update(r,t,e)}},q=function(e){if(!e.defaultPrevented&&(de.closeDropDown(),13===e.which)){if(!We(b,"li,ul,ol")&&tt(b)){p=null;var t=De("br",{},d);if(v.insertNode(t),!wt){var n=t.parentNode,o=n.lastChild;o&&o.nodeType===Te&&""===o.nodeValue&&(Re(o),o=n.lastChild),!rt(n,!0)&&o===t&&rt(t.previousSibling)&&v.insertHTML("<br>")}e.preventDefault()}}},Z=function(){Ze(m,function(e){if(e.nodeType===Ee&&!/inline/.test(Pe(e,"display"))&&!We(e,".sceditor-nlf")&&tt(e)){var t=De("p",{},d);return t.className="sceditor-nlf",t.innerHTML=wt?"":"<br />",Fe(m,t),!1}if(3===e.nodeType&&!/^\s*$/.test(e.nodeValue)||We(e,"br"))return!1})},U=function(){de.val(l.value)},$=function(){de.closeDropDown(),p=null},de._=function(){var n=arguments;return t&&t[n[0]]&&(n[0]=t[n[0]]),n[0].replace(/\{(\d+)\}/g,function(e,t){return void 0!==n[t-0+1]?n[t-0+1]:"{"+t+"}"})},K=function(t){r&&r.call(t.type+"Event",t,de);var e=(t.target===s?"scesrc":"scewys")+t.type;fe[e]&&fe[e].forEach(function(e){e.call(de,t)})},de.bind=function(e,t,n,o){for(var r=(e=e.split(" ")).length;r--;)if(we(t)){var i="scewys"+e[r],a="scesrc"+e[r];n||(fe[i]=fe[i]||[],fe[i].push(t)),o||(fe[a]=fe[a]||[],fe[a].push(t)),"valuechanged"===e[r]&&(ae.hasHandler=!0)}return de},de.unbind=function(e,t,n,o){for(var r=(e=e.split(" ")).length;r--;)we(t)&&(n||ke(fe["scewys"+e[r]]||[],t),o||ke(fe["scesrc"+e[r]]||[],t));return de},de.blur=function(e,t,n){return we(e)?de.bind("blur",e,t,n):de.sourceMode()?s.blur():m.blur(),de},de.focus=function(e,t,n){if(we(e))de.bind("focus",e,t,n);else if(de.inSourceMode())s.focus();else{if(He(d,":focus").length)return;var o,r=v.selectedRange();x||ne(),!wt&&r&&1===r.endOffset&&r.collapsed&&(o=r.endContainer)&&1===o.childNodes.length&&We(o.firstChild,"br")&&(r.setStartBefore(o.firstChild),r.collapse(!0),v.selectRange(r)),i.focus(),m.focus(),p&&(v.selectRange(p),p=null)}return J(),de},de.keyDown=function(e,t,n){return de.bind("keydown",e,t,n)},de.keyPress=function(e,t,n){return de.bind("keypress",e,t,n)},de.keyUp=function(e,t,n){return de.bind("keyup",e,t,n)},de.nodeChanged=function(e){return de.bind("nodechanged",e,!1,!0)},de.selectionChanged=function(e){return de.bind("selectionchanged",e,!1,!0)},de.valueChanged=function(e,t,n){return de.bind("valuechanged",e,t,n)},oe=function(e){var n=0,o=de.emoticonsCache,t=String.fromCharCode(e.which);Ne(b,"code")||(o||(o=[],Se(ve,function(e,t){o[n++]=[e,t]}),o.sort(function(e,t){return e[0].length-t[0].length}),de.emoticonsCache=o,de.longestEmoticonCode=o[o.length-1][0].length),v.replaceKeyword(de.emoticonsCache,!0,!0,de.longestEmoticonCode,ye.emoticonsCompat,t)&&(ye.emoticonsCompat&&/^\s$/.test(t)||e.preventDefault()))},re=function(){!function(e,t){var n=/[^\s\xA0\u2002\u2003\u2009\u00a0]+/,o=e&&He(e,"img[data-sceditor-emoticon]");if(e&&o.length)for(var r=0;r<o.length;r++){var i=o[r],a=i.parentNode,l=i.previousSibling,c=i.nextSibling;if(l&&n.test(l.nodeValue.slice(-1))||c&&n.test((c.nodeValue||"")[0])){var s=t.cloneSelected(),u=-1,d=s.startContainer,f=l.nodeValue;null===f&&(f=l.innerText||""),f+=Ve(i,"sceditor-emoticon"),d===c&&(u=f.length+s.startOffset),d===e&&e.childNodes[s.startOffset]===c&&(u=f.length),d===l&&(u=s.startOffset),c&&c.nodeType===Te||(c=a.insertBefore(a.ownerDocument.createTextNode(""),c)),c.insertData(0,f),Re(l),Re(i),-1<u&&(s.setStart(c,u),s.collapse(!0),t.selectRange(s))}}}(b,v)},de.emoticons=function(e){if(!e&&!1!==e)return ye.emoticonsEnabled;(ye.emoticonsEnabled=e)?(ze(m,"keypress",oe),de.sourceMode()||(v.saveRange(),N(),ae(!1),v.restoreRange())):(Se(He(m,"img[data-sceditor-emoticon]"),function(e,t){var n=Ve(t,"sceditor-emoticon"),o=d.createTextNode(n);t.parentNode.replaceChild(o,t)}),Oe(m,"keypress",oe),ae());return de},de.css=function(e){return o||(o=De("style",{id:"inline"},d),Fe(d.head,o)),be(e)?(o.styleSheet?o.styleSheet.cssText=e:o.innerHTML=e,de):o.styleSheet?o.styleSheet.cssText:o.innerHTML},W=function(e){var t=[],n={"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+",";":": ","'":'"',",":"<",".":">","/":"?","\\":"|","[":"{","]":"}"},o={109:"-",110:"del",111:"/",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"},r=e.which,i={8:"backspace",9:"tab",13:"enter",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",91:"win",92:"win",93:"select",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scrolllock",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"}[r]||String.fromCharCode(r).toLowerCase();(e.ctrlKey||e.metaKey)&&t.push("ctrl"),e.altKey&&t.push("alt"),e.shiftKey&&(t.push("shift"),o[r]?i=o[r]:n[i]&&(i=n[i])),i&&(r<16||18<r)&&t.push(i),t=t.join("+"),ge[t]&&!1===ge[t].call(de)&&(e.stopPropagation(),e.preventDefault())},de.addShortcut=function(e,t){return e=e.toLowerCase(),be(t)?ge[e]=function(){return R(he[t],de.commands[t]),!1}:ge[e]=t,de},de.removeShortcut=function(e){return delete ge[e.toLowerCase()],de},j=function(e){var t,n,o;if(!ye.disableBlockRemove&&8===e.which&&(n=v.selectedRange())&&(t=n.startContainer,0===n.startOffset&&(o=ie())&&!We(o,"body"))){for(;t!==o;){for(;t.previousSibling;)if((t=t.previousSibling).nodeType!==Te||t.nodeValue)return;if(!(t=t.parentNode))return}de.clearBlockFormatting(o),e.preventDefault()}},ie=function(){for(var e=b;!tt(e)||rt(e,!0);)if(!(e=e.parentNode)||We(e,"body"))return;return e},de.clearBlockFormatting=function(e){return!(e=e||ie())||We(e,"body")||(v.saveRange(),e.className="",p=null,Ae(e,"style",""),We(e,"p,div,td")||nt(e,"p"),v.restoreRange()),de},ae=function(e){if(r&&(r.hasHandler("valuechangedEvent")||ae.hasHandler)){var t,n=de.sourceMode(),o=!n&&v.hasSelection();e=(g=!1)!==e&&!d.getElementById("sceditor-start-marker"),h&&(clearTimeout(h),h=!1),o&&e&&v.saveRange(),(t=n?s.value:m.innerHTML)!==ae.lastVal&&(ae.lastVal=t,Ge(w,"valuechanged",{rawValue:n?de.val():t})),o&&e&&v.removeMarkers()}},le=function(){h&&ae()},ce=function(e){var t=e.which,n=ce.lastChar,o=13===n||32===n,r=8===n||46===n;ce.lastChar=t,g||(13===t||32===t?o?ce.triggerNext=!0:ae():8===t||46===t?r?ce.triggerNext=!0:ae():ce.triggerNext&&(ae(),ce.triggerNext=!1),clearTimeout(h),h=setTimeout(function(){g||ae()},1500))},Y=function(e){(g=/start/i.test(e.type))||ae()},se=function(){de.updateOriginal()},M()}kt.locale={},kt.formats={},kt.icons={},kt.command={get:function(e){return gt[e]||null},set:function(e,t){return!(!e||!t)&&((t=Ce(gt[e]||{},t)).remove=function(){kt.command.remove(e)},gt[e]=t,this)},remove:function(e){return gt[e]&&delete gt[e],this}},window.sceditor={command:kt.command,commands:gt,defaultOptions:ct,ie:h,ios:ut,isWysiwygSupported:dt,regexEscape:ft,escapeEntities:pt,escapeUriScheme:function(e){var t,n=window.location;return e&&/^[^\/]*:/i.test(e)&&!v.test(e)?((t=n.pathname.split("/")).pop(),n.protocol+"//"+n.host+t.join("/")+"/"+e):e},dom:{css:Pe,attr:Ae,removeAttr:r,is:We,closest:Ne,width:Ke,height:Xe,traverse:Qe,rTraverse:Ze,parseHTML:et,hasStyling:tt,convertElement:nt,blockLevelList:c,canHaveChildren:ot,isInline:rt,copyCSS:s,fixNesting:it,findCommonAncestor:function(e,t){for(;e=e.parentNode;)if((n=e)!==(o=t)&&n.contains&&n.contains(o))return e;var n,o},getSibling:d,removeWhiteSpace:at,extractContents:u,getOffset:lt,getStyle:f,hasStyle:function(e,t,n){var o=f(e,t);return!!o&&(!n||o===n||Array.isArray(n)&&-1<n.indexOf(o))}},locale:kt.locale,icons:kt.icons,utils:{each:Se,isEmptyObject:t,extend:Ce},plugins:ht.plugins,formats:kt.formats,create:function(e,t){t=t||{},Me(e,".sceditor-container")||(t.runWithoutWysiwygSupport||dt)&&new kt(e,t)},instance:function(e){return e._sceditor}}}();
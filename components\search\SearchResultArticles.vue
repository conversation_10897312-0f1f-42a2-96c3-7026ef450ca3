<template>
  <div>
    <SeriesItem
      v-for="item in datas"
      :key="item.aid"
      :mode="item.cover_type ? 'vertical' : 'horizontal'"
      :title="item.title"
      :date="item.time | date2short"
      :avatar="item.avatar"
      :author="item.author"
      :groupname="`${item.parent_group_name}-${item.group_name}`"
      :views="item.hits | num2short"
      :comments="item.comments | num2short"
      :banner="item.cover"
      @cover-click="toPage(`/detail/${item.aid}`)"
      @avatar-click="toPage(`/profile/${item.uid}`)"
    />
    <!-- 分页 -->
    <div style="margin-top: 40px">
      <el-pagination
        class="my-pagination"
        layout="prev, pager, next, jumper"
        :prev-text="$t('paginate.prev')"
        :next-text="$t('paginate.next')"
        :hide-on-single-page="true"
        :disabled="loading"
        :current-page="pageinfo.cur"
        :total="pageinfo.count"
        :page-size="pageinfo.size"
        @current-change="$emit('current-change', $event)"
      />
    </div>
  </div>
</template>

<script>
import SeriesItem from '@/components/search/SeriesItem';
export default {
  components: {
    SeriesItem
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    pageinfo: {
      type: Object,
      default: () => ({ cur: 1, count: 0, size: 20 })
    },
    datas: {
      type: Array,
      default: () => []
    }
  }
};
</script>

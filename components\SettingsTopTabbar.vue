<template>
  <div>
    <ul class="top-title-tabbar flex ai-center">
      <li class="tab-item" :class="{ active: $route.name.includes('settings-password') }">
        <nuxt-link class="flex ai-center nuxt-link" :to="$i18n.path('settings/password')">修改密码</nuxt-link>
      </li>
      <li class="tab-item" :class="{ active: $route.name.includes('settings-infos') }">
        <nuxt-link class="flex ai-center nuxt-link" :to="$i18n.path('settings/infos')">修改资料</nuxt-link>
      </li>
      <!-- <li class="tab-item" :class="{ active: $route.name.includes('settings-email') }">
        <nuxt-link class="flex ai-center nuxt-link" :to="$i18n.path('settings/email')">修改邮箱</nuxt-link>
      </li> -->
    </ul>
    <p class="line"></p>
  </div>
</template>
<script>
export default {
  name: 'SettingsTopTabbar'
};
</script>
<style lang="scss" scope>
.top-title-tabbar {
  height: 50px;
  padding: 0 15px;
  .tab-item {
    height: 100%;
    border-top: 2px solid #fff;
    margin-right: 30px;
    &.active {
      border-color: $primary !important;
      .nuxt-link {
        color: $primary !important;
      }
    }
    .nuxt-link {
      font-size: $md;
      width: 100%;
      height: 100%;
    }
  }
}
.line {
  height: 10px;
  line-height: 10px;
  background: $gray-f7;
}
</style>

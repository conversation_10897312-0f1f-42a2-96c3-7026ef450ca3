const Cookie = process.client ? require('js-cookie') : undefined;

export const state = () => ({
  token: null, // 登录后 token
  loginLayerIndex: 0, // 登录弹窗显示的内容  0:登录 1:注册 2: 完善资料
  showLoginLayer: false, // 是否显示登录弹框
  isLogin: false, // 是否登录
  loginUser: null, // 当前登录用户信息
  unloginBack: false // 未登录条状回首页
});

export const mutations = {
  // 设置 token
  setToken(state, token) {
    state.token = token;
  },

  // 设置金币数量
  setCoins(state, { inc, num }) {
    const assets = state.loginUser.balance;
    if (inc) {
      assets && (assets.coin += num);
    } else {
      assets && (assets.coin -= num);
    }
  },

  // 登录
  login(state, user) {
    state.isLogin = true;
    state.loginUser = user;
    const token = { security_key: user.security_key };
    this.commit('login/setToken', token);
    Cookie.set('token', token, {
      expires: 30
    });
  },

  // 退出登录
  logout(state) {
    state.isLogin = false;
    state.loginUser = null;
  },

  // 切换登录弹框的显示和隐藏
  toggleLoginLayer(state, { show = false, index = 0 }) {
    state.showLoginLayer = show;
    state.loginLayerIndex = index;
  },

  // 设置 "是否是未登录跳回"
  setUnloginBack(state, flag) {
    state.unloginBack = flag;
  },

  // 刷新用户信息
  refreshUserinfo(state, user) {
    state.loginUser = { ...state.loginUser, ...user };
  }
};

export const actions = {
  // 登录
  async login({ commit }, user) {
    const res = await this.$axios.$post('/api/user/login', user);
    if (res.code === 0) {
      commit('login', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 刷新用户信息
  async refreshUserinfo({ commit }) {
    if (this.state.login.loginUser.uid) {
      const uid = this.state.login.loginUser.uid;
      const res = await this.$axios.$post('/api/user/info', { uid });
      res.code === 0 && commit('refreshUserinfo', res.data);
    }
  },

  // 注销登录
  logout({ commit }) {
    commit('setToken', null);
    Cookie.remove('token');
    commit('logout');
    return Promise.resolve();
  }
};

export const getters = {
  isLogin(state) {
    return state.isLogin;
  },
  loginUser(state) {
    return (
      state.loginUser || {
        uid: 0,
        nickname: '',
        avatar: '',
        passer: 0,
        gender: 0,
        sign: '',
        status: 0,
        banner: '',
        ban_end_date: '',
        following: 0,
        comments: 0,
        favorites: 0,
        articles: 0,
        followers: 0,
        level: { exp: 0, level: 0, name: '', next_exp: '' },
        security_key: '',
        balance: { coin: 0, balance: 0 },
        medals: []
      }
    );
  }
};

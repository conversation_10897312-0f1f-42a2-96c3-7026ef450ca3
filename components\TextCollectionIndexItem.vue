<template>
  <!-- eslint-disable vue/no-v-html -->
  <div class="text-collection-item-contaienr flex flex-between">
    <div class="left-infos flex-1">
      <!-- <p class="title pad-right-20 hover" @click="$emit('title-click')">{{ title }}</p> -->
      <p class="title pad-right-20 hover" @click="$emit('title-click')" v-html="title"></p>
      <div class="infos text-gray fs-sm flex">
        <span class="mar-right-30">{{ time }}</span>
        <div class="flex">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 730.49 426.86">
            <path
              d="M400,577.54c-94.2,0-170.85-76.64-170.85-170.85S305.82,235.84,400,235.84s170.85,76.64,170.85,170.85S494.22,577.54,400,577.54Zm0-303.06c-72.89,0-132.2,59.31-132.2,132.21s59.31,132.2,132.2,132.2,132.21-59.31,132.21-132.2S472.92,274.48,400,274.48Z"
              transform="translate(-34.78 -193.26)"
            />
            <path
              d="M408.76,620.11c-199.46,0-318-118.77-359.76-169.84h0a62.65,62.65,0,0,1-1.08-78.12c41.42-53.8,158.53-178.88,351.35-178.88,194.14,0,311.45,126.31,352.89,180.62a62.78,62.78,0,0,1,1.34,75C716.65,500.36,609.31,620.11,408.76,620.11ZM78.93,425.81c38.23,46.81,146.92,155.66,329.83,155.66,182.61,0,279.94-108.46,313.3-155.1a24.33,24.33,0,0,0-.62-29.06c-38-49.73-145.3-165.41-322.17-165.41-175.64,0-282.79,114.55-320.72,163.8a24.16,24.16,0,0,0,.38,30.11Z"
              transform="translate(-34.78 -193.26)"
            />
          </svg>
          <span>{{ views }}</span>
        </div>
      </div>
    </div>
    <div class="right-comments-counter flex flex-cols">
      <span class="text-center text-primary fs-ml count">{{ comments }}</span>
      <span class="text-center mar-top-10 text-gray fs-sm">{{ $t('components.part_card.comments') }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    views: {
      // 查看次数
      type: [String, Number],
      default: 0
    },
    comments: {
      // 评论数
      type: [String, Number],
      default: 256
    },
    time: {
      type: [String, Number],
      default: ''
    }
  }
};
</script>

<style lang="scss" scoped>
.text-collection-item-contaienr {
  .left-infos {
    .title {
      line-height: 1.5;
    }
    .infos {
      padding: 20px 0;
    }
  }
  .right-comments-counter {
    width: 80px;
  }
}
</style>

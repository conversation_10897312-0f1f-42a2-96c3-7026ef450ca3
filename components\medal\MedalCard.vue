<template>
  <div :class="`medal-card ${noBorder && 'no-border'}`">
    <div class="medal-img-container">
      <div v-if="!noMask" class="medal-img-mask">
        <slot name="mask-info"></slot>
      </div>
      <el-image :src="imageUrl" lazy fit="contain" class="user-medal-image"></el-image>
    </div>
    <div class="medal-name" :title="name">{{ name }}</div>
    <div class="medal-info">
      <slot name="description"></slot>
    </div>
    <div class="medal-action">
      <slot name="action"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MedalCard',
  props: {
    imageUrl: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    noBorder: {
      type: Boolean,
      default: false
    },
    noMask: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style lang="scss" scope>
.medal-card {
  padding: 10px;
  width: 130px;
  display: flex;
  flex-direction: column;
  border-radius: 5px;
  border: 1px solid rgba(243, 243, 243, 1);
  align-items: center;
  .medal-img-mask {
    position: absolute;
    border-radius: 5px;
    top: 0;
    left: 0;
    width: 110px;
    height: 110px;
    background: #000000;
    color: $white;
    opacity: 0;
    padding: 15px 5px;
    font-size: 13px;
    font-weight: 400;
    line-height: 20px;
  }
}
.medal-card.no-border {
  border: none !important;
}
.medal-card:not(.no-border):hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}
.medal-img-container {
  position: relative;
  width: 110px;
  height: 110px;
  .user-medal-image {
    margin: 10px;
    width: 90px;
    height: 90px;
    position: unset;
  }
}
@keyframes fade-in {
  to {
    opacity: 1;
    background: rgba(0, 0, 0, 0.6);
  }
}
.medal-img-container:hover .medal-img-mask {
  animation: fade-in 0.35s;
  -moz-animation: fade-in 0.35s;
  -webkit-animation: fade-in 0.35s;
  -o-animation: fade-in 0.35s;
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  -o-animation-fill-mode: forwards;
}
.medal-name {
  font-size: 15px;
  font-weight: 400;
  color: $dark;
  line-height: 35px;
  align-self: flex-start;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
.medal-info {
  align-self: flex-start;
  font-size: 13px;
  color: $gray;
  font-weight: 400;
  min-height: 24px;
}
.medal-action {
  margin-top: 16px;
}
</style>

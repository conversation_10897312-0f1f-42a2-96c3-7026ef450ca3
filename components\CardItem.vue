<template>
  <!-- eslint-disable vue/no-v-html -->
  <div class="article-item-container" @click="toDelOrThePage">
    <!--
      顶部图片 mode: 垂直:vertical 水平:horizontal
      ver: 固定高度为 100px
      hor: 固定高度为 225px
    -->
    <div class="article-item-img-container radius-5 vertical" :class="{ horizontal: mode === 'horizontal' }">
      <el-image :src="img" lazy></el-image>
      <span v-if="hasIcon" class="icon lv-icon lv-0">{{ iconText }}</span>
    </div>

    <!-- 标题 -->
    <p v-if="title" class="article-item-title text-dark fs-sm" :class="title2rows ? 'text-hide-2' : 'text-hide-1'" :title="title" v-html="title"></p>
    <p v-else class="article-item-title text-dark fs-sm" :class="title2rows ? 'text-hide-2' : 'text-hide-1'" :title="title">
      <slot name="title">{{ title }}</slot>
    </p>

    <!-- 底部信息 -->
    <div class="article-item-infos flex fs-sm text-gray">
      <slot>
        <div class="flex-1">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 730.49 426.86">
            <path
              d="M400,577.54c-94.2,0-170.85-76.64-170.85-170.85S305.82,235.84,400,235.84s170.85,76.64,170.85,170.85S494.22,577.54,400,577.54Zm0-303.06c-72.89,0-132.2,59.31-132.2,132.21s59.31,132.2,132.2,132.2,132.21-59.31,132.21-132.2S472.92,274.48,400,274.48Z"
              transform="translate(-34.78 -193.26)"
            />
            <path
              d="M408.76,620.11c-199.46,0-318-118.77-359.76-169.84h0a62.65,62.65,0,0,1-1.08-78.12c41.42-53.8,158.53-178.88,351.35-178.88,194.14,0,311.45,126.31,352.89,180.62a62.78,62.78,0,0,1,1.34,75C716.65,500.36,609.31,620.11,408.76,620.11ZM78.93,425.81c38.23,46.81,146.92,155.66,329.83,155.66,182.61,0,279.94-108.46,313.3-155.1a24.33,24.33,0,0,0-.62-29.06c-38-49.73-145.3-165.41-322.17-165.41-175.64,0-282.79,114.55-320.72,163.8a24.16,24.16,0,0,0,.38,30.11Z"
              transform="translate(-34.78 -193.26)"
            />
          </svg>
          <span>{{ views }}</span>
        </div>
        <div class="flex-1">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 624 447.5">
            <path
              d="M602.88,190.18h-407A101.39,101.39,0,0,0,94.65,291.45V521.9A101.4,101.4,0,0,0,195.89,623.19h407A101.41,101.41,0,0,0,704.17,521.9V291.45A101.4,101.4,0,0,0,602.88,190.18ZM678.77,521.9a76,76,0,0,1-75.89,75.89h-407a76,76,0,0,1-75.84-75.89V291.45a75.94,75.94,0,0,1,75.84-75.87h407a76,76,0,0,1,75.89,75.87Z"
              transform="translate(-87.41 -182.94)"
            />
            <path
              d="M602.89,630.44h-407A108.64,108.64,0,0,1,87.41,521.9V291.45A108.62,108.62,0,0,1,195.89,182.94h407A108.64,108.64,0,0,1,711.41,291.45V521.9A108.66,108.66,0,0,1,602.89,630.44Zm-407-433a94.11,94.11,0,0,0-94,94V521.9a94.12,94.12,0,0,0,94,94h407a94.14,94.14,0,0,0,94-94V291.45a94.13,94.13,0,0,0-94-94ZM602.89,605h-407a83.2,83.2,0,0,1-83.08-83.14V291.45a83.2,83.2,0,0,1,83.08-83.12h407A83.23,83.23,0,0,1,686,291.45V521.9A83.24,83.24,0,0,1,602.89,605Zm-407-382.21a68.69,68.69,0,0,0-68.59,68.62V521.9a68.71,68.71,0,0,0,68.59,68.65h407a68.73,68.73,0,0,0,68.65-68.65V291.45a68.71,68.71,0,0,0-68.65-68.62Z"
              transform="translate(-87.41 -182.94)"
            />
            <circle cx="151.32" cy="115.79" r="26.04" />
            <path d="M586.13,322.88H318.72a24.16,24.16,0,0,1,0-48.31H586.13a24.16,24.16,0,0,1,0,48.31Z" transform="translate(-87.41 -182.94)" />
            <circle cx="151.32" cy="223.02" r="26.04" />
            <path
              d="M526.39,430.84h-.08l-207.66-.72a24.16,24.16,0,0,1,.07-48.31h.08l207.66.72a24.16,24.16,0,0,1-.07,48.31Z"
              transform="translate(-87.41 -182.94)"
            />
            <path d="M390.41,537.35H318.72a24.16,24.16,0,0,1,0-48.31h71.69a24.16,24.16,0,0,1,0,48.31Z" transform="translate(-87.41 -182.94)" />
          </svg>
          <span>{{ comments }}</span>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    mode: {
      // 图片模式
      type: String,
      default: 'vertical'
    },
    img: {
      // 图片
      type: String,
      required: true
    },
    hasIcon: {
      // 是否有标签
      type: [Boolean, Number],
      default: 0
    },
    iconText: {
      // 标签文字
      type: String,
      default: '合集'
    },
    title: {
      // 标题
      type: [String, Number],
      default: ''
    },
    views: {
      // 查看数
      type: [String, Number],
      default: 0
    },
    comments: {
      // 评论数
      type: [String, Number],
      default: 0
    },
    title2rows: {
      // 标题是否占用2行
      type: Boolean,
      default: false
    },
    gid: {
      type: Number,
      default: 0
    }
  },
  methods: {
    toDelOrThePage() {
      if (this.gid === 42) {
        this.$emit('click1');
      } else {
        this.$emit('click');
      }
    }
  }
};
</script>

<style lang="scss" scope>
.article-item-container {
  width: 160px;
  padding: 0;
  margin: 0;
  &:hover {
    cursor: pointer;
  }

  // 图片
  .article-item-img-container {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    border: 1px solid $white-fa;
    overflow: hidden;
    background-color: #fff;
    // background-image: url(http://localhost:2333/static/img/banner.jpg');
    // background-repeat: no-repeat;
    // background-position: center center;
    // background-size: cover;
    // border-radius: 5px;
    &.vertical {
      height: 100px;
    }
    &.horizontal {
      height: 225px !important;
    }
    .el-image {
      height: 100%;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .icon {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }

  // 标题
  .article-item-title {
    line-height: 1.2;
    margin: 10px 0 10px 0;
    .lv-icon {
      padding: 0 5px;
      height: 20px;
      line-height: 20px;
    }
  }

  // 底部信息
  .article-item-infos {
    svg {
      height: 10px;
      fill: $gray;
    }
  }
}
</style>

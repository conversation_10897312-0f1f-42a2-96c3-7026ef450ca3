<template>
  <div>
    <SettingsTopTabbar />
    <div class="form">
      <!-- 现有密码 -->
      <div v-if="reset_pw !== 1" class="form-item flex ai-center">
        <span class="label">{{ $t('settings_password.now_passwd') }}</span>
        <input v-model="model.oldPwd" type="password" class="input-control" :placeholder="$t('settings_password.input_new_password')" />
      </div>

      <!-- 现有密码 -->
      <div class="form-item flex ai-center">
        <span class="label">{{ $t('settings_password.reset_new_password') }}</span>
        <input v-model="model.newPwd" type="password" class="input-control" :placeholder="$t('settings_password.support_16_num_str')" />
      </div>

      <!-- 重复密码 -->
      <div class="form-item flex ai-center">
        <span class="label">{{ $t('settings_password.repeat_password') }}</span>
        <input v-model="model.confirm" type="password" class="input-control" :placeholder="$t('settings_password.confirm_password')" />
      </div>

      <!-- 确定 -->
      <div class="form-item flex ai-center">
        <span class="label"></span>
        <button class="btn btn-submit btn-primary" @click="submit">{{ $t('settings_password.confirm_update') }}</button>
      </div>
    </div>
  </div>
</template>

<script>
import SettingsTopTabbar from '@/components/SettingsTopTabbar';

export default {
  layout: 'settings',
  middleware: 'auth',
  components: {
    SettingsTopTabbar
  },

  async asyncData({ $axios }) {
    const res = await $axios.$post('/api/user/security-info');
    if (res.code === 0) {
      return res.data;
    }
  },

  data: () => ({
    reset_pw: 0, // 重置密码状态 0正常 1新账号允许免密设置密码 2强制重置密码
    model: {
      oldPwd: '', // 原密码
      newPwd: '', // 新密码
      confirm: '' // 确认面貌
    }
  }),

  methods: {
    // 检查数据是否正确
    check() {
      const { oldPwd, newPwd, confirm } = this.model;
      if (this.reset_pw !== 1 && !oldPwd.trim()) {
        this.$message.error('原密码有误');
        return;
      }
      const reg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[\da-zA-Z~!@#$%^&*()_+`\-={}[\];':",./<>?\\|]{8,16}$/;
      if (!reg.test(newPwd)) {
        this.$message.error('密码格式有误');
        return;
      }
      if (newPwd !== confirm) {
        this.$message.error('两次密码不一致');
        return;
      }
      return true;
    },

    async submit() {
      if (this.check()) {
        const params = {
          new_password: this.model.newPwd
        };
        if (this.reset_pw !== 1) {
          params.password = this.model.oldPwd;
        }
        const res = await this.$axios.$post('/api/user/set-password', params);
        if (res.code === 0) {
          this.$message.success(this.$t('tips.update_success'));
          this.$store.dispatch('login/logout');
          this.toPage('/', 'replace');
        }
      }
    }
  },

  head() {
    const title = this.$t('settings_password.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss">
@import '../../../assets/scss/settings.scss';
</style>

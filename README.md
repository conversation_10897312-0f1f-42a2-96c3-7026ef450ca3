# 发布文档

## 安装依赖

```
npm i
```

## 修改 nuxt 服务端运行端口号

- 找到 `/nuxt.config.js` 中的 `server` 选项
- 文档: https://zh.nuxtjs.org/faq/host-port/

```js
server: {
  port: 3001; // 默认 3000
}
```

## 修改 .env 文件

```ini
# 是否开启调试
APP_DEBUG = false

# 运行的域名
APP_HOST = "https://eyw1vb4ttdp1.lightnovel.us"

# 文件上传路径
VUE_APP_UPLOAD_URL = "https://eyw1vb4ttdp1.lightnovel.us:3000/proxy"

# API 请求路径
VUE_APP_BASE_URL = "https://api-dev-lvoq4mx4bnr5.lightnovel.us"
```

## 打包

```
npm run build
```

## 开启服务端运行

```
npm run start
```

## 修改代理请求的地址

- 找到 `/server/proxy.js`, 因为这和 nuxt 不是同一个进程, 所以用 `process.env` 获取不到 `.env` 的配置

```js
const proxy = createProxyMiddleware({
  target: 'http://**************:801', // 修改这个地址
  secure: false,
  changeOrigin: true,
  pathRewrite: {
    '/proxy': '/'
  }
});
```

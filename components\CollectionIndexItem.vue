<template>
  <!-- eslint-disable vue/no-v-html -->
  <div class="collection-index-item-container hover flex-inline vertical" :class="{ horizontal: !vertical }">
    <!-- 左边 cover -->
    <div
      class="radius-5 left-cover vertical"
      :class="{ horizontal: !vertical }"
      :style="{ 'background-image': `url(${cover})` }"
      @click="$emit('cover-click')"
    ></div>

    <!-- 右边 文字 -->
    <div class="right-contents  text-gray fs-sm flex-inline ai-start" @click="$emit('cover-click')">
      <span class="intro vertical" :class="{ horizontal: !vertical }">
        <span class="text-dark">P{{ index > 9 ? index : '0' + index }}</span>
        <span v-html="title"></span>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    vertical: {
      // 布局方式: 垂直(vertical)  水平(horizontal)
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    cover: {
      // cover
      type: String,
      default: ''
    },
    index: {
      // 索引
      type: [String, Number],
      default: '0'
    },
    title: {
      // 简介
      type: String,
      default: ''
    }
  }
};
</script>

<style lang="scss" scope>
.collection-index-item-container {
  &.vertical {
    width: 245px;
  }
  &.horizontal {
    width: 245px !important;
  }
  background-color: #fff;
  .left-cover {
    // background-image: url('http://localhost:2333/static/img/banner.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    &.vertical {
      // height: 124px;
      height: 63px;
      min-width: 45px;
    }
    &.horizontal {
      height: 55px !important;
      min-width: 88px;
    }
  }
  .right-contents {
    padding-left: 20px;
    overflow: hidden;
    line-height: 1.5;
    .intro {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      word-break: break-all;
      &.vertical {
        display: flex;
        flex-direction: column;
        -webkit-line-clamp: 2 !important;
      }
      &.horizontal {
        display: flex;
        flex-direction: column;
        -webkit-line-clamp: 2 !important;
      }
      span {
        &:last-of-type {
          line-height: 22px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}
</style>

<template>
  <div>
    <Nav />
    <div class="layout-container">
      <nuxt />
    </div>
    <Footer />
    <DialogMobileTips v-if="isShowMobileDialog" />
  </div>
</template>

<script>
import Nav from '../components/Nav.vue';
import Footer from '../components/Footer.vue';
import DialogMobileTips from '@/components/DialogMobileTips';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  components: { Nav, Footer, DialogMobileTips },
  data: () => ({
    isShowMobileDialog: false
  }),
  mounted() {
    if (process.browser) {
      this.isShowMobileDialog = this.isMobile(navigator.userAgent) && !sessionStorage.getItem('close_mobile_tips');
    }
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  }
};
</script>

<template>
  <div>
    <!-- 发起评分dialog -->
    <el-dialog
      :title="$t('series.send_rate')"
      :visible="showRateDialog"
      :close-on-click-modal="false"
      width="500px"
      class="rate-dialog"
      @close="toggleRateDialog(false)"
    >
      <!-- 发起评分 -->
      <div class="pad-bottom-30">
        <el-rate v-model="rateStars" />
      </div>
      <div class="flex jc-between ai-center">
        <textarea v-model="rateContent" :minlength="minlen" :maxlength="maxlen" class="input-control flex-1 rate-area"></textarea>
        <span class="fs-sm text-gray">{{ minlen }} - {{ maxlen - rateContent.length }}</span>
      </div>
      <!-- 底部分页 -->
      <div slot="footer" class="flex jc-center ai-center" style="width: 100%">
        <button class="btn btn-primary btn-publish" @click="publishRate">
          {{ $t('series.btn_publish') }}
        </button>
      </div>
    </el-dialog>

    <!-- 顶部内容 -->
    <div id="full-bg" class="full-bg hor" :class="{ ver: vertical }">
      <div class="layout-container collection-info hor" :class="{ ver: vertical }">
        <!--  ==================== 垂直排版 ==================== -->
        <div v-if="vertical" class="collection-container flex">
          <!-- 封面 -->
          <div class="collection-cover" :style="{ 'background-image': `url(${series.cover})` }"></div>

          <div class="collection-infos flex flex-cols jc-between flex-1">
            <!-- 评分 -->
            <div class="rate flex flex-cols jc-center ai-center">
              <span class="rate-num">{{ series.score }}</span>
              <span class="rate-tip fs-xs mar-top-15">{{ $t('series.rate') }}</span>
            </div>

            <!-- 作者信息 -->
            <div class="top-title">
              <h3 class="title fs-md">{{ series.name }}</h3>
              <p class="fs-xs mar-top-20">{{ $t('series.author') }}: {{ series.author }}</p>
              <p class="fs-xs mar-top-10">{{ $t('series.floor_master') }}: {{ series.editors[0].nickname }}</p>
            </div>

            <!-- 底部: 按钮和简介 -->
            <div class="btm-intro-btns">
              <pre class="intro fs-xs say">{{ series.intro }}</pre>
              <div class="btns mar-top-30">
                <button class="btn btn-rate mar-right-20" @click="toggleRateDialog(true)">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 752.69 721.35" class="svg-icon">
                    <path
                      d="M376.51,0a53.94,53.94,0,0,0-49.3,30.64l-70.55,143a68.66,68.66,0,0,1-51.72,37.58L47.2,234.09a55,55,0,0,0-30.47,93.73L130.88,439.13a68.65,68.65,0,0,1,19.75,60.79L123.63,657a55,55,0,0,0,79.77,58l141.15-74.22a68.73,68.73,0,0,1,63.92,0L549.57,715a55,55,0,0,0,79.77-58l-27-157.12a68.68,68.68,0,0,1,19.76-60.79L736.3,327.82a55,55,0,0,0-30.48-93.77L548.08,211.17a68.67,68.67,0,0,1-51.71-37.57l-70.56-143A53.94,53.94,0,0,0,376.51,0Z"
                      transform="translate(-0.19 0)"
                    ></path>
                  </svg>
                  <span>{{ $t('series.btn_rate') }}</span>
                </button>
                <button class="btn btn-collect" @click="addCollection">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 773.88 737.51" class="svg-icon" :class="{ active: series.already_fav }">
                    <path
                      d="M399.81,768.76a50.85,50.85,0,0,1-24.19-6.31l-2.17-1.12a892.13,892.13,0,0,1-191.1-134.22A595.27,595.27,0,0,1,66.26,482.33c-34.15-59.25-52.57-127.77-53.2-197.86C13,184.09,66,92.75,148.94,50.23S329.78,41.13,400,105.56C470.22,41.13,568.12,7.71,651.06,50.23S787,184.09,786.94,284.47c-.62,70.11-19,138.65-53.2,197.92A595.8,595.8,0,0,1,617.66,627.16,891.66,891.66,0,0,1,426.6,761.33l-2.17,1.12a50.89,50.89,0,0,1-24.62,6.31Zm0,0"
                      transform="translate(-13.06 -31.24)"
                    />
                  </svg>
                  <span>{{ $t('series.btn_favorite') }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- ==================== 水平排版 ==================== -->
        <div v-else class="collection-container flex flex-cols hor">
          <div class="flex">
            <!-- 封面 -->
            <div class="collection-cover" :style="{ 'background-image': `url(${series.cover})` }"></div>
            <div class="collection-infos flex flex-cols jc-between flex-1">
              <!-- 评分 -->
              <div class="rate flex flex-cols jc-center ai-center">
                <span class="rate-num">{{ series.score }}</span>
                <span class="rate-tip fs-xs mar-top-15">{{ $t('series.rate') }}</span>
              </div>

              <!-- 作者信息 -->
              <div class="top-title">
                <h3 class="title fs-lg">{{ series.name }}</h3>
                <p class="fs-xs mar-top-20">{{ $t('series.author') }}: {{ series.author }}</p>
                <p class="fs-xs mar-top-10">{{ $t('series.floor_master') }}: {{ series.editors[0].nickname }}</p>
              </div>

              <!-- 底部: 按钮和简介 -->
              <div class="btm-intro-btns">
                <div class="btns mar-top-30">
                  <button class="btn btn-rate mar-right-20" @click="toggleRateDialog(true)">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 752.69 721.35" class="svg-icon">
                      <path
                        d="M376.51,0a53.94,53.94,0,0,0-49.3,30.64l-70.55,143a68.66,68.66,0,0,1-51.72,37.58L47.2,234.09a55,55,0,0,0-30.47,93.73L130.88,439.13a68.65,68.65,0,0,1,19.75,60.79L123.63,657a55,55,0,0,0,79.77,58l141.15-74.22a68.73,68.73,0,0,1,63.92,0L549.57,715a55,55,0,0,0,79.77-58l-27-157.12a68.68,68.68,0,0,1,19.76-60.79L736.3,327.82a55,55,0,0,0-30.48-93.77L548.08,211.17a68.67,68.67,0,0,1-51.71-37.57l-70.56-143A53.94,53.94,0,0,0,376.51,0Z"
                        transform="translate(-0.19 0)"
                      ></path>
                    </svg>
                    <span>{{ $t('series.btn_rate') }}</span>
                  </button>
                  <button class="btn btn-collect" @click="addCollection">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 773.88 737.51" class="svg-icon" :class="{ active: series.already_fav }">
                      <path
                        d="M399.81,768.76a50.85,50.85,0,0,1-24.19-6.31l-2.17-1.12a892.13,892.13,0,0,1-191.1-134.22A595.27,595.27,0,0,1,66.26,482.33c-34.15-59.25-52.57-127.77-53.2-197.86C13,184.09,66,92.75,148.94,50.23S329.78,41.13,400,105.56C470.22,41.13,568.12,7.71,651.06,50.23S787,184.09,786.94,284.47c-.62,70.11-19,138.65-53.2,197.92A595.8,595.8,0,0,1,617.66,627.16,891.66,891.66,0,0,1,426.6,761.33l-2.17,1.12a50.89,50.89,0,0,1-24.62,6.31Zm0,0"
                        transform="translate(-13.06 -31.24)"
                      />
                    </svg>
                    <span>{{ $t('series.btn_favorite') }}</span>
                  </button>
                </div>
              </div>
            </div>
            <!--  -->
          </div>
          <div class="mar-top-25">
            <p class="intro fs-xs hor">{{ series.intro }}</p>
          </div>
        </div>
      </div>
    </div>
    <!-- =============== 底部目录|评分 =============== -->
    <div class="layout-container btm-contents">
      <div class="area-title flex jc-between">
        <p class="fs-ml flex-1">{{ $t('series.all_article') }}</p>
        <!-- icons -->
        <div class="icons-text-img-container flex jc-between">
          <!-- <svg
            :class="{ active: series_view_index === 1 }"
            class="hover img-icon svg-icon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 551.5 547"
            @click="viewChange(1)"
          >
            <path
              d="M303.8,126.9H181.2a57,57,0,0,0-57,57V306.6a57,57,0,0,0,57,57H303.9a57,57,0,0,0,57-57V183.9A57.12,57.12,0,0,0,303.8,126.9Z"
              transform="translate(-124.2 -126.9)"
            />
            <path
              d="M618.7,126.9H496a57,57,0,0,0-57,57V306.6a57,57,0,0,0,57,57H618.7a57,57,0,0,0,57-57V183.9A57,57,0,0,0,618.7,126.9Z"
              transform="translate(-124.2 -126.9)"
            />
            <path
              d="M303.8,437.2H181.2a57,57,0,0,0-57,57V616.9a57,57,0,0,0,57,57H303.9a57,57,0,0,0,57-57V494.2A57.12,57.12,0,0,0,303.8,437.2Z"
              transform="translate(-124.2 -126.9)"
            />
            <path
              d="M618.7,437.2H496a57,57,0,0,0-57,57V616.9a57,57,0,0,0,57,57H618.7a57,57,0,0,0,57-57V494.2A57,57,0,0,0,618.7,437.2Z"
              transform="translate(-124.2 -126.9)"
            />
          </svg> -->
          <svg
            :class="{ active: series_view_index === 1 }"
            class="hover img-icon svg-icon"
            viewBox="0 0 1063 1024"
            xmlns="http://www.w3.org/2000/svg"
            @click="viewChange(1)"
          >
            <path
              d="M0 0m68.4882 0l147.467087 0q68.4882 0 68.4882 68.4882l0 123.025873q0 68.4882-68.4882 68.4882l-147.467087 0q-68.4882 0-68.4882-68.4882l0-123.025873q0-68.4882 68.4882-68.4882Z"
            ></path>
            <path
              d="M389.002973 0m68.488201 0l536.192161 0q68.4882 0 68.4882 68.4882l0 123.025873q0 68.4882-68.4882 68.4882l-536.192161 0q-68.4882 0-68.488201-68.4882l0-123.025873q0-68.4882 68.488201-68.4882Z"
            ></path>
            <path
              d="M0 381.332962m68.4882 0l147.467087 0q68.4882 0 68.4882 68.4882l0 123.025873q0 68.4882-68.4882 68.4882l-147.467087 0q-68.4882 0-68.4882-68.4882l0-123.025873q0-68.4882 68.4882-68.4882Z"
            ></path>
            <path
              d="M389.002973 381.332962m68.488201 0l536.192161 0q68.4882 0 68.4882 68.4882l0 123.025873q0 68.4882-68.4882 68.4882l-536.192161 0q-68.4882 0-68.488201-68.4882l0-123.025873q0-68.4882 68.488201-68.4882Z"
            ></path>
            <path
              d="M0 381.332962m68.4882 0l147.467087 0q68.4882 0 68.4882 68.4882l0 123.025873q0 68.4882-68.4882 68.4882l-147.467087 0q-68.4882 0-68.4882-68.4882l0-123.025873q0-68.4882 68.4882-68.4882Z"
            ></path>
            <path
              d="M0 762.679819m68.4882 0l147.467087 0q68.4882 0 68.4882 68.4882l0 123.025873q0 68.4882-68.4882 68.4882l-147.467087 0q-68.4882 0-68.4882-68.4882l0-123.025873q0-68.4882 68.4882-68.4882Z"
            ></path>
            <path
              d="M389.002973 762.679819m68.488201 0l536.192161 0q68.4882 0 68.4882 68.4882l0 123.025873q0 68.4882-68.4882 68.4882l-536.192161 0q-68.4882 0-68.488201-68.4882l0-123.025873q0-68.4882 68.488201-68.4882Z"
            ></path>
            <path
              d="M0 762.679819m68.4882 0l147.467087 0q68.4882 0 68.4882 68.4882l0 123.025873q0 68.4882-68.4882 68.4882l-147.467087 0q-68.4882 0-68.4882-68.4882l0-123.025873q0-68.4882 68.4882-68.4882Z"
            ></path>
          </svg>
          <!-- <svg
            :class="{ active: series_view_index === 0 }"
            class="hover text-icon svg-icon"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 758.4 549"
            @click="viewChange(0)"
          >
            <circle cx="53.6" cy="53.6" r="53.6" />
            <path d="M729,228.8H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
            <circle cx="53.6" cy="274.5" r="53.6" />
            <circle cx="53.6" cy="495.4" r="53.6" />
            <path d="M729,449.7H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
            <path d="M729,670.6H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
          </svg> -->

          <svg
            :class="{ active: series_view_index === 0 }"
            class="hover text-icon svg-icon"
            viewBox="0 0 1063 1024"
            xmlns="http://www.w3.org/2000/svg"
            @click="viewChange(0)"
          >
            <path
              d="M0 0m68.485774 0l147.461862 0q68.485774 0 68.485774 68.485774l0 147.447968q0 68.485774-68.485774 68.485773l-147.461862 0q-68.485774 0-68.485774-68.485773l0-147.447968q0-68.485774 68.485774-68.485774Z"
            ></path>
            <path
              d="M388.989192 0m68.485774 0l147.461862 0q68.485774 0 68.485774 68.485774l0 147.447968q0 68.485774-68.485774 68.485773l-147.461862 0q-68.485774 0-68.485774-68.485773l0-147.447968q0-68.485774 68.485774-68.485774Z"
            ></path>
            <path
              d="M777.978384 0m68.485773 0l147.461863 0q68.485774 0 68.485773 68.485774l0 147.447968q0 68.485774-68.485773 68.485773l-147.461863 0q-68.485774 0-68.485773-68.485773l0-147.447968q0-68.485774 68.485773-68.485774Z"
            ></path>
            <path
              d="M0 369.106225m68.485774 0l147.461862 0q68.485774 0 68.485774 68.485774l0 147.447968q0 68.485774-68.485774 68.485774l-147.461862 0q-68.485774 0-68.485774-68.485774l0-147.447968q0-68.485774 68.485774-68.485774Z"
            ></path>
            <path
              d="M388.989192 369.106225m68.485774 0l147.461862 0q68.485774 0 68.485774 68.485774l0 147.447968q0 68.485774-68.485774 68.485774l-147.461862 0q-68.485774 0-68.485774-68.485774l0-147.447968q0-68.485774 68.485774-68.485774Z"
            ></path>
            <path
              d="M777.978384 369.106225m68.485773 0l147.461863 0q68.485774 0 68.485773 68.485774l0 147.447968q0 68.485774-68.485773 68.485774l-147.461863 0q-68.485774 0-68.485773-68.485774l0-147.447968q0-68.485774 68.485773-68.485774Z"
            ></path>
            <path
              d="M0 738.21245m68.485774 0l147.461862 0q68.485774 0 68.485774 68.485774l0 147.447968q0 68.485774-68.485774 68.485774l-147.461862 0q-68.485774 0-68.485774-68.485774l0-147.447968q0-68.485774 68.485774-68.485774Z"
            ></path>
            <path
              d="M388.989192 738.21245m68.485774 0l147.461862 0q68.485774 0 68.485774 68.485774l0 147.447968q0 68.485774-68.485774 68.485774l-147.461862 0q-68.485774 0-68.485774-68.485774l0-147.447968q0-68.485774 68.485774-68.485774Z"
            ></path>
            <path
              d="M777.978384 738.21245m68.485773 0l147.461863 0q68.485774 0 68.485773 68.485774l0 147.447968q0 68.485774-68.485773 68.485774l-147.461863 0q-68.485774 0-68.485773-68.485774l0-147.447968q0-68.485774 68.485773-68.485774Z"
            ></path>
          </svg>
        </div>
      </div>

      <!-- ==================== 合集目录 ==================== -->
      <div class="collection-index">
        <!-- tabbar: 切换标签 -->
        <div class="index-tabbars flex">
          <!-- <span
            v-for="(item, index) in tabbars"
            :key="index"
            :class="{ active: tabbarIndex === index }"
            class="tab-item hover"
            @click="tabChange(index)"
          >
            <span v-if="item.start === item.end">{{ item.start }}</span>
            <span v-else>{{ item.start }}-{{ item.end }}</span>
          </span> -->
          <div v-if="series_view_index === 0">
            <span
              v-for="(item, index) in otherTabbars"
              :key="index"
              :class="{ active: tabbarIndex === index }"
              class="tab-item hover"
              @click="tabChange(index)"
            >
              <span v-if="item.start === item.end">{{ item.start }}</span>
              <span v-else>{{ item.start }}-{{ item.end }}</span>
            </span>
          </div>
          <div v-if="series_view_index === 1">
            <span
              v-for="(item, index) in tabbars"
              :key="index"
              :class="{ active: tabbarIndex === index }"
              class="tab-item hover"
              @click="tabChange(index)"
            >
              <span v-if="item.start === item.end">{{ item.start }}</span>
              <span v-else>{{ item.start }}-{{ item.end }}</span>
            </span>
          </div>
        </div>

        <!-- tabbar: 切换对应的区域 -->
        <!-- 文字 -->
        <div v-show="series_view_index === 0">
          <div v-for="(page, index) in otherPages" :key="index">
            <div v-show="tabbarIndex === index" class="item-container">
              <!-- <div v-for="item in page" :key="item.aid" class="text-series-item">
                <TextCollectionIndexItem
                  :title="`${item.title}`"
                  :comments="item.comments"
                  :views="item.hits"
                  :time="item.last_time | date2short"
                  @title-click="toPage(`/detail/${item.aid}`)"
                />
              </div> -->
              <div
                v-for="(item, idx) in page"
                :key="idx"
                class="item-box"
                :class="{ isRead: item.aid === detailId }"
                @click="toPage(`/detail/${item.aid}`)"
              >
                P{{ index * otherSize + idx + 1 > 9 ? index * otherSize + idx + 1 : '0' + (index * otherSize + idx + 1) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 图文混排 -->
        <div v-show="series_view_index === 1">
          <div v-for="(page, index) in pages" :key="`img-${index}`">
            <div v-show="tabbarIndex === index" class="flex flex-wrap">
              <div v-for="(item, key) in page" :key="key" class="mar-bottom-20 pos-mode" :class="{ 'max-w-item': vertical }">
                <CollectionIndexItem
                  :vertical="vertical"
                  :index="index * pageSize + key + 1"
                  :cover="item.cover"
                  :title="`${item.title}`"
                  @cover-click="toPage(`/detail/${item.aid}`)"
                />
                <div v-if="item.aid === detailId" class="mode" :class="{ horizontal: !vertical }">
                  <span class="mode-txt">{{ $t('series.last_read') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- ==================== 合集评分 ==================== -->
      <div v-if="comments.length">
        <p id="all-rates" class="fs-ml area-title">{{ $t('series.all_comments') }}</p>
        <ul class="collection-rates">
          <li v-for="(item, index) in comments" :key="index" class="rate-item flex">
            <div class="avatar-box">
              <userAvatar :avatar-img="item.user_info.avatar" />
            </div>
            <div class="comment-box flex-1">
              <CommentItem
                :username="item.user_info.nickname"
                :lv="item.user_info.level.level"
                :lv-name="item.user_info.level.name"
                :content="item.text"
                :date="item.time | date2short"
                :zans="item.likes"
                :rate="item.rate"
                :zaned="item.already_like"
                :medal="item.user_info.medals ? item.user_info.medals[0] : {}"
                :show-reply="false"
                :show-floor="false"
                @zan="zanRate(item, index)"
              />
            </div>
          </li>
        </ul>
      </div>

      <!-- 合集分页 -->
      <el-pagination
        class="my-pagination"
        layout="prev, pager, next, jumper"
        :prev-text="$t('paginate.prev')"
        :next-text="$t('paginate.next')"
        :disabled="loading"
        :hide-on-single-page="true"
        :current-page="paginate.cur"
        :total="paginate.count"
        :page-size="paginate.size"
        @current-change="onPageChange"
      />
    </div>
    <!-- 底部结束 -->
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import CommentItem from '@/components/CommentItem.vue';
import CollectionIndexItem from '@/components/CollectionIndexItem.vue';
// import TextCollectionIndexItem from '@/components/TextCollectionIndexItem.vue';
import UserAvatar from '@/components/UserAvatar.vue';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  layout: 'collection',
  components: {
    // TextCollectionIndexItem,
    CollectionIndexItem,
    CommentItem,
    UserAvatar
  },
  async asyncData({ $axios, params }) {
    const sid = params.id;
    const data = { sid };
    const seriesRes = await $axios.$post('/api/series/get-info', {
      sid
    });
    if (seriesRes.code === 0) {
      data.series = seriesRes.data;
      // data.series.intro = data.series.intro.replace(/[\r\n]/g, '');
      // data.series.intro = data.series.intro.replace(/\s+/g, '\n');
    }

    const commentsRes = await $axios.$post('/api/series/get-rate-list', {
      sid,
      page: 1
    });
    if (commentsRes.code === 0) {
      data.comments = commentsRes.data.list || [];
      data.paginate = commentsRes.data.page_info || {};
    }
    const vertical = (data.vertical = Boolean(data.series.cover_type));
    // 处理分页
    const articlesLength = data.series.articles.length;
    const pages = [];
    const tabbars = [];
    const otherPages = [];
    const otherTabbars = [];
    if (articlesLength) {
      const pageSize = vertical ? 12 : 12;
      // 新增48的分页
      const otherSize = 48;
      data.pageSize = pageSize;
      data.otherSize = otherSize;
      if (articlesLength < pageSize) {
        pages.push(data.series.articles);
        tabbars.push({ start: 1, end: articlesLength });
      } else {
        for (let i = 0, key = 1, start, end; i < articlesLength; i += pageSize) {
          start = i || 1;
          end = i + pageSize;
          if (i > 1) start += 1;
          tabbars.push({ start, end });
          pages.push(data.series.articles.slice(i, pageSize * key));
          key++;
        }
      }
      if (articlesLength < otherSize) {
        otherPages.push(data.series.articles);
        otherTabbars.push({ start: 1, end: articlesLength });
      } else {
        for (let i = 0, key = 1, start, end; i < articlesLength; i += otherSize) {
          start = i || 1;
          end = i + otherSize;
          if (i > 1) start += 1;
          otherTabbars.push({ start, end });
          otherPages.push(data.series.articles.slice(i, otherSize * key));
          key++;
        }
      }
      // 处理最后一个tabbar也许不足指定数量
      tabbars[tabbars.length - 1].end = articlesLength;
      otherTabbars[otherTabbars.length - 1].end = articlesLength;
    }
    data.pages = pages;
    data.tabbars = tabbars;

    data.otherPages = otherPages;
    data.otherTabbars = otherTabbars;
    return data;
  },
  data: () => ({
    pageSize: 12, // 每页多少条数据
    otherSize: 48, // 文字类型分页
    loading: false, // 是否在就加载
    paginate: {}, // 分页信息
    sid: 0, // 合集id
    tabbars: [], // tabbar分页
    comments: [], // 评分
    series: {}, // 合集信息
    vertical: false, // 是否是竖排封面
    minlen: 20, // 最少输入 50 个字符
    maxlen: 1000, // 最多输入 1000 个字符
    rateStars: 0, // 评分的星星数量
    rateContent: '', // 评分内容
    showRateDialog: false, // 是否显示评分弹框
    tabbarIndex: 0, // 当前tabbar对应的索引
    detailId: 0 // 缓存下来的帖子id
  }),

  computed: {
    ...mapGetters('login', ['loginUser', 'isLogin']),
    ...mapState('viewTab', ['series_view_index'])
  },

  watch: {
    isLogin(val) {
      val && this.addHistory();
    }
  },

  mounted() {
    // 缓存合集记录，如果大于50条则删除前10条
    if (storage.get('last_read') && storage.get('last_read').length > 50) {
      const lastRead1 = storage.get('last_read');
      lastRead1.splice(0, 10);
      storage.set('last_read', lastRead1);
    }

    if (process.browser) {
      this.inItReadDetail();
      this.initFullBgURL(this.series.cover);
      this.$nextTick(this.addHistory);
    }
  },

  methods: {
    // 初始化读服务器和缓存的上一次阅读
    inItReadDetail() {
      // 如果为0就读本地,非0就读服务器的
      if (this.series && this.series.user_read && this.series.user_read.last_aid === 0) {
        // 如果有缓存的detailId应该先读出来，没有则过
        if (storage.get('last_read') && storage.get('last_read').find((item) => item.seriesId === this.series.sid)) {
          this.detailId = storage.get('last_read').find((item) => item.seriesId === this.series.sid).detailId;
        }
      } else if (this.series && this.series.user_read) {
        this.detailId = this.series.user_read.last_aid;
      } else if (this.series) {
        // 未登录的状态,就读本地的
        if (storage.get('last_read') && storage.get('last_read').find((item) => item.seriesId === this.series.sid)) {
          this.detailId = storage.get('last_read').find((item) => item.seriesId === this.series.sid).detailId;
        }
      }
      // 初始化判断方法
      this.chickView();
    },
    // 初始化判断方法
    chickView() {
      if (storage.get('last_read') && storage.get('last_read').find((item) => item.seriesId === this.series.sid)) {
        const id = storage.get('last_read').find((item) => item.seriesId === this.series.sid).detailId;
        if (storage.get('vuex').viewTab.series_view_index === 0 && this.otherPages && this.otherPages.length > 1) {
          this.otherPages.forEach((el, index) => {
            el.forEach((els, idx) => {
              if (els.aid === id) {
                this.tabbarIndex = index;
              }
            });
          });
        } else if (storage.get('vuex').viewTab.series_view_index === 1 && this.pages && this.pages.length > 1) {
          this.pages.forEach((el, index) => {
            el.forEach((els, idx) => {
              if (els.aid === id) {
                this.tabbarIndex = index;
              }
            });
          });
        }
      }
    },
    // 切换视图
    viewChange(index) {
      this.$store.commit('viewTab/setViewIndex', { key: 'series_view_index', val: index });
      this.chickView();
    },

    // 增加历史记录
    addHistory() {
      this.isLogin && this.$axios.post('/api/history/add-history', { fid: this.series.sid, class: 2 });
    },

    // 点赞评分
    zanRate(item, index) {
      this.loginNext(() => {
        if (item.already_like) {
          return;
        }
        const params = {
          sid: this.sid,
          uid: this.loginUser.uid
        };
        this.$axios.$post('/api/series/like', params).then((res) => {
          if (res.code === 0) {
            const curRate = this.comments[index];
            curRate.likes++;
            curRate.already_like = true;
          }
        });
      });
    },

    // 页面切换
    onPageChange(page) {
      this.loading = true;
      this.$axios
        .$post('/api/series/get-rate-list', { sid: this.sid, page })
        .then((res) => {
          if (res.code === 0) {
            this.comments = res.data.list;
            this.paginate = res.data.page_info;
          }
        })
        .finally(() => {
          this.loading = false;
          this.$nextTick(() => document.querySelector('#all-rates').scrollIntoView());
        });
    },

    // 收藏合集
    addCollection() {
      this.loginNext(() => {
        if (this.series.already_fav) {
          this.$message.info(this.$t('series.already_fav'));
          return;
        }
        this.$store
          .dispatch('user/addCollection', {
            _class: 2,
            fid: this.sid
          })
          .then(() => {
            this.$message.success(this.$t('series.fav_success'));
            this.series.already_fav = true;
          })
          .catch(() => this.$message.error(this.$t('series.fav_fail')));
      });
    },

    // 刷新评分数据
    async refreshRates() {
      const res = await this.$axios.$post('/api/series/get-rate-list', {
        sid: this.sid,
        page: 1
      });

      if (res.code === 0) {
        this.comments = res.data.list;
        this.paginate = res.data.page_info;
      }
    },

    // 发布评分
    publishRate() {
      this.loginNext(async () => {
        const rate = this.rateStars * 2;
        if (!rate) {
          this.$message.error(this.$t('series.please_select_rate'));
          return;
        }
        const text = this.rateContent.trim();
        if (!text) {
          this.$message.error(this.$t('series.please_input_content'));
          return;
        }
        if (text.length < this.minlen) {
          this.$message.error(this.$t('series.min_content_50_charts'));
          return;
        }
        const { sid } = this.series;
        const res = await this.$axios.$post('/api/series/rate', {
          sid,
          rate,
          text
        });
        if (res.code === 0) {
          this.series.already_rate = true;
          this.$message.success(this.$t('series.rate_success'));
          this.refreshRates();
          this.toggleRateDialog(false);
        } else {
          this.$message.error(this.$t('series.rate_fail'));
        }
      });
    },

    // 切换评分 dialog 是否显示
    toggleRateDialog(flag) {
      if (flag && this.series.already_rate) {
        this.$message.info(this.$t('series.already_rate'));
        return;
      }
      this.showRateDialog = flag;
    },

    // 切换 tabbar
    tabChange(i) {
      this.tabbarIndex = i;
    },

    // 设置全屏的模糊背景
    initFullBgURL(img) {
      const style = document.createElement('style');
      style.type = 'text/css';
      document.head.appendChild(style);
      const styleRule = `#full-bg::before{background-image: url(${img})}`;
      style.sheet.insertRule(styleRule, 0);
    }
  },

  head() {
    const title = this.series.name + '-' + this.$t('title');
    const baseKeywords = this.$t('base_metas.keywords');
    const author = this.series.author;
    return {
      title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.series.intro
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `${this.series.name}, ${author}, ${baseKeywords}`
        },
        {
          hid: 'author',
          name: 'author',
          content: author
        }
      ]
    };
  }
};
</script>

<style lang="scss" scope>
.rate-dialog {
  .rate-area {
    padding: 10px;
    height: 80px;
    resize: none;
  }
  .btn-publish {
    width: 140px;
    height: 40px;
    border-radius: 40px;
  }
}
.full-bg {
  &.hor {
    height: 338px;
  }
  &.ver {
    height: 460px;
  }
  width: 100%;
  background-color: rgba(#000, 0.25);
  position: relative;
  color: #fff;
  overflow: hidden;
  &::before {
    // background-image: url('http://localhost:2333/static/img/banner3.jpg');
    background-position: top center;
    background-repeat: no-repeat;
    // background-size: 100% auto;
    background-size: cover;
    background-position: center center;
    z-index: -1;
    content: '';
    filter: blur(25px);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  .collection-info {
    color: #fff;
    position: absolute;
    top: 100px;
    left: 50%;
    transform: translateX(-50%);
    &.hor {
      height: 215px;
    }
    &.ver {
      height: 338px;
    }

    // 合集信息
    .collection-container {
      height: 100%;

      &.hor {
        // 水平排版: 封面
        .collection-cover {
          height: 150px;
        }
      }
      .say {
        width: 890px;
        white-space: pre-line;
        margin-top: 5px;
      }
      // 简介
      .intro {
        line-height: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 5;
        &.hor {
          -webkit-line-clamp: 2 !important;
        }
      }

      .collection-cover {
        // 封面
        border: 2px solid #fff;
        width: 240px;
        height: 100%;
        background-color: #fff;
        // background-image: url('http://localhost:2333/static/img/banner.jpg');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
        border-radius: 5px;
        overflow: hidden;
      }
      .collection-infos {
        // 右边信息
        position: relative;
        padding-left: 30px;
        width: 100%;
        // height: 100%;
        .top-title {
          .title {
            line-height: 1.2;
            padding-right: 60px;
          }
        }
        .rate {
          // 评分
          position: absolute;
          top: 0;
          right: 0;
          &-num {
            font-size: 40px;
            color: #ffc24b;
          }
        }

        // 底部按钮
        .btm-intro-btns {
          .btns {
            margin-top: 5px;
            .btn-rate,
            .btn-collect {
              display: inline-flex;
              justify-content: center;
              align-items: center;
              width: 110px;
              height: 35px;
              border-radius: 35px;
              background-color: rgba(255, 255, 255, 0.5);
              color: #fff;
              .svg-icon {
                fill: #fff;
                margin-right: 5px;
                width: 15px;
                &.active {
                  fill: $primary !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

// ----- 索引 -----
.btm-contents {
  .area-title {
    padding: 20px 0;
    .icons-text-img-container {
      height: 100%;
      width: 50px;
      .svg-icon {
        fill: $gray;
        &.active {
          fill: $primary !important;
        }
      }
      .text-icon {
        height: 18px;
      }
      .img-icon {
        height: 18px;
      }
    }
  }

  // 合集索引
  .collection-index {
    padding-bottom: 20px;
    border-bottom: 1px solid $gray-white;
    .text-series-item {
      border-bottom: 1px solid $gray-white;
      margin-bottom: 20px;
    }
    .max-w-item {
      max-width: 290px;
    }
    .index-tabbars {
      padding: 30px 0 20px 0;
      .tab-item {
        margin-right: 30px;
        min-width: 50px;
        &.active {
          color: $primary;
        }
      }
    }
    .item-container {
      display: flex;
      width: 1180px;
      font-size: 15px;
      flex-wrap: wrap;
      line-height: 7px;
      .item-box {
        width: 82px;
        height: 50px;
        background: #fcfcfc;
        border-radius: 5px;
        padding: 20px 29px;
        margin-right: 16px;
        margin-bottom: 15px;
        cursor: pointer;
        &:hover {
          color: $primary;
        }
        &:active {
          color: $primary;
        }
      }
    }
  }

  // 合集屏风
  .collection-rates {
    .rate-item {
      margin-bottom: 30px;
      .comment-box {
        padding-left: 20px;
        border-bottom: 1px solid $gray-white;
        padding-bottom: 20px;
      }
    }
  }
}
// 帖子遮罩
.mode {
  position: absolute;
  width: 45px;
  top: 0;
  height: 63px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.3);
  padding-top: 10px;
  cursor: pointer;
  &.vertical {
    height: 63px;
    min-width: 45px;
  }
  &.horizontal {
    height: 55px !important;
    min-width: 88px;
    padding-top: 14px;
    padding-left: 4px;
  }
  .mode-txt {
    color: white;
    padding: 4px 6px;
    display: inline-block;
  }
}
.pos-mode {
  position: relative;
  background: #fcfcfc;
  margin-bottom: 15px;
  margin-right: 45px;
}
.isRead {
  color: $primary;
}
</style>

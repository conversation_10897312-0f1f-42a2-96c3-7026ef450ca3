<template>
  <!-- eslint-disable vue/no-v-html -->
  <div class="guild-container">
    <div class="guild-left">
      <div class="img-box imgWrap">
        <img src="@/assets/images/gonghuidating.png" />
      </div>
      <nuxt-link
        v-for="(item, idx) in partitiontMenu"
        :key="idx"
        :class="{ active: Number($route.params.id) === item.gid }"
        :to="{
          path: $i18n.path(`guildhall/${item.gid}`),
          params: { id: item.gid }
        }"
      >
        <div class="img-box1 imgWrap">
          <img :src="item.pic" alt="" />
        </div>
        <span>{{ item.name }}</span>
      </nuxt-link>
    </div>
    <div class="guild-right">
      <!-- 发帖区域 -->
      <div class="comment-list-container">
        <!-- 输入内容 -->
        <div class="comment-list-item flex">
          <div v-if="isLogin" class="flex-1 hover" @click="toPage(`/profile/${loginUser.uid}`)">
            <UserAvatar :avatar-img="loginUser.avatar" />
          </div>
          <div class="comment-list-item-content">
            <CommentInput
              ref="commentinput"
              btn-box-bottom-pad="30px"
              btn-box-top-pad="30px"
              upload-url="/upload/article-image"
              :pleaseholder="$t('guildhall.input_content')"
              :btn-content="$t('guildhall.publish_btn')"
              :show-image-btn="false"
              :show-other-image-btn="true"
              :show-emoji-btn="false"
              :show-vote-btn="true"
              :show-add-btn="true"
              :article-num="2000"
              :islogin="isLogin"
              @confirm="publishComment"
              @poll="getPoll"
              @additional="getAdditional"
              @images="getImages"
            >
              <template>
                <span slot="cacheDrafts" class="cache-content">
                  {{ $t('guildhall.to_cache') }}
                </span>
              </template>
            </CommentInput>
          </div>
        </div>
        <div v-if="true" class="">
          <!-- 已经上传的内容图片 -->
          <ul v-show="sortContentImages" class="uploaded-imgs-wrapper flex jc-between flex-wrap">
            <li v-for="item in sortContentImages" :key="item.id" class="img-item-wrapper flex jc-start ai-center hover">
              <div class="btn-box hover" @click="removeContentImg(item.id)">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 800 800">
                  <path
                    d="M447.5,400l307.2-312c7.2-7.3,11.1-16.9,11.1-27.1c0-10.2-4-19.8-11.1-27.1c-7.4-7.5-17.4-11.7-28.1-11.7
	c-10.7,0-20.7,4.2-28.1,11.7L400,336.9L101.4,33.7C93.9,26.1,84,22,73.3,22s-20.7,4.2-28.1,11.7c-14.7,14.9-14.7,39.3,0,54.2
	L352.5,400L45.3,712.1c-7.2,7.3-11.1,16.9-11.1,27.1c0,10.2,4,19.8,11.1,27.1c7.4,7.5,17.4,11.7,28.1,11.7
	c10.7,0,20.7-4.2,28.1-11.7L400,463.1l298.6,303.3c7.4,7.5,17.4,11.7,28.1,11.7s20.7-4.2,28.1-11.7c14.7-14.9,14.7-39.3,0-54.2
	L447.5,400z"
                  />
                </svg>
              </div>
              <img :src="item.url" />
              <p class="flex-1 dis-select text-gray text-hide-1" @click="appendImgToContents(item.url)">{{ item.name }}</p>
            </li>
          </ul>
        </div>
        <!-- 锚点跳转的回复位置 -->
        <div id="comments"></div>
        <!-- 开始: 帖子列表  -->
        <Loading v-show="loadingComments" />
        <div v-if="guildMsg.list && guildMsg.list.length && !loadingComments">
          <div v-for="(item, idx) in guildMsg.list" :key="idx" class="comment-container">
            <div class="comment-box">
              <div class="box-top">
                <div class="top-left">
                  <div class="hover avatar imgWrap mar-right-5" @click="toPage(`/profile/${item.author.uid}`)">
                    <img :src="item.avatar" alt="" />
                  </div>
                  <span class="top-time mar-right-5">{{ item.time | date1short }}</span>
                  <div class="top-title mar-right-5">{{ item.author.nickname }}</div>
                  <div class="lv-icon  mar-right-5" :class="[`lv-${item.author.level.level}`]">{{ item.author.level.name }}</div>
                  <div v-for="(medItem, medIdx) in item.author.medals.slice(0, 5)" :key="medIdx" class="top-img imgWrap mar-right-5">
                    <img :src="medItem.img" alt="" />
                  </div>
                </div>
                <div class="top-right">
                  <div class="top-right-content">
                    <div v-if="item.only_passer === 1" class="img-box imgWrap" :title="$t('guildhall.brave')">
                      <img src="@/assets/images/yongzhe.png" />
                    </div>
                    <div v-if="item.has_reward === 1" class="img-box imgWrap" :title="$t('guildhall.re_back')">
                      <img src="@/assets/images/jinbi.png" />
                    </div>
                    <div v-if="item.has_pay === 1" class="img-box imgWrap" :title="$t('guildhall.pay')">
                      <img src="@/assets/images/fufei.png" />
                    </div>
                    <div v-if="item.is_top === 1" class="img-box imgWrap" :title="$t('guildhall.stick')">
                      <img src="@/assets/images/zhiding.png" />
                    </div>
                  </div>
                  <div class="right-end">
                    <span @click="toThemeReply(item.aid)">{{ $t('guildhall.reply') }}</span>
                    <span v-if="false" @click="deleteArticleConfirm(item)">{{ $t('guildhall.del') }}</span>
                  </div>
                </div>
              </div>
              <div class="box-bottom">
                <!-- 评论内容 -->
                <div class="comment-content">
                  <article v-html="item.summary"></article>
                </div>
                <!-- 图片内容-->
                <div v-if="item.images && item.images.length > 0" class="comment-images">
                  <div v-for="(imgUrl, key) in item.images" :key="key" class="img-summary">
                    <img :src="imgUrl.url" />
                  </div>
                </div>
                <!-- 回复内容 -->
                <div v-if="item.reply_list && item.reply_list.length > 0" class="reply-content">
                  <div v-for="(repltItem, idx1) in item.reply_list" :key="idx1" class="reply-box">
                    <div class="flex rtop-cont-box">
                      <div class="rtop-box">
                        <span class="r-name mar-right-5 btn" @click="toPage(`/profile/${repltItem.user_info.uid}`)">{{
                          repltItem.user_info.nickname
                        }}</span>
                        <div class="lv-icon mar-right-5" :class="[`lv-${repltItem.user_info.level.level}`]">
                          {{ repltItem.user_info.level.name }}
                        </div>
                        <span v-if="repltItem.r_user_info" class="fs-sm mar-right-5">
                          {{ $t('components.btn_reply') }}
                          <span class="to-name text-link">@{{ repltItem.r_user_info.nickname }}</span>
                        </span>
                        <span class="r-time mar-right-5">{{ repltItem.time | date2short }}</span>
                      </div>
                    </div>
                    <div class="rbottom-box">
                      <article class="r-content" v-html="repltItem.content"></article>
                    </div>
                  </div>
                  <div v-if="item.reply_list.length >= 5" class="show-more-box" @click="toPage(`themereply/${item.aid}`)">
                    共{{ item.replies + $t('guildhall.how_reply') }} <span class="show-more">{{ $t('guildhall.show_more') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="!guildMsg.list.length && !loadingComments">
          <div style="text-align:center;">
            {{ $t('guildhall.no_more') }}
          </div>
        </div>
        <!-- 底部分页 -->
        <div class="top-tips" style="height=200px">
          <div v-if="paginate" class="paginate">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :hide-on-single-page="true"
              :disabled="loadingComments"
              :current-page="paginate.cur"
              :total="paginate.count"
              :page-size="paginate.size"
              @current-change="commentsPageChange($event, true)"
            />
          </div>
        </div>
        <!-- 结束: 帖子列表 -->
      </div>
    </div>
    <div class="guild-end">
      <div class="end-box">
        <div class="box btn" @click="toBottomScroll">
          <img src="@/assets/images/liu.png" alt="" />
        </div>
        <div class="box btn" @click="toPage(`messages/${loginUser.uid}`)">
          <img src="@/assets/images/liao.png" alt="" />
        </div>
        <div v-backtop class="box btn">
          <img src="@/assets/images/liu.png" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import _ from 'lodash';

// import recommendItem from '@/components/RecommendItem.vue';
import CommentInput from '@/components/CommentInput.vue';
import UserAvatar from '@/components/UserAvatar.vue';
// import CommentItem from '@/components/CommentItem.vue';
// import ReplyItem from '@/components/ReplyItem.vue';
import Loading from '@/components/Loading.vue';
import { category } from '@/api';

export default {
  layout: 'guild',
  components: {
    Loading,
    UserAvatar,
    CommentInput
    // recommendItem,
  },
  async fetch() {
    await this.$store.dispatch('emoji/getEmojis');
  },
  // 请求数据
  async asyncData({ params, $axios, store }) {
    const data = {
      guildMsg: {},
      partitiontMenu: {}
    };
    const cateGid = Number(params.id);
    const subCateRes = await $axios.$post(category.getCategories, { parent_gid: 42 });
    if (subCateRes.code === 0) {
      data.partitiontMenu = subCateRes.data.filter((el) => el.gid !== 130);
    }
    const resGuild = await $axios.$post('/api/guild/hall', {
      gid: cateGid
    });
    if (resGuild.code === 0) {
      data.guildMsg = resGuild.data;
      // 过滤掉外链图片，但表情包留着
      /* eslint-disable */
      // data.guildMsg.list.forEach((el) => {
      //   el.reply_list.forEach((repel, i) => {
      //     const imgReg = /<img.*?(?:>|\/>)/gi; // 匹配图片中的img标签
      //     const arr = repel.content.match(imgReg); // 筛选出所有的img
      //     if (arr && arr.length) {
      //       // 获取图片地址
      //       for (let index = 0; index < arr.length; index++) {
      //         if (arr[index].includes('https://static.lightnovel.us/') || arr[index].includes('https://res.lightnovel.us/')) {
      //           continue;
      //         } else {
      //           // 过滤图片后的默认换行
      //           repel.content = repel.content.replace(/<(img)[^>]*><br>/gi, '');
      //           repel.content = repel.content.replace(/<(img)[^>]*>/gi, '');
      //         }
      //       }
      //     }
      //   });
      // });
      data.paginate = resGuild.data.page_info || false;
    }
    return data;
  },

  data: () => ({
    tempContentImagesMap: [], // 内容图片 [{占位符, res_id, res_url}]
    contentImages: [], // 上传图片内容
    guildMsg: {}, // 公会大厅数据
    partitiontMenu: [], // 公会大厅子分区数据
    casted: false, // 是否已经投过票
    repliesLoading: false, // 回复loading
    openedReplyKey: {}, // 已经展开的评论的回复列表key
    openedReplies: {}, // 已经展开的评论的回复列表
    loadingComments: false, // 是否在加载
    paginate: {},
    article: {
      title: '', // 文章标题
      content: '', // 文章正文
      group_id: '', // 文章分区
      only_passer: 0 // 是否正式会员可见 0否 1是
    },
    comments: { list: [], hots: [] },
    showArticleLock: false, // 是否显示文章锁定
    showCommentPleaseholder: '', // 显示的回复输入框的pleaseholder
    showCommentInputIndex: -1, // 当前评论的回复输入框显示的位置
    commentContent: '', // 评论内容
    commentImgs: [], // 是否回复显示预览图片
    loginStatus: false, // 是否登录
    showEmojiPanel: false, // 是否显示表情面板
    selectedVoteIdx: {}, // 被选中的投票选项的index
    commentAddress: {}, // 当前评论的页数
    timer: '',
    isPublishComment: true, // 是否可以发帖
    timer2: null
  }),
  computed: {
    ...mapState('login', ['isLogin', 'loginUser']),
    sortContentImages() {
      if (!this.contentImages.length) return false;
      return _.sortBy(this.contentImages, ['name']);
    }
  },
  mounted() {
    // console.log(this.guildMsg);
    // console.log(this.partitiontMenu);
    // console.log(this.article, 'article');
    // 1.增加访问记录
    if (process.browser) {
      this.initContentLinks();
    }
  },
  methods: {
    // 删除文章
    // deleteArticleConfirm(aid, isBook = false) {
    //   const payload = {
    //     fid: aid,
    //     _class: 1,
    //     dataInfo: {
    //       key: 'aid',
    //       id: aid,
    //       data: 'publish_article_articles'
    //     }
    //   };
    //   if (isBook) {
    //     payload.dataInfo.data = 'publish_article_books';
    //   }
    //   this.$confirm(this.$t('tips.are_you_sure'), this.$t('components.type_warn'), {
    //     confirmButtonText: this.$t('components.btn_confirm'),
    //     cancelButtonText: this.$t('components.btn_cancel'),
    //     type: 'warning'
    //   })
    //     .then(() => this.deleteArticle(payload))
    //     .catch(Function.prototype);
    // },

    // 执行删除
    // deleteArticle(payload) {
    //   this.$store
    //     .dispatch('userPublish/deletePublish', payload)
    //     .then(() => {
    //       this.$message.success(this.$t('tips.delete_success'));
    //     })
    //     .catch(() => {
    //       this.$message.error(this.$t('tips.delete_fail'));
    //     });
    // },
    toThemeReply(tid) {
      this.toPage(`themereply/${tid}`);
    },
    // 移除文件列表的文件
    removeContentImg(id) {
      this.contentImages = this.contentImages.filter((item) => item.id !== id);
    },
    // 将图片插入到内容当中
    appendImgToContents(url) {
      this.$refs.commentinput.addImageContent(url);
    },
    // 获取文章内容图片数组
    getImages(images) {
      this.contentImages = images;
      this.uploadedImages = images;
    },
    // 获取附加功能信息
    getAdditional(add) {
      // 处理附加功能信息
      if (add.hasPay) {
        this.article.need_pay = add.need_pay;
      }
      if (add.hasReward) {
        this.article.reward = add.reward;
      }
      if (add.only_passer) {
        this.article.only_passer = 1;
      }
    },
    // 刷新帖子(发帖后刷新帖子内容)
    async refreshArticle() {
      const res = await this.$axios.$post('/api/guild/hall', {
        gid: this.$route.params.id
      });
      if (res.code === 0) {
        this.guildMsg = res.data;
        // console.log(this.guildMsg);
      }
    },
    // 处理内容
    contentConverter(content, isSubmit = true) {
      let str = String(content);
      const imgs = this.uploadedImages;
      if (isSubmit) {
        // 将百分比转数字: 发布/更新内容需要传这样的值
        // str = str.replace(/\[size=(\d{2,3})\](.+)\[\/size\]/gim, (...args) => { return `[size=${this.fontsizeMap[args[1]]}]${args[2]}[/size]`; });

        // 将图片转res: 发布/更新内容需要传这样的值
        str = str.replace(/\[img\](.+)\[\/img\]/gim, (...args) => {
          for (let i = 0; i < imgs.length; i++) {
            if (args[1] === imgs[i].url) {
              this.tempContentImagesMap.push({
                index: args[2],
                id: imgs[i].id,
                url: imgs[i].url
              });
              return `[img]${args[2]}[/img]`;
            }
          }
          return args[0];
        });

        return str;
      }

      // 将数字转百分比: 编辑内容需要预览
      // str = str.replace(/\[size=(\d{1})\](.+)\[\/size\]/gim, (...args) => { return `[size=${this.mapFontsizes[args[1]]}]${args[2]}[/size]`; });

      // 转换对齐显示的标签[align=left] => [left][right][center]
      const alignReg = /(\[align\s{0,}=\s{0,}(left|center|right)\](.*?)\[\/align\])/gim;
      str = str.replace(alignReg, (...args) => `[${args[2]}]${args[3]}[/${args[2]}]`);

      // 将 res 转 img
      str = str.replace(/\[res\](.+)\[\/res\]/gim, (...args) => {
        const [fileType] = args[1].split(','); // 0: 图片 2:文件
        if (Number(fileType) !== 0) return '';
        if (this.tempPreviewResMap[args[1]]) {
          return `[img]${this.tempPreviewResMap[args[1]]}[/img]`;
        } else {
          return args[0];
        }
      });

      // 附件
      str = str.replace(/\[file\](.+)\[\/file\]/gim, '');
      return str;
    },
    toBottomScroll() {
      let x = document.body.scrollTop || document.documentElement.scrollTop;
      const clients = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      const wholeHeight = document.body.scrollHeight;
      const timer = setInterval(function() {
        x = x + 100;
        if (x >= wholeHeight - clients) {
          x = wholeHeight - clients;
          window.scrollTo(x, x);
          clearInterval(timer);
        }
        window.scrollTo(x, x);
      }, 16.7);
    },

    // 初始化文章内容中的 a 标签, 所有a标签都在新标签页打开
    initContentLinks() {
      const aNodes = document.querySelectorAll('#article-main-contents a');
      aNodes && aNodes.length && Array.from(aNodes).forEach((item) => (item.target = '_blank'));
    },
    // 过滤掉外链图片
    // filterPic(data) {
    //   /* eslint-disable */
    //   data.list.forEach((el) => {
    //     el.reply_list.forEach((repel, i) => {
    //       const imgReg = /<img.*?(?:>|\/>)/gi; // 匹配图片中的img标签
    //       const arr = repel.content.match(imgReg); // 筛选出所有的img
    //       if (arr && arr.length) {
    //         // 获取图片地址
    //         for (let index = 0; index < arr.length; index++) {
    //           if (arr[index].includes('https://static.lightnovel.us/') || arr[index].includes('https://res.lightnovel.us/')) {
    //             continue;
    //           } else {
    //             // 过滤图片后的默认换行
    //             repel.content = repel.content.replace(/<(img)[^>]*><br>/gi, '');
    //             repel.content = repel.content.replace(/<(img)[^>]*>/gi, '');
    //           }
    //         }
    //       }
    //     });
    //   });
    //   return data;
    // },
    // curChange
    async commentsPageChange(page, back = false) {
      const gid = Number(this.$route.params.id);
      this.loadingComments = true;
      const res = await this.$axios.$post('/api/guild/hall', {
        gid,
        page
      });
      if (res.code === 0) {
        this.loadingComments = false;
        this.guildMsg = res.data;
        this.paginate = res.data.page_info;
        this.$nextTick(() => {
          back && document.querySelector('#comments').scrollIntoView();
        });
      }
    },

    // 显示评论输入框
    showCommentInput(key, username, isComment, item) {
      this.loginNext(() => {
        this.showCommentInputIndex = key;
        this.showCommentReplyParams = item; // 当前显示评论回复输入框, 的额外参数
        this.showCommentReplyIsComment = true; // 当前显示评论回复输入框, 是否是评论
        this.showCommentPleaseholder = this.$t('detail.publish_replay');
        if (!isComment) {
          this.showCommentReplyIsComment = false;
          this.showCommentPleaseholder += `@${username}`;
        }
      });
    },

    // 检查文章数据
    checkArticleData(content) {
      if (!content.toString().trim()) {
        this.$message.error(this.$t('write.input_content'));
        return false;
      }
      if (this.getByteSize(content) < 10) {
        this.$message.error(this.$t('write.content_too_less'));
        return false;
      }
      // 仅会员可见 和 内容图片(tempContentImagesMap 在 contentConverter 方法中处理过)
      if (this.tempContentImagesMap && this.tempContentImagesMap.length) {
        this.article.images = {};
        this.tempContentImagesMap.forEach((item) => {
          this.article.images[item.index] = item.id;
        });
      }

      return true;
    },
    // 发布帖子,发布成功后间隔15秒才能发
    publishComment({ content }) {
      if (this.publishCommentReqing) {
        return;
      }
      this.loginNext(async () => {
        this.article.content = this.contentConverter(content);
        // 处理文章标题
        if (this.$route.params.id) {
          this.article.title = this.partitiontMenu.find((el) => Number(el.gid) === Number(this.$route.params.id)).name;
          this.article.group_id = Number(this.$route.params.id);
        } else {
          this.article.group_id = 129;
          this.article.title = 'ARIA之都';
        }
        if (this.checkArticleData(content)) {
          // console.log(this.article, 'this.article');
          if (this.isPublishComment) {
            this.publishCommentReqing = true; // 正在发送...
            const res = await this.$axios.$post('/api/article/post-article', this.article);
            this.publishCommentReqing = false; // 正在发送...
            // console.log(res, 'res');
            if (res.code === 0) {
              // console.log(res);
              this.$message.success(this.$t('tips.publish_success'));
              // 清空数据
              this.refreshArticle();
              this.$refs.commentinput.clearContent();
            } else {
              this.$message.success(this.$t('tips.publish_fail'));
            }
          } else {
            this.$message.warning('此操作需要间隔15秒');
          }
          if (!this.timer2) {
            this.isPublishComment = false;
            const context = this;
            this.timer2 = setTimeout(function() {
              context.isPublishComment = true;
              context.timer2 = null;
            }, 15000);
          }
        }
      });
    },
    // 增加访问记录
    addHistory() {
      this.$axios.post('/api/history/add-history', {
        fid: this.article.aid,
        class: 1
      });
    },

    // 获取子组件传来的投票信息
    getPoll(poll) {
      // console.log(poll);
      this.isPoll = true;
      this.article.poll = poll;
    }
  },
  head() {
    const title = this.$t('title');
    if (!this.$route.params.id) {
      return { title };
    }
    const curCate = this.partitiontMenu.find((item) => item.gid === Number(this.$route.params.id));
    const name = this.$t(`guildhall.${curCate.name}`);
    if (!curCate) {
      return { title };
    }
    return { title: name + '-' + title };
  }
};
</script>
<style lang="scss" scoped>
.guild-container {
  margin-top: 29px;
  display: flex;
  background-color: white;
  .guild-left {
    background-color: white;
    width: 140px;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 999;
    a {
      color: #b2b2b2;
      &:hover {
        color: #3ad8d9;
      }
      &.active,
      &.exact-active-link {
        span {
          color: #39d7d9;
        }
      }
    }
    .img-box {
      width: 110px;
      height: 95px;
      margin: 15px 15px 29px;
    }
    .img-box1 {
      width: 120px;
      height: 70px;
      margin: 19px 10px 0px 10px;
      overflow: hidden;
      & ~ span {
        font-size: 15px;
        display: inline-block;
        margin: 9px 10px 0px 10px;
      }
    }
  }
  .guild-right {
    width: 100%;
    padding: 31px 31px 5px;
    // 已经上传的内容图片
    .uploaded-imgs-wrapper {
      padding-bottom: 20px;
      .img-item-wrapper {
        width: 45%;
        padding: 10px;
        border: 1px solid $gray-white;
        margin-bottom: 10px;
        border-radius: 5px;
        position: relative;
        .file-tag {
          border-radius: 0 !important;
          position: absolute;
          top: 0;
          left: 0;
        }
        p {
          height: 100%;
          line-height: 40px;
          padding: 0 15px;
        }
        img {
          width: 40px;
          height: 40px;
        }
        .btn-box {
          position: absolute;
          right: 5px;
          top: 5px;
          width: 20px;
          height: 20px;
          padding: 4px;
          svg {
            height: 100%;
            fill: $gray;
            &:hover {
              fill: $primary;
            }
          }
        }
      }
    }
    .comment-list-item-content {
      color: #b2b2b2;
      width: 100%;
      position: relative;
    }
    .comment-container {
      border-top: 1px solid #eeeeee;
      padding-top: 14px;
      text-align: justify;
      .comment-box {
        .box-top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .top-left {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .top-time {
              font-size: 15px;
              color: #b2b2b2;
            }
            .top-title {
              font-size: 15px;
              color: #424242;
            }
            .top-img {
              width: 30px;
              height: 30px;
              overflow: hidden;
              border-radius: 50%;
            }
            .avatar {
              width: 30px;
              height: 30px;
              overflow: hidden;
              border-radius: 50%;
              img {
                border-radius: 50px;
              }
            }
          }
          .top-right {
            display: flex;
            align-items: center;
            justify-content: space-between;
            text-align: end;
            .top-right-content {
              display: flex;
              width: 111px;
              justify-content: flex-end;
              .img-box {
                width: 15px;
                height: 15px;
                cursor: pointer;
                margin-left: 5px;
              }
            }
            .right-end {
              height: 16px;
              font-size: 15px;
              color: #b2b2b2;
              line-height: 15px;
              margin-left: 5px;
              cursor: pointer;
            }
          }
        }
        .box-bottom {
          margin-top: 6.5px;
          .comment-content {
            max-width: 1095px;
            font-size: 15px;
            color: #424242;
            line-height: 24px;
            margin-bottom: 15.5px;
            word-break: break-word;
            white-space: pre-wrap;
            padding: 0 20px;
          }
          .comment-images {
            .img-summary {
              display: block;
              margin-bottom: 5px;
              img {
                max-height: 250px;
              }
            }
          }
          .reply-content {
            text-align: justify;
            .reply-box {
              padding: 15px;
              display: table;
              background: #fafafa;
              margin-bottom: 5px;
              border-radius: 5px;
              .rtop-cont-box {
                display: flex;
                justify-content: space-between;
                .rtop-box {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  line-height: 15px;
                  height: 21px;
                  margin-bottom: 13px;
                  max-width: 1098px;
                  .r-name {
                    color: #608ab8;
                  }
                  .r-time {
                    height: 15px;
                    font-size: 15px;
                    color: #b2b2b2;
                    line-height: 15px;
                  }
                }
                .relpy {
                  display: none;
                }
              }
              .rbottom-box {
                padding: 0 20px;
                .r-content {
                  font-size: 15px;
                  color: #424242;
                  line-height: 24px;
                  max-width: 1068px;
                  white-space: pre-wrap;
                  word-break: break-all;
                }
              }
            }
            .reply-box1 {
              padding: 15px;
              display: table;
              background: #fafafa;
              margin-bottom: 5px;
              border-radius: 5px;
              .rtop-cont-box {
                display: flex;
                justify-content: space-between;
                .rtop-box {
                  display: flex;
                  justify-content: space-between;
                  line-height: 15px;
                  height: 15px;
                  margin-bottom: 13px;
                  max-width: 1098px;
                  .r-name {
                    color: #608ab8;
                  }
                  .r-time {
                    height: 15px;
                    font-size: 15px;
                    color: #b2b2b2;
                    line-height: 15px;
                  }
                }
                .relpy {
                  cursor: pointer;
                  margin-left: 5px;
                  height: 15px;
                  font-size: 15px;
                  color: #b2b2b2;
                  line-height: 15px;
                }
              }
              .rbottom-box {
                padding: 0 20px;
                .r-content {
                  font-size: 15px;
                  color: #424242;
                  line-height: 24px;
                  max-width: 1068px;
                  white-space: pre-wrap;
                  word-break: break-all;
                }
              }
            }
            .show-more-box {
              cursor: pointer;
              height: 33px;
              font-size: 15px;
              color: #b2b2b2;
              line-height: 30px;
              .show-more {
                color: #3ad8d9;
              }
            }
          }
        }
      }
    }
  }
  .guild-end {
    position: fixed;
    z-index: 999;
    right: 14px;
    bottom: 112px;
    .end-box {
      .box {
        background-color: white;
        border-radius: 5px;
        width: 50px;
        height: 50px;
        padding: 13px 10px;
        margin-bottom: 10px;
        img {
          width: 30px;
          height: 25px;
        }
        &:first-of-type {
          transform: rotate(180deg);
        }
      }
    }
  }
}
.comment-input-btns {
  position: relative;
}
.cache-content {
  font-size: 15px;
  position: absolute;
  color: #b2b2b2;
  line-height: 30px;
  right: 99px;
}
.btn-publish {
  width: 130px;
  height: 35px;
  border-radius: 35px;
  text-align: center;
  margin: 0 auto;
}
.btn-publish1 {
  width: 130px;
  height: 35px;
  border-radius: 35px;
  text-align: center;
  margin: 0 auto;
  border: 1px solid gray;
}
</style>
<style lang="scss">
.r-content {
  img {
    max-width: 250px !important;
  }
}
</style>

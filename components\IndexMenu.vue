<template>
  <div class="index-menu">
    <div class="container">
      <ul>
        <li>
          <nuxt-link :to="{ path: $i18n.path('') }">
            {{ $t('components.index_menu.all') }}
          </nuxt-link>
        </li>
        <li v-for="(value, key) in menu" :key="key" @mouseenter="changeActive(value, key)" @mouseleave="removeActive(value)">
          <nuxt-link
            :to="{
              path: targetURL(value.gid),
              query: { type: value.cover_type }
            }"
            :class="{ active: $route.params.category == value.gid }"
          >
            {{ $t(`links.${value.name}`) || value.name }}
          </nuxt-link>
          <!-- 浮窗 -->
          <div v-show="value.isShow" class="index-flow">
            <div class="item-box">
              <div v-for="(item, idx) in value.items" :key="idx" class="btn">
                <nuxt-link
                  :to="{
                    path: $i18n.path(`category/${value.gid}/${item.gid}`),
                    params: { subCategory: item.gid },
                    query: { type: value.cover_type }
                  }"
                  class="tag"
                >
                  {{ $t(`linkSon.${item.name}`) }}
                </nuxt-link>
              </div>
            </div>
          </div>
        </li>
        <li><slot name="guildhall"></slot></li>
        <li><slot name="confinement"></slot></li>
      </ul>
      <!-- 浮窗 -->
      <div v-show="isShow" class="index-flow"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    menu: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isShow: false
    };
  },
  methods: {
    targetURL(gid) {
      // 如果是 大分区 是 轻小说(gid:3), 那么默认跳到 gid 为 106 的小分区
      // 如果是 大分区是其他分区 那么默认跳到 "全部(gid:0)" 小分区
      const subCateGid = gid === 3 ? 106 : 0;
      return this.$i18n.path(`category/${gid}/${subCateGid}`);
    },
    changeActive(val, idx) {
      if (val) {
        val.isShow = true;
        this.isShow = true;
      }
    },
    changeActive1(val) {
      if (val) {
        val.isShow = true;
        this.isShow = true;
      }
    },
    removeActive(val) {
      if (val) {
        val.isShow = false;
        this.isShow = false;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.index-menu {
  // max-width: 1920px;
  min-width: 1160px;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: white;
  box-shadow: 0px 0px 15px 0px rgba(1, 1, 1, 0.1);
  .container {
    width: 1160px;
    margin: 0 auto;
    padding: 0 !important;
    .index-flow {
      position: absolute;
      width: 100%;
      left: 0;
      z-index: 98;
      height: 50px;
      line-height: 50px;
      opacity: 0.8;
      background: #ffffff;
    }
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      li {
        margin-right: 60px;
        cursor: pointer;
        &:hover a {
          color: #39d7d9;
        }
        a {
          color: #424242;
          position: relative;
          font-size: 15px;
          text-decoration: none;
          &:hover {
            color: #39d7d9;
          }
          &.active,
          &.exact-active-link {
            color: #39d7d9;

            &::after {
              content: ' ';
              position: absolute;
              left: 0;
              bottom: -15px;
              width: 100%;
              height: 2px;
              background: #39d7d9;
              border-radius: 1px;
            }
          }
        }
        &:nth-last-child(2) {
          position: absolute;
          right: 95px;
          div {
            display: flex;
            align-items: center;
            a {
              margin-left: 5px;
            }
          }
        }
        &:last-of-type {
          position: absolute;
          right: 0;
        }
        .index-flow {
          position: absolute;
          width: 100%;
          left: 0;
          z-index: 99;
          height: 50px;
          line-height: 50px;
          opacity: 0.8;
          background: #ffffff;
          border: none;
          .item-box {
            margin: 0 auto;
            padding: 0 !important;
            display: flex;
            div {
              margin-right: 51px;
              a {
                color: #424242;
                text-decoration: none;
                &:hover {
                  color: #39d7d9;
                }
              }
            }
          }
        }
        &:nth-of-type(2),
        &:nth-of-type(3) {
          .index-flow {
            .item-box {
              width: 1160px;
            }
          }
        }
        &:nth-of-type(4) {
          .index-flow {
            .item-box {
              display: inline-flex;
              position: absolute;
              left: 205px;
            }
          }
        }
        &:nth-of-type(5) {
          .index-flow {
            .item-box {
              display: inline-flex;
              position: relative;
              left: 333px;
            }
          }
        }
        &:nth-of-type(6) {
          .index-flow {
            .item-box {
              display: inline-flex;
              position: relative;
              left: 462px;
            }
          }
        }
        &:nth-of-type(7) {
          .index-flow {
            .item-box {
              display: inline-flex;
              position: relative;
              left: 506px;
            }
          }
        }
        &:nth-of-type(8) {
          .index-flow {
            .item-box {
              display: inline-flex;
              position: relative;
              left: 594px;
            }
          }
        }
        &:nth-of-type(9) {
          .index-flow {
            .item-box {
              display: inline-flex;
              position: relative;
              left: 660px;
            }
          }
        }
      }
    }
  }
}
</style>

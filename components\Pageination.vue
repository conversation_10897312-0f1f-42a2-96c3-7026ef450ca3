<template>
  <div class="pageination h30 flex">
    <div class="pageination-btns h30">
      <!-- 上一页 -->
      <button class="h30 br5 w60 btn" :class="{ disabled: page === 1 }" @click="prevClick">
        {{ $t('components.pagination.prev_page') }}
      </button>

      <!-- 页数 -->
      <button v-for="item in count" :key="item" :class="{ selected: item === page }" class="h30 br5 w30 btn" @click="change(false)">
        {{ item }}
      </button>

      <!-- 下一页 -->
      <button class="h30 br5 w60 btn" :class="{ disabled: page === count }" @click="nextClick">
        {{ $t('components.pagination.next_page') }}
      </button>
    </div>

    <!-- 直接跳 -->
    <div v-show="!hideInput" class="pageination-input">
      <span class="h30 br5"> {{ $t('components.pagination.jump_to') }}</span>
      <input v-model="target" type="number" class="h30 br5 w60 page-number-input" :min="1" :max="count" @keyup.enter="change(true)" />
      <span class="h30 br5"> {{ $t('components.pagination.page') }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    page: {
      // 当前选中页
      type: Number,
      required: true
    },
    count: {
      // 总共有多少页
      type: Number,
      required: true
    },
    hideInput: {
      // 隐藏条状页面的input
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      target: ''
    };
  },
  methods: {
    prevClick() {
      this.$emit('prev-click', this.page - 1);
    },
    nextClick() {
      this.$emit('next-click', this.page + 1);
    },
    change(input = false) {
      const page = Number(this.page);
      if (input) {
        if (this.target > this.count) {
          alert('不能大于最大页数');
          return;
        }
        if (this.target < 1) {
          alert('不能小于最小页数');
        }
        this.$emit('change', page);
        return;
      }
      this.$emit('change', page);
    }
  }
};
</script>

<style lang="scss" scope>
.br5 {
  border-radius: 5px;
}
.h30 {
  height: 30px;
  margin: 0;
  padding: 0;
}
.w60 {
  width: 60px;
}
.w30 {
  width: 30px;
}
.pageination {
  .pageination-btns {
    height: 30px;
    margin-right: 20px;
    .btn {
      background-color: #fff;
      border: 1px solid $gray-white;
      margin: 0 5px;
      &.disabled {
        color: $gray-white;
      }
      &.selected {
        background-color: $primary;
        color: $white;
        border: none;
      }
    }
  }

  .pageination-input {
    .page-number-input {
      border: 1px solid $gray-white;
      height: 30px;
      box-sizing: border-box;
      padding: 5px;
    }
  }
}
</style>

<template>
  <div class="search-page">
    <div class="search-box">
      <input v-model="keywords" type="text" class="search-input" :placeholdee="$t('search.please_input_search_kw')" @keyup.enter="search" />
      <button type="button" class="btn-search" @click="search()">
        <span class="icon-search">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 674.91 674.98">
            <path
              d="M706,729a38.53,38.53,0,0,1-27.54-11.14L546.88,590.66l-5.61,3.67A312.66,312.66,0,0,1,258.14,618.2,305.86,305.86,0,0,1,161.33,555,295.06,295.06,0,0,1,96,461.66,281,281,0,0,1,72.05,347.74c0-78,31.76-151.36,89.41-206.72A309.46,309.46,0,0,1,258.7,77.9,322.23,322.23,0,0,1,377.94,54,310.76,310.76,0,0,1,496.29,77.21a305.91,305.91,0,0,1,96.82,63.19,295,295,0,0,1,65.33,93.35,280.81,280.81,0,0,1,23.95,113.92c0,69.07-24.36,134.06-70.53,187.83l-5.46,6L734.78,664.71l.27.27a34.84,34.84,0,0,1,0,52.46l-.13.14A42.75,42.75,0,0,1,706,729ZM376.42,123.51C247.7,123.51,143,224.07,143,347.74c0,124.43,104.72,225.75,233.44,225.75S609.86,472.86,609.86,349.26c0-124.5-104.71-225.75-233.44-225.75Zm0,0"
              transform="translate(-72.05 -54.02)"
              fill="#FFFFFF"
            />
          </svg>
        </span>
        <span>{{ $t('search.search') }}</span>
      </button>
    </div>
    <div class="search-container">
      <div class="search-menu">
        <ul class="tabbars">
          <li v-for="item in menu" :key="item['type']">
            <a href="javascript:;" class="tabbar-item" :class="{ active: type === item['type'] }" @click="changeTab(item.type)">
              {{ item['name'] }}
            </a>
          </li>
        </ul>
      </div>

      <div v-show="loading" class="loading-container">
        <Loading />
      </div>
      <div v-show="!loading" class="search-result">
        <!-- 没有数据的情况: 显示 -->
        <div v-if="isNodata" class="no-data">
          <img src="@/assets/images/no_data.png" class="no-data-img" />
          <span class="tips mar-top-10">{{ $t('search.no_search_data') }}</span>
        </div>
        <div v-else>
          <!-- 综合-对应的数据 -->
          <div v-show="type === 0">
            <!-- 游戏: PC端暂未支持 -->
            <!-- 合集 -->
            <!-- TODO: 缺少合集的评论数 -->
            <SeriesItem
              v-for="item in complex.collections"
              :key="item.sid"
              mode="vertical"
              :title="item.name"
              :has-icon="true"
              :date="item.last_time | date2short"
              :avatar="item.editors[0].avatar"
              :author="item.author"
              :groupname="`${item.parent_group_name}-${item.group_name}`"
              :views="item.hits | num2short"
              :comments="item.likes | num2short"
              :banner="item.cover"
              @cover-click="toPage(`/series/${item.sid}`)"
              @avatar-click="toPage(`/profile/${item.editors[0].uid}`)"
            />
            <!-- 用户 -->
            <UserItem v-for="item in complex.users" :key="item.uid" :kw="keywords" :item="item" />
            <!-- 文章 -->
            <SeriesItem
              v-for="(item, key) in complex.articles"
              :key="`aid-${key}-${item.aid}`"
              :mode="item.cover_type ? 'vertical' : 'horizontal'"
              :title="item.title"
              :date="item.time | date2short"
              :avatar="item.avatar"
              :author="item.author"
              :groupname="`${item.parent_group_name}-${item.group_name}`"
              :views="item.hits | num2short"
              :comments="item.comments | num2short"
              :banner="item.cover"
              @cover-click="toPage(`/detail/${item.aid}`)"
              @avatar-click="toPage(`/profile/${item.uid}`)"
            />
            <!-- 分页 -->
            <div class="search-pages">
              <el-pagination
                class="my-pagination"
                layout="prev, pager, next, jumper"
                :prev-text="$t('paginate.prev')"
                :next-text="$t('paginate.next')"
                :hide-on-single-page="true"
                :disabled="loading"
                :current-page="complex_pageinfo.cur"
                :total="complex_pageinfo.count"
                :page-size="complex_pageinfo.size"
                @current-change="pageChange"
              />
            </div>
          </div>

          <!-- 用户-对应的数据 -->
          <div v-show="type === 1">
            <UserItem v-for="item in users" :key="item.uid" :kw="keywords" :item="item" />
            <!-- 分页 -->
            <div class="search-pages">
              <el-pagination
                class="my-pagination"
                layout="prev, pager, next, jumper"
                :prev-text="$t('paginate.prev')"
                :next-text="$t('paginate.next')"
                :hide-on-single-page="true"
                :disabled="loading"
                :current-page="users_pageinfo.cur"
                :total="users_pageinfo.count"
                :page-size="users_pageinfo.size"
                @current-change="pageChange"
              />
            </div>
          </div>

          <!-- 合集-对应的数据 -->
          <div v-show="type === 2">
            <SeriesItem
              v-for="item in series"
              :key="item.sid"
              mode="vertical"
              :title="item.name"
              :has-icon="true"
              :date="item.last_time | date2short"
              :avatar="item.editors[0].avatar"
              :author="item.author"
              :groupname="`${item.parent_group_name}-${item.group_name}`"
              :views="item.hits | num2short"
              :comments="item.likes | num2short"
              :banner="item.cover"
              @cover-click="toPage(`/series/${item.sid}`)"
              @avatar-click="toPage(`/profile/${item.editors[0].uid}`)"
            />

            <!-- 分页 -->
            <div class="search-pages">
              <el-pagination
                class="my-pagination"
                layout="prev, pager, next, jumper"
                :prev-text="$t('paginate.prev')"
                :next-text="$t('paginate.next')"
                :hide-on-single-page="true"
                :disabled="loading"
                :current-page="series_pageinfo.cur"
                :total="series_pageinfo.count"
                :page-size="series_pageinfo.size"
                @current-change="pageChange"
              />
            </div>
          </div>

          <!-- 资讯-对应的数据 -->
          <SearchResultArticles v-show="type === 3" :datas="infos" :pageinfo="infos_pageinfo" @current-change="pageChange" />

          <!-- 动画-对应的数据 -->
          <SearchResultArticles v-show="type === 4" :datas="animation" :pageinfo="animation_pageinfo" @current-change="pageChange" />

          <!-- 漫画-对应的数据 -->
          <SearchResultArticles v-show="type === 5" :datas="manga" :pageinfo="manga_pageinfo" @current-change="pageChange" />

          <!-- 轻小说-对应的数据 -->
          <SearchResultArticles v-show="type === 6" :datas="novel" :pageinfo="novel_pageinfo" @current-change="pageChange" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import SeriesItem from '@/components/search/SeriesItem';
import SearchResultArticles from '@/components/search/SearchResultArticles';
import UserItem from '@/components/search/UserItem';
import Loading from '@/components/Loading';

export default {
  layout: 'front_wrap',
  components: {
    SeriesItem,
    UserItem,
    SearchResultArticles,
    Loading
  },

  async asyncData({ query, $axios }) {
    const data = {
      complex: {},
      complex_pageinfo: {},
      keywords: query.keywords
    };
    if (data.keywords) {
      const params = { q: data.keywords, type: 0, page: 1 };
      const res = await $axios.$post('/api/search/search-result', params);
      if (res.code === 0) {
        data.complex = res.data;
        data.complex_pageinfo = res.data.page_info;
      }
    }
    return data;
  },

  data: () => ({
    type: 0,
    keywords: '',
    loading: false, // 是否正在请求数据
    complex: { games: [], collections: [], articles: [] }, // 综合
    complex_pageinfo: {}, // 综合分页信息
    users: [], // 用户
    users_pageinfo: {}, // 用户分页
    series: [], // 合集
    series_pageinfo: {}, // 合集分页
    novel: [], // 小说
    novel_pageinfo: {}, // 小说分页
    animation: [], // 动画
    animation_pageinfo: {}, // 动画分页
    manga: [], // 漫画
    manga_pageinfo: {}, // 漫画分页
    infos: [], // 资讯
    infos_pageinfo: {}, // 资讯分页
    // 允许的搜索类型 0:综合 1:用户 2:合集 3:资讯 4:动画 5:漫画 6:轻小说
    allowTypes: [0, 1, 2, 3, 4, 5, 6]
  }),

  computed: {
    menu() {
      return this.$t('search.menu');
    },
    isNodata() {
      let key;
      switch (this.type) {
        case 0:
          key = 'complex_pageinfo';
          break;
        case 1:
          key = 'users_pageinfo';
          break;
        case 2:
          key = 'series_pageinfo';
          break;
        case 3:
          key = 'infos_pageinfo';
          break;
        case 4:
          key = 'animation_pageinfo';
          break;
        case 5:
          key = 'manga_pageinfo';
          break;
        case 6:
          key = 'novel_pageinfo';
          break;
      }
      return this[key].count === 0;
    }
  },

  methods: {
    // 切换tabbar
    changeTab(type) {
      if (this.type === type) return;
      this.type = type;
      this.search();
    },

    // 分页: 切换页
    pageChange(page) {
      this.search(page, true);
    },

    // 搜索
    async search(page = 1, backtop = false) {
      const { keywords, type, loading } = this;
      if (loading || !keywords || !this.allowTypes.includes(type)) {
        return;
      }
      this.loading = true;
      const res = await this.$axios.$post('/api/search/search-result', {
        q: keywords,
        type,
        page
      });
      backtop && this.$nextTick(() => this.backtop()); // 滚动到顶部
      this.loading = false;
      if (res.code === 0) {
        let key;
        switch (type) {
          case 0:
            this.complex = res.data;
            this.complex_pageinfo = res.data.page_info;
            return;
          case 1:
            key = 'users';
            break;
          case 2:
            key = 'series';
            break;
          case 3:
            key = 'infos';
            break;
          case 4:
            key = 'animation';
            break;
          case 5:
            key = 'manga';
            break;
          case 6:
            key = 'novel';
            break;
        }
        this[key] = res.data.list;
        this[`${key}_pageinfo`] = res.data.page_info;
      }
    }
  },

  head() {
    const title = this.$t('search.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>
<style lang="scss" scoped>
.search-page {
  position: relative;
  width: 1160px;
  margin: 0 auto;
  .search-box {
    width: 620px;
    margin: 40px auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search-input {
      width: 500px;
      height: 40px;
      border: 1px solid #eee;
      border-radius: 20px;
      padding-left: 20px;
    }
    .btn-search {
      cursor: pointer;
      position: relative;
      width: 100px;
      height: 40px;
      background: $primary;
      border-radius: 20px;
      text-align: left;
      border: none;
      outline: none;
      font-size: 20px;
      padding-left: 40px;
      color: white;
      .icon-search {
        position: absolute;
        top: 40%;
        transform: translateY(-40%);
        left: 20px;
        display: block;
        width: 15px;
        height: 17px;
      }
    }
  }
  .search-container {
    position: relative;
    min-height: calc(100vh - 400px);
    .search-menu {
      height: 32px;
      border-bottom: 1px solid #eee;
      .tabbars {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        justify-content: space-between;
        width: 600px;
        margin: 0 auto;
        .tabbar-item {
          position: relative;
          text-decoration: none;
          color: $dark;
          font-size: 15px;
          &:hover,
          &.active {
            color: $primary;
            &::after {
              content: ' ';
              position: absolute;
              bottom: -14px;
              left: 0;
              width: 100%;
              height: 2px;
              background-color: $primary;
            }
          }
        }
      }
    }
    .search-result,
    .loading-container {
      min-height: 45vh;
    }
    .search-result {
      position: relative;
      min-height: 45vh;
      .no-data {
        min-height: 45vh;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .no-data-img {
          max-width: 260px;
        }
        .tips {
          color: $gray;
        }
      }
      .search-pages {
        margin-top: 40px;
      }
    }
  }
}
</style>

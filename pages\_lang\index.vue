<template>
  <div>
    <ul class="sidebar-buttons">
      <!-- 黑夜模式 -->
      <li v-if="isLight" class="btn-item hover" @click="toBlack">
        <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M416.147324 490.012886c0 189.883463-108.05191 354.433104-265.960062 435.753774a25.814655 25.814655 0 0 0 0.540936 46.11477 510.37285 510.37285 0 0 0 257.094726 51.088373c254.405074-15.491798 461.478272-220.942189 478.728111-475.257107C906.836124 248.785608 670.357059 0.000255 375.772483 0.000255A509.846941 509.846941 0 0 0 196.707734 32.291113c-19.849336 7.42284-23.140028 34.30434-5.334227 45.799224 135.203878 87.210859 224.773817 239.063536 224.773817 411.922549z"
          ></path>
        </svg>
      </li>
      <!-- 光明模式 -->
      <li v-else class="btn-item hover" @click="toLight($event)">
        <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path d="M511.992609 511.992609m-258.605269 0a258.605269 258.605269 0 1 0 517.210538 0 258.605269 258.605269 0 1 0-517.210538 0Z"></path>
          <path d="M511.992609 65.408878m-65.408877 0a65.408878 65.408878 0 1 0 130.817755 0 65.408878 65.408878 0 1 0-130.817755 0Z"></path>
          <path d="M511.992609 958.591122m-65.408877 0a65.408878 65.408878 0 1 0 130.817755 0 65.408878 65.408878 0 1 0-130.817755 0Z"></path>
          <path d="M65.408878 511.992609m-65.408878 0a65.408878 65.408878 0 1 0 130.817755 0 65.408878 65.408878 0 1 0-130.817755 0Z"></path>
          <path d="M958.591122 511.992609m-65.408877 0a65.408878 65.408878 0 1 0 130.817755 0 65.408878 65.408878 0 1 0-130.817755 0Z"></path>
          <path d="M827.788149 196.211851m-65.408878 0a65.408878 65.408878 0 1 0 130.817755 0 65.408878 65.408878 0 1 0-130.817755 0Z"></path>
          <path d="M196.211851 827.788149m-65.408877 0a65.408878 65.408878 0 1 0 130.817755 0 65.408878 65.408878 0 1 0-130.817755 0Z"></path>
          <path d="M196.211851 196.211851m-65.408877 0a65.408878 65.408878 0 1 0 130.817755 0 65.408878 65.408878 0 1 0-130.817755 0Z"></path>
          <path d="M827.788149 827.788149m-65.408878 0a65.408878 65.408878 0 1 0 130.817755 0 65.408878 65.408878 0 1 0-130.817755 0Z"></path>
        </svg>
      </li>
    </ul>
    <IndexMenu :menu="categoryMenu">
      <template slot="guildhall">
        <div>
          <img src="@/assets/images/liaotianshi.png" alt="" />
          <nuxt-link
            :to="{
              path: $i18n.path('guildhall/129'),
              params: { id: 129 }
            }"
            target="_blank"
          >
            公会大厅
          </nuxt-link>
        </div>
      </template>
      <!-- <template slot="confinement">
        <nuxt-link :to="$i18n.path(`confinement`)" target="_blank" class="">
          <span> 禁闭室</span>
        </nuxt-link>
      </template> -->
    </IndexMenu>
    <div class="index-container">
      <div class="module-recommend">
        <!-- 轮播图 -->
        <client-only>
          <div class="home-swiper">
            <swiper ref="mySwiper" :options="swiperOptions">
              <swiper-slide v-for="item in carousel" :key="item.id">
                <div class="swiper-item hover" @click="doRecomAction(item)">
                  <el-image class="swiper-image" :src="item.pic_url" lazy></el-image>
                  <div class="swiper-mask" :title="item.title">
                    <div class="swiper-title text-hide-1" :title="item.title">
                      {{ item.title }}
                    </div>
                  </div>
                </div>
              </swiper-slide>
              <div slot="pagination" class="swiper-pagination"></div>
            </swiper>
          </div>
        </client-only>

        <!-- 轮播图右边推荐 -->
        <div class="recommend-list">
          <div v-for="item in recommends" :key="item.id" class="recommend-item">
            <CardRecommend :aid="item.action_params" :img="item.pic_url" :title="item.title" @click.native="doRecomAction(item)" />
          </div>
        </div>
      </div>

      <!-- 底部分区 -->
      <div v-for="(row, k) in rows" :key="k" class="module-index" :class="{ other: row.cover_type == 2 }">
        <div class="module-left">
          <div class="module-head">
            <div class="module-title flex ai-center">
              <img v-if="row.icon" :src="row.icon" class="module-icon mar-top-10" />
              <p class="icon-title">{{ $t(`linkSon.${row.title}`) }}</p>
            </div>
            <a v-if="row.more_type === 1" :href="morelink(row.more_params, row.more_type)" target="_blank" class="module-more">{{ row.more }}</a>
          </div>
          <div class="module-list">
            <div v-for="item in row.items" :key="item.id" class="card-common-box">
              <CardItem
                :mode="item.type == 2 ? 'horizontal' : 'vertical'"
                :img="item.pic_url"
                :title="item.title"
                :comments="item.comments"
                :views="item.hits"
                :title2rows="true"
                :has-icon="item.action_type === 2"
                @click="doRecomAction(item)"
              />
            </div>
          </div>
        </div>
        <div v-if="row.ranks[0].cover_type === 1" class="module-right">
          <RankOther :ranks="row.ranks" />
        </div>
        <div v-else class="module-right">
          <Rank v-if="k === 0" :ranks="row.ranks" :covers="0" :max-num="6" />
          <Rank v-else :ranks="row.ranks" />
        </div>
      </div>
      <FixedDownload />
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from 'vuex';
import { category } from '@/api';
import CardRecommend from '@/components/CardRecommend';
import Rank from '@/components/Rank';
import RankOther from '@/components/RankOther';
import FixedDownload from '@/components/FixedDownload';
import IndexMenu from '@/components/IndexMenu';
import CardItem from '@/components/CardItem.vue';
import storage from '@/plugins/mindLocalStorage.js';
export default {
  components: {
    CardRecommend,
    Rank,
    RankOther,
    FixedDownload,
    IndexMenu,
    CardItem
  },

  async asyncData({ $axios, store }) {
    // 处理数据
    const data = {
      carousel: [],
      recommends: [],
      rows: [],
      categoryMenu: []
    };
    const resCategory = await $axios.$post(category.getArticleByCates, { depth: 2, cache: true });
    if (resCategory.code === 0) {
      data.categoryMenu = resCategory.data.slice(0, resCategory.data.length - 1);
    }
    const res = await $axios.$post('/api/recom/get-pc-home');
    if (res.code === 0) {
      let item = null;
      for (let i = 0, l = res.data.length; i < l; i++) {
        item = res.data[i];
        // 计算出: 所有的推荐项的目标地址(action_type: 1 文章 2 合集)
        // for (const recom of item.items) {
        //   if (recom.action_type === 1) {
        //     recom.url = `/detail/${recom.action_params}`;
        //   } else if (recom.action_type === 2) {
        //     recom.url = `/series/${recom.action_params}`;
        //   }
        // }
        switch (item.type) {
          case 5: // 全屏背景banner
            data.topBarItem = item.items ? item.items[0] : {};
            break;
          case 1: // 轮播图
            data.carousel = item.items;
            break;
          case 3: // 轮播图右边的推荐
            data.recommends = item.items;
            break;
          case 2: // 资讯(标题2行)|轻小说|漫画|动画|其他
            data.rows.push(item);
            break;
        }
      }
    }
    return data;
  },

  data: () => ({
    topBarItem: {}, // topbar推荐
    categoryMenu: [],
    carousel: [], // 轮播图
    recommends: [], // 轮播图右边的推荐
    rows: [], // 底部分类
    swiperOptions: {
      autoplay: true,
      pagination: {
        el: '.swiper-pagination',
        clickable: true
      }
    },
    showFlowDialog: true,
    isLight: true
  }),

  computed: {
    ...mapGetters('login', ['isLogin']),
    ...mapState('home', ['topbar'])
  },

  watch: {
    isLogin(val) {
      val && process.browser && this.$store.dispatch('login/refreshUserinfo');
    }
  },

  mounted() {
    if (process.browser) {
      // 延迟提交TopBar
      const that = this;
      setTimeout(() => {
        that.$store.commit('home/setTopBar', that.topBarItem);
      }, 50);
    }
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
      this.isLight = false;
    }
  },
  methods: {
    // "更多" 连接地址
    morelink(MoreId, type) {
      // 由于 "轻小说" 分类没有 "全部分类(gid=0)", 所以不能写死
      // 如果需要改这个变态的智障需求需要修改两个地方 IndexMenu.vue 组件和这个文件
      if (type === 1) {
        const moreArr = MoreId.split(',').map(Number);
        const gid = moreArr[0];
        const subCateGid = gid === 3 ? 106 : 0;
        return this.$i18n.path(`category/${gid}/${subCateGid}?type=${moreArr[2]}`);
      }
    },
    toBlack() {
      this.$includeLinkStyle(require('@/static/css/user.css'));
      this.$message.success(this.$t('index.hei_zhi'));
      storage.set('mode', 'light');
      this.isLight = false;
    },
    toLight(el) {
      this.isLight = true;
      storage.remove('mode');
      this.$message.success(this.$t('index.b_zhi'));
      this.$router.go(0);
    }
  },

  head() {
    return {
      title: this.$t('title')
    };
  }
};
</script>
<style lang="scss" scoped>
.sidebar-buttons {
  width: 50px;
  position: fixed;
  right: 20px;
  bottom: 5%;
  .btn-item {
    border: 1px solid $gray-white;
    width: 100%;
    height: 50px;
    margin-bottom: 10px;
    padding: 15px;
    a {
      width: 100%;
      height: 100%;
    }
    &:hover {
      background: $primary;
      .svg-icon {
        fill: $white;
      }
    }
    .svg-icon {
      fill: $dark;
    }
  }
}
.index-container {
  width: 1160px;
  margin: 0 auto;
  margin-top: 25px;

  .module-recommend {
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    height: 270px;

    .home-swiper {
      width: 500px;
      height: 270px;
      border-radius: 10px;
      overflow: hidden;

      .swiper-item {
        display: block;
        position: relative;
        height: 270px;
        overflow: hidden;

        .swiper-image {
          width: 100%;
        }
        .swiper-mask {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 60px;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));

          .swiper-title {
            position: absolute;
            left: 0;
            bottom: 10px;
            font-size: 13px;
            color: white;
            padding: 0 100px 0 20px;
            text-align: left;
          }
        }
      }

      .swiper-pagination {
        bottom: 5px;
        text-align: right;

        & /deep/ .swiper-pagination-bullet {
          margin: 0 15px 0 0;
          width: 10px;
          height: 10px;

          &.swiper-pagination-bullet-active {
            background-color: #39d7d9;
          }

          &:last-of-type {
            margin: 0 20px 0 0;
          }
        }
      }
    }

    .recommend-list {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      width: 660px;
      margin-top: -20px;
      margin-left: -20px;
      .recommend-item {
        margin-left: 20px;
        margin-top: 20px;
      }
    }
  }

  .module-index {
    margin-top: 30px;
    // height: 365px;
    display: flex;
    justify-content: space-between;
    overflow: hidden;

    // &.other {
    // height: 620px;
    // }

    .module-left {
      width: 880px;

      .module-head {
        height: 35px;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .module-title {
          position: relative;
          height: 100%;
          .module-icon {
            height: 30px;
          }
          .icon-title {
            align-self: flex-end;
            margin: 0;
            margin-left: 7px;
            color: #424242;
            font-size: 25px;
            line-height: 25px;
          }
        }

        .module-more {
          font-size: 15px;
          color: #39d7d9;
          text-decoration: none;
        }
      }

      .module-list {
        margin-top: 25px;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        margin-left: -20px;
        .card-common-box {
          margin-left: 20px;
          margin-bottom: 20px;
        }

        // & /deep/ .card-common {
        //   margin-left: 20px;
        //   margin-bottom: 20px;
        // }
      }
    }

    .module-right {
      & /deep/ .title {
        height: 35px;
      }
    }
  }

  // .icon {
  //   &-infomation {
  //     width: 33px;
  //     height: 31px;
  //     background-image: url('../../assets/images/icon_infomation.png');
  //   }

  //   &-novel {
  //     width: 37px;
  //     height: 33px;
  //     background-image: url('../../assets/images/icon_novel.png');
  //   }

  //   &-manga {
  //     width: 37px;
  //     height: 29px;
  //     background-image: url('../../assets/images/icon_manga.png');
  //   }

  //   &-animation {
  //     width: 34px;
  //     height: 33px;
  //     background-image: url('../../assets/images/icon_animation.png');
  //   }

  //   &-other {
  //     width: 36px;
  //     height: 32px;
  //     background-image: url('../../assets/images/icon_other.png');
  //   }
  // }
}
</style>

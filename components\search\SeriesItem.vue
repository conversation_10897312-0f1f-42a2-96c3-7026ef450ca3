<template>
  <!-- eslint-disable vue/no-v-html -->
  <div class="search-searies-item-container">
    <!-- 顶部: 文章信息 -->
    <div class="flex">
      <!-- 图片 -->
      <div
        class="radius-5 left-banner hover horizontal"
        :class="{ vertical: mode === 'vertical' }"
        :style="{ 'background-image': `url(${banner})` }"
        @click="$emit('cover-click')"
      >
        <span v-if="hasIcon" class="lv-icon fs-xs lv-0">
          {{ $t('components.collection_item.collection') }}
        </span>
      </div>
      <div class="cneter-infos flex-1 flex flex-cols jc-between">
        <!-- 标题 -->
        <div class="flex-1">
          <p class="text-hide-1 hover fs-md" @click="$emit('cover-click')">
            {{ title }}
          </p>
        </div>

        <!-- 收藏日期 + 底部信息 -->
        <div class="btm-infos fs-xs text-gray">
          <div class="flex ai-center info-date">
            <img class="author-avatar mar-right-5 hover" :src="avatar" @click="$emit('avatar-click')" />
            <span class="mar-right-20 hover" @click="$emit('avatar-click')">{{ author }}</span>
            <span class="mar-right-20">{{ date }}</span>
            <span class="mar-right-20">{{ groupname }}</span>
          </div>
          <!-- 底部信息 -->
          <div class="mar-top-20 infos-counter">
            <div class="flex">
              <!-- 查看次数 -->
              <div class="counter-item">
                <svg class="svg-icon views" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 730.49 426.84">
                  <path
                    d="M365.22,384.28c-94.2,0-170.85-76.64-170.85-170.85S271,42.58,365.22,42.58s170.85,76.64,170.85,170.85S459.44,384.28,365.22,384.28Zm0-303.06c-72.89,0-132.2,59.31-132.2,132.21s59.31,132.2,132.2,132.2,132.21-59.31,132.21-132.2S438.14,81.22,365.22,81.22Z"
                    transform="translate(-0.01 -0.01)"
                  />
                  <path
                    d="M374,426.85C174.52,426.85,56,308.08,14.22,257h0a62.65,62.65,0,0,1-1.08-78.12C54.56,125.09,171.67,0,364.49,0,558.63,0,675.94,126.32,717.38,180.63a62.79,62.79,0,0,1,1.34,75C681.87,307.1,574.53,426.85,374,426.85ZM44.15,232.55C82.38,279.36,191.07,388.21,374,388.21c182.61,0,279.94-108.46,313.3-155.1a24.32,24.32,0,0,0-.62-29.06c-38-49.73-145.3-165.41-322.17-165.41-175.64,0-282.79,114.55-320.72,163.8a24.16,24.16,0,0,0,.38,30.11Z"
                    transform="translate(-0.01 -0.01)"
                  />
                </svg>
                <span>{{ views }}</span>
              </div>

              <!-- 评论数量 -->
              <div class="counter-item">
                <svg class="svg-icon comments" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 624 447.5">
                  <path
                    d="M515.47,7.24h-407A101.39,101.39,0,0,0,7.24,108.51V339A101.4,101.4,0,0,0,108.48,440.25h407A101.41,101.41,0,0,0,616.76,339V108.51A101.4,101.4,0,0,0,515.47,7.24ZM591.36,339a76,76,0,0,1-75.89,75.89h-407A76,76,0,0,1,32.63,339V108.51a76,76,0,0,1,75.84-75.87h407a76,76,0,0,1,75.89,75.87Z"
                  />
                  <path
                    d="M515.48,447.5h-407A108.64,108.64,0,0,1,0,339V108.51A108.62,108.62,0,0,1,108.48,0h407A108.64,108.64,0,0,1,624,108.51V339A108.66,108.66,0,0,1,515.48,447.5Zm-407-433a94.11,94.11,0,0,0-94,94V339a94.12,94.12,0,0,0,94,94h407a94.15,94.15,0,0,0,94-94V108.51a94.12,94.12,0,0,0-94-94Zm407,407.56h-407A83.2,83.2,0,0,1,25.4,338.92V108.51a83.2,83.2,0,0,1,83.08-83.12h407a83.23,83.23,0,0,1,83.11,83.12V339A83.24,83.24,0,0,1,515.48,422.06Zm-407-382.21a68.69,68.69,0,0,0-68.59,68.62V339a68.71,68.71,0,0,0,68.59,68.65h407A68.72,68.72,0,0,0,584.13,339V108.51a68.71,68.71,0,0,0-68.65-68.62Z"
                  />
                  <circle cx="151.32" cy="115.79" r="26.04" />
                  <path d="M498.72,139.94H231.31a24.16,24.16,0,0,1,0-48.31H498.72a24.16,24.16,0,0,1,0,48.31Z" />
                  <circle cx="151.32" cy="223.02" r="26.04" transform="translate(-81.87 358.15) rotate(-85.93)" />
                  <path d="M439,247.9h-.08l-207.66-.72a24.16,24.16,0,0,1,.07-48.31h.08l207.66.72A24.16,24.16,0,0,1,439,247.9Z" />
                  <path d="M303,354.41H231.31a24.16,24.16,0,0,1,0-48.31H303a24.16,24.16,0,0,1,0,48.31Z" />
                </svg>
                <span>{{ comments }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    mode: {
      // 封面模式(横排|竖排) horizontal | vertical
      type: String,
      default: 'horizontal'
    },
    hasIcon: {
      type: Boolean,
      default: false
    },
    banner: {
      // banner图片地址
      type: String,
      // required: true,
      default: ''
    },
    title: {
      // 标题
      type: [String, Number],
      // required: true,
      default: ''
    },
    date: {
      // 收藏日期
      type: [String, Number],
      default: ''
    },
    groupname: {
      // 分区
      type: String,
      default: ''
    },
    author: {
      // 作者
      type: [String, Number],
      default: ''
    },
    avatar: {
      // 作者头像
      type: String,
      default: ''
    },
    views: {
      // 浏览次数
      type: [String, Number],
      default: 0
    },
    comments: {
      // 评论数
      type: [String, Number],
      default: 0
    },
    noBorder: {
      // 不需要边框
      type: Boolean,
      default: false
    }
  }
};
</script>

<style lang="scss" scope>
.search-searies-item-container {
  border-bottom: 1px solid $gray-white;
  padding: 30px 0;
  position: relative;
  top: 0;
  left: 0;
  .open-btn {
    width: 100px;
    background: #fff;
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 50%);
    .svg-icon {
      fill: $primary;
      width: 13px;
    }
  }
  .left-holder {
    height: 100%;
    visibility: hidden;
    &.vertical {
      // 垂直封面
      width: 88px !important;
    }
    &.horizontal {
      // 水平封面
      width: 160px;
    }
  }
  // padding-bottom: 30px;
  .left-banner {
    position: relative;
    overflow: hidden;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    &.vertical {
      // 垂直封面
      width: 88px !important;
      height: 125px !important;
    }
    &.horizontal {
      // 水平封面
      width: 160px;
      height: 100px;
    }
    .lv-icon {
      position: absolute;
      top: 5px;
      right: 5px;
    }
  }
  .cneter-infos {
    padding: 0 15px;
    .btm-infos {
      .info-date {
        .author-avatar {
          width: 20px;
          height: 20px;
          border-radius: 100%;
        }
      }
      .counter-item {
        display: inline-flex;
        align-items: center;
        margin-right: 20px;
        .svg-icon {
          margin-right: 5px;
          width: 15px;
          fill: $gray;
          // sizes
          &.coins,
          &.collections,
          &.share,
          &.zan {
            width: 13px;
          }

          // fill colors
          &.coins {
            fill: #fadd52;
          }
          &.collections {
            fill: #b6ed6f;
          }
          &.share {
            fill: #69cff9;
          }
          &.zan {
            fill: #ffb236;
          }
        }
      }
    }
  }

  // 右边按钮
  $btnsboxw: 160px;
  $btnw: 70px;
  .right-btns {
    width: $btnsboxw;
    .btn {
      width: $btnw;
      height: 30px;
      border-radius: 30px;
    }
  }

  // 底部页面的
  .page-list-container {
    .page-index {
      padding-top: 15px;
      .page-item {
        padding: 15px 0;
        .index-sort {
          margin: 0 10px;
          width: 10px;
          &:hover {
            cursor: crosshair;
          }
          .svg-icon {
            width: 8px;
            fill: $gray;
            &.down {
              margin-top: 2px;
              transform: rotate(180deg);
            }
          }
        }
        .title {
          color: $gray;
        }
        .btns {
          width: $btnsboxw;
          .btn-item {
            width: $btnw;
          }
        }
      }
    }
  }
}
</style>

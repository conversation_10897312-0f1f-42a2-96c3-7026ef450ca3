/* eslint-disable */
CKEDITOR.plugins.add('myupload', {
  icons: 'myupload',
  init: function(editor) {
    editor.addCommand('myupload', {
      exec: function(editor) {
        var myEvent = new CustomEvent('ckeditormyupload');
        // 在 window 上触发事件
        if (window.dispatchEvent) {
          window.dispatchEvent(myEvent);
        } else {
          window.fireEvent(myEvent);
        }
      }
    });
    editor.ui.addButton('myupload', {
      label: '上传文件',
      command: 'myupload',
      toolbar: 'insert'
    });
  }
});

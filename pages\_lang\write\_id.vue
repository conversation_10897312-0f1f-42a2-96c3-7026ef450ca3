<template>
  <!-- 上标 -->
  <div class="write-page">
    <el-dialog
      :title="$t('write.up_mark')"
      :fullscreen="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :visible="showRubyLayer"
      class="crop-dialog"
    >
      <div>
        <p class="mar-bottom-20">
          <input type="text" class="input-control" readonly :value="selectionContents[1]" />
        </p>
        <p class="mar-bottom-20">
          <input v-model="rubyContent" type="text" class="input-control" :placeholder="$t('write.up_mark_content')" />
        </p>
        <div slot="footer" class="dialog-footer flex jc-center">
          <button class="btn btn-primary mar-right-15" @click="rubyConfirm">{{ $t('write.btn_confirm') }}</button>
          <button class="btn btn-primary border" @click="showRubyLayer = false">{{ $t('write.btn_cancel') }}</button>
        </div>
      </div>
    </el-dialog>

    <!-- 裁剪图片 -->
    <el-dialog
      title=""
      width="90%"
      top="10vh"
      :fullscreen="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :visible="showCrop"
      class="crop-dialog"
    >
      <div id="crop-container">
        <div class="crop-box">
          <!-- 竖排封面裁剪 -->
          <vue-cropper
            v-if="verticalMode || verticalModeCollection"
            ref="cropper"
            mode="cover"
            :img="cropOptions.img"
            :output-size="cropOptions.outputSize"
            :output-type="cropOptions.outputType"
            :full="cropOptions.full"
            :high="cropOptions.high"
            :fixed="cropOptions.fixed"
            :can-move="cropOptions.canMove"
            :can-move-box="cropOptions.canMoveBox"
            :fixed-box="cropOptions.fixedBox"
            :original="cropOptions.original"
            :center-box="cropOptions.centerBox"
            :auto-crop="true"
            :fixed-number="[cropperSize1.w / cropperSize1.h, 1]"
            @real-time="realTimePreview"
          />
          <!-- 横排封面裁剪 -->
          <vue-cropper
            v-else
            ref="cropper"
            mode="cover"
            :img="cropOptions.img"
            :output-size="cropOptions.outputSize"
            :output-type="cropOptions.outputType"
            :full="cropOptions.full"
            :high="cropOptions.high"
            :fixed="cropOptions.fixed"
            :can-move="cropOptions.canMove"
            :can-move-box="cropOptions.canMoveBox"
            :fixed-box="cropOptions.fixedBox"
            :original="cropOptions.original"
            :auto-crop="true"
            :fixed-number="[cropperSize2.w / cropperSize2.h, 1]"
            :center-box="cropOptions.centerBox"
            @real-time="realTimePreview"
          />

          <!-- 放大缩小 -->
          <div class="scale-btns">
            <button class="btn-img-scale btn btn-primary" @click="scaleImage(1)">{{ $t('write.scale_max') }}</button>
            <button class="btn-img-scale btn btn-primary border" @click="scaleImage(-1)">{{ $t('write.scale_min') }}</button>
          </div>
        </div>
        <div class="preview-box" :style="corpPreviews.div">
          <img :src="corpPreviews.url" :style="corpPreviews.img" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <button class="btn btn-primary" @click="cropImage(true)">{{ $t('write.btn_confirm') }}</button>
        <button class="btn btn-primary border" @click="cropImage(false)">{{ $t('write.btn_cancel') }}</button>
      </div>
    </el-dialog>

    <!-- 隐藏的文件上传组件: 手动触发 -->
    <div v-show="false">
      <!-- 上传文章附件 -->
      <el-upload
        :action="uploadAttachmentURL"
        :data="uploadImageJSONData"
        :before-upload="attachmentBeforeUpload"
        :on-success="attachmentUploadSuccess"
        :multiple="false"
      >
        <button id="upload-article-contents-ext-file-btn" slot="trigger" style="display: none;"></button>
      </el-upload>
      <!-- 上传文章内容图片 -->
      <el-upload
        :action="uploadURL"
        :data="uploadImageJSONData"
        :before-upload="beforeUpload"
        :on-success="contentImgUploadSuccess"
        :multiple="true"
        list-type="picture"
        accept="image/*"
      >
        <button id="upload-article-contents-img-btn" slot="trigger" style="display: none;"></button>
      </el-upload>
      <!-- 上传文章封面图片 -->
      <el-upload
        :action="uploadURL"
        :data="uploadImageJSONData"
        :before-upload="beforeBannerUpload"
        :on-success="coverUploadSuccess"
        :on-error="coverUploadError"
        accept="image/*"
      >
        <button id="upload-article-banner-img-btn" slot="trigger"></button>
      </el-upload>
      <!-- 上传文章合集封面图片 -->
      <el-upload
        :action="uploadURL"
        :data="uploadImageJSONData"
        :before-upload="beforeBannerUpload"
        :on-success="coverUploadSuccess"
        :on-error="coverUploadError"
        accept="image/*"
      >
        <button id="upload-collection-img-btn" slot="trigger"></button>
      </el-upload>
    </div>

    <!-- 表情 -->
    <el-drawer title direction="ttb" :visible.sync="showEmojiDrawer" :with-header="false" size="50%">
      <EmojiPanel @input-emoji="inputEmoji" />
    </el-drawer>

    <!-- 投票  -->
    <el-dialog :visible="showVoteDialog" :close-on-click-modal="false" top="10vh" width="580px" class="votes-dialog" @close="closeVoteLayer">
      <p slot="title" class="fs-ml vote-title text-dark">
        <span>{{ $t('write.votes') }}</span>
      </p>

      <!-- 投票: 标题 -->
      <div class="item">
        <p class="sub-title">
          {{ $t('write.vote_title') }}
          <span class="require">*</span>
        </p>
        <div class="flex">
          <input v-model="vote.title" type="text" class="input-control" max="20" maxlength="20" :placeholder="$t('write.input_vote_title')" />
          <span class="flex ai-center text-counter">{{ 20 - vote.title.length }}</span>
        </div>
      </div>

      <!-- 投票: 类型 -->
      <div class="item">
        <div class="sub-title">
          <span>{{ $t('write.vote_type') }}</span>
          <span class="require mar-right-20">*</span>
          <div class="checkboxs flex-inline ai-center">
            <p class="mar-right-20 flex" @click="isMultipleChoose = false">
              <span class="radio-btn" :class="{ checked: !isMultipleChoose }"></span>
              <span>{{ $t('write.single_select') }}</span>
            </p>
            <p class="mar-right-20 flex" @click="isMultipleChoose = true">
              <span class="radio-btn" :class="{ checked: isMultipleChoose }"></span>
              <span>{{ $t('write.multiple_select') }}</span>
            </p>
            <p v-show="isMultipleChoose" class="mar-right-20" @click="isMultipleChoose = true">
              <input
                v-model="vote.max_choices"
                :placeholder="$t('write.max_select_options')"
                type="number"
                min="2"
                class="input-control"
                style="min-width: 120px"
                :max="vote.options.length"
              />
            </p>
          </div>
        </div>
      </div>

      <!-- 投票: 时长 -->
      <div class="item">
        <div class="sub-title">
          <span>{{ $t('write.vote_expried_time') }}</span>
          <span class="require mar-right-20">*</span>
          <div class="checkboxs flex-inline">
            <p class="mar-right-20 flex" @click="vote.expiration = 3">
              <span class="radio-btn" :class="{ checked: vote.expiration === 3 }"></span>
              <span>3 {{ $t('write.day') }}</span>
            </p>
            <p class="mar-right-20 flex" @click="vote.expiration = 5">
              <span class="radio-btn" :class="{ checked: vote.expiration === 5 }"></span>
              <span>7 {{ $t('write.day') }}</span>
            </p>
            <p class="mar-right-20 flex" @click="vote.expiration = 7">
              <span class="radio-btn" :class="{ checked: vote.expiration === 7 }"></span>
              <span>30 {{ $t('write.day') }}</span>
            </p>
          </div>
        </div>
      </div>

      <!-- 投票: 选项 -->
      <div class="vote-items">
        <div class="item" style="marigin: 0; padding:0">
          <p class="sub-title">
            {{ $t('write.vote_options') }}
            <span class="require">*</span>
          </p>
          <div v-for="(item, index) in vote.options" :key="index" style="margin-bottom: 10px" class="flex">
            <input v-model="vote.options[index]" type="text" class="input-control" maxlength="12" :placeholder="$t('write.input_option_title')" />
            <p class="flex ai-center text-counter">{{ 12 - item.length }}</p>
            <button class="flex btn btn-del ai-center text-counter" @click="delVoteItem(index)">
              <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 552.7 576">
                <path
                  d="M656.8,191.1h-513c-10.8,0-19.6,9.9-19.6,22.1s8.8,22.1,19.6,22.1h31.7V565.2c0,67.4,48.7,122.3,108.6,122.3H516.5c59.9,0,108.6-54.9,108.6-122.3V235.3h31.7c10.8,0,19.6-9.9,19.6-22.1S667.6,191.1,656.8,191.1ZM311.4,648.2H284c-40.7,0-73.7-37.2-73.7-83V235.3H311.5V648.2Zm143.9,0H345.2V235.3H455.3V648.2Zm134.9-83c0,45.8-33.1,83-73.7,83H489.1V235.3H590.3V565.2Z"
                  transform="translate(-123.95 -111.75)"
                />
                <path
                  class="cls-1"
                  d="M357.6,156.8h85.3a22.4,22.4,0,1,0,0-44.8H357.6a22.4,22.4,0,0,0,0,44.8Z"
                  transform="translate(-123.95 -111.75)"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
      <div class="item">
        <button class="btn text-primary" @click="addVoteItem">{{ $t('write.add_options') }}</button>
      </div>

      <!-- 投票: 底部按钮 -->
      <p slot="footer" class="dialog-footer flex ai-center jc-center">
        <button class="btn btn-publish btn-primary" @click="confirmVote">{{ $t('write.confirm') }}</button>
      </p>
    </el-dialog>

    <!-- ======================================== 新建合集 ======================================== -->
    <el-dialog
      :visible="showCollectionDialog"
      :close-on-click-modal="false"
      top="10vh"
      width="580px"
      class="new-collection-dialog"
      @close="showCollectionDialog = false"
    >
      <p slot="title" class="fs-ml text-dark">
        <span>{{ $t('write.new_collection') }}</span>
        <span class="fs-sm text-gray">{{ $t('write.update_collection_tips') }}</span>
      </p>
      <!-- 新建合集: 分区 -->
      <div class="item">
        <p class="sub-title">
          {{ $t('write.category') }}
          <span class="require">*</span>
        </p>
        <select class="select mar-right-20" @change="selectCollectionSuperGroup">
          <option v-for="(item, index) in articleConfigs.groups" :key="index" :value="index">{{ item.name }}</option>
        </select>
        <select v-model="collection.gid" class="select">
          <option v-for="(item, index) in subCollectionGroups" :key="index" :value="item.gid">{{ item.name }}</option>
        </select>
      </div>
      <!-- 新建合集: 名称 -->
      <div class="item">
        <p class="sub-title">
          {{ $t('write.collection_name') }}
          <span class="require">*</span>
        </p>
        <div class="flex">
          <input v-model="collection.name" type="text" class="input-control" maxlength="80" :placeholder="$t('write.input_collection_name')" />
          <p class="flex ai-center text-counter">{{ 80 - collection.name.length }}</p>
        </div>
      </div>
      <!-- 新建合集: 作者 -->
      <div class="item">
        <p class="sub-title">{{ $t('write.author') }}</p>
        <div class="flex">
          <input v-model="collection.author" type="text" class="input-control" :placeholder="$t('write.input_collection_author')" />
          <p class="flex ai-center text-counter">{{ 20 - collection.author.length }}</p>
        </div>
      </div>
      <!-- 新建合集: 简介 -->
      <div class="item">
        <p class="sub-title">{{ $t('write.intro') }}</p>
        <div class="flex">
          <textarea
            v-model="collection.intro"
            type="text"
            class="collection-intro input-control"
            :placeholder="$t('write.input_collection_intro')"
            maxlength="150"
          ></textarea>
          <p class="flex ai-center text-counter">{{ 150 - collection.intro.length }}</p>
        </div>
      </div>
      <!-- 新建合集: 单选项 -->
      <!-- <div class="item">
        <p class="sub-title" @click="showName = !showName">
          <span class="radio-btn" :class="{ checked: showName === true }"></span>
          <span>不允许合集内帖子从分区页直接进入合集主页</span>
        </p>
      </div> -->
      <!-- 新建合集: 封面 -->
      <div class="item">
        <p class="sub-title">
          {{ $t('write.intro') }}
          <span>{{ $t('write.use_default_banner') }}</span>
        </p>
        <div class="select-banner btn flex ai-center jc-center" :class="{ vertical: verticalModeCollection }" @click="selectCover(true)">
          <!-- 0: 默认情况 1:上传中(显示loading)  2:上传成功(显示图片) -->
          <svg v-if="collectionCoverState === 0" class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 626.7 626.7">
            <path
              d="M646.4,86.9H154.1a67.28,67.28,0,0,0-67.2,67.2V646.4a67.28,67.28,0,0,0,67.2,67.2H646.4a67.28,67.28,0,0,0,67.2-67.2V154.1A67.28,67.28,0,0,0,646.4,86.9ZM154.1,134H646.4a20.1,20.1,0,0,1,20.1,20.1V496.6L580.2,330.4c-16.8-32.4-61.9-35.7-83.3-6L415.8,437.3a25.15,25.15,0,0,1-34.1,6.5l-76.9-49.2a78.68,78.68,0,0,0-107,21.4L134,508V154.1A20.1,20.1,0,0,1,154.1,134Z"
              transform="translate(-86.9 -86.9)"
            />
          </svg>
          <Loading v-else-if="collectionCoverState === 1" />
          <img v-else-if="collectionCoverState === 2" :src="collectionCover" class="cover-img" />
        </div>
      </div>
      <!-- 新建合集: 底部按钮 -->
      <p slot="footer" class="dialog-footer flex ai-center jc-center">
        <button class="btn btn-publish btn-primary" @click="createCollection">{{ $t('write.confirm') }}</button>
      </p>
    </el-dialog>
    <!-- ==================== 新建合集结束 ==================== -->

    <!-- 顶部: 发布帖子 -->
    <div class="top-tips">
      <p class="tip text-primary fs-md flex jc-center ai-center">{{ $t('write.publich_article') }}</p>
    </div>
    <div class="contents">
      <!-- 分区 -->
      <div class="item">
        <p class="sub-title fs-md">
          {{ $t('write.category') }}
          <span class="require">*</span>
        </p>
        <select v-model="article.gid" class="select mar-right-20" @change="selectSuperGroup">
          <option v-for="(item, index) in articleConfigs.groups" :key="index" :value="item.gid">{{ item.name }}</option>
        </select>
        <select v-model="article.group_id" class="select mar-right-20">
          <option v-for="(item, index) in subGroups" :key="index" :value="item.gid">{{ item.name }}</option>
        </select>
      </div>

      <!-- 合集 -->
      <div class="item">
        <p class="sub-title fs-md">
          {{ $t('write.collection') }}
          <button class="btn new-collection fs-sm text-primary" @click="showCollectionDialog = true">{{ $t('write.new_collection') }}</button>
        </p>
        <select v-if="series.length" v-model="article.series_id" class="select mar-right-20 padrig-30">
          <option v-for="(item, key) in series" :key="key" :value="item.sid" :selected="isEditMode && item.sid === article.series_id">{{
            item.name
          }}</option>
        </select>
        <select v-else class="select mar-right-20">
          <option value selected disabled>{{ $t('write.please_select') }}</option>
        </select>
      </div>

      <!-- 标题 -->
      <div class="item">
        <p class="sub-title fs-md">
          {{ $t('write.title') }}
          <span class="require">*</span>
        </p>
        <div class="flex">
          <input v-model="article.title" maxlength="150" type="text" class="input-control" :placeholder="$t('write.input_title')" />
          <p class="text-counter">{{ 150 - article.title.length }}</p>
        </div>
      </div>

      <!-- 正文 -->
      <div class="item">
        <p class="sub-title fs-md sub-titleCon">
          <span>
            {{ $t('write.content') }}
            <span class="require">*</span>
          </span>
          <span v-if="!isEditMode" class="require-right">{{ $t('write.cache-cont') }}</span>
        </p>

        <!-- 已经上传的内容图片 -->
        <ul v-show="sortContentImages" class="uploaded-imgs-wrapper flex jc-between flex-wrap">
          <li v-for="item in sortContentImages" :key="item.id" class="img-item-wrapper flex jc-start ai-center hover">
            <div class="btn-box hover" @click="removeContentImg(item.id)">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 800 800">
                <path
                  d="M447.5,400l307.2-312c7.2-7.3,11.1-16.9,11.1-27.1c0-10.2-4-19.8-11.1-27.1c-7.4-7.5-17.4-11.7-28.1-11.7
	c-10.7,0-20.7,4.2-28.1,11.7L400,336.9L101.4,33.7C93.9,26.1,84,22,73.3,22s-20.7,4.2-28.1,11.7c-14.7,14.9-14.7,39.3,0,54.2
	L352.5,400L45.3,712.1c-7.2,7.3-11.1,16.9-11.1,27.1c0,10.2,4,19.8,11.1,27.1c7.4,7.5,17.4,11.7,28.1,11.7
	c10.7,0,20.7-4.2,28.1-11.7L400,463.1l298.6,303.3c7.4,7.5,17.4,11.7,28.1,11.7s20.7-4.2,28.1-11.7c14.7-14.9,14.7-39.3,0-54.2
	L447.5,400z"
                />
              </svg>
            </div>
            <img :src="item.url" />
            <p class="flex-1 dis-select text-gray text-hide-1" @click="appendImgToContents(item.url)">{{ item.name }}</p>
          </li>
        </ul>

        <!-- 已经上传的附件 -->
        <ul v-show="contentAttachment.length" class="uploaded-imgs-wrapper flex jc-between flex-wrap">
          <li v-for="(item, key) in contentAttachment" :key="key" class="img-item-wrapper flex jc-start ai-center">
            <span class="file-tag lv lv-icon lv-0">{{ $t('write.attachment') }}</span>
            <div v-if="item.id" class="btn-box hover" @click="removeAttachment(item.id)">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 800 800">
                <path
                  d="M447.5,400l307.2-312c7.2-7.3,11.1-16.9,11.1-27.1c0-10.2-4-19.8-11.1-27.1c-7.4-7.5-17.4-11.7-28.1-11.7
	c-10.7,0-20.7,4.2-28.1,11.7L400,336.9L101.4,33.7C93.9,26.1,84,22,73.3,22s-20.7,4.2-28.1,11.7c-14.7,14.9-14.7,39.3,0,54.2
	L352.5,400L45.3,712.1c-7.2,7.3-11.1,16.9-11.1,27.1c0,10.2,4,19.8,11.1,27.1c7.4,7.5,17.4,11.7,28.1,11.7
	c10.7,0,20.7-4.2,28.1-11.7L400,463.1l298.6,303.3c7.4,7.5,17.4,11.7,28.1,11.7s20.7-4.2,28.1-11.7c14.7-14.9,14.7-39.3,0-54.2
	L447.5,400z"
                />
              </svg>
            </div>
            <p class="flex-1 dis-select text-gray text-hide-1">{{ item.name }}</p>
          </li>
        </ul>

        <!-- --- 编辑器 --- -->
        <!-- <input id="__ck_editor__" style="display: none" /> -->
        <input id="sceditor" type="text" style="display: none; height: 800px; width: 100%;" />

        <!-- 视频 -->
        <el-dialog
          :visible="showVideoDialog"
          :close-on-click-modal="true"
          width="340px"
          class="video-dialog"
          :modal="false"
          top="6.5vh"
          @close="closeVideoLayer"
        >
          <div>
            <div class="three-btn">
              <!-- <div class="btn" :class="{ active: itemIndex === 1 }" @click="itemIndex = 1">种子</div>
              <div class="btn" :class="{ active: itemIndex === 0 }" @click="itemIndex = 0">磁力</div> -->
              <div class="btn" :class="{ active: itemIndex === 2 }" @click="itemIndex = 2">第三方</div>
            </div>
            <!-- iframe视频上传 -->
            <div v-if="itemIndex === 2">
              <div unselectable="on">
                <label for="link" unselectable="on">
                  <font style="vertical-align: inherit;">
                    <div class="radio-box flex">
                      <span>请选择视频尺寸:</span>
                      <label><input v-model="videoSize" type="radio" name="male" value="0" /> {{ $t('write.big') }}</label>
                      <label><input v-model="videoSize" type="radio" name="male" value="1" /> {{ $t('write.mid') }}</label>
                      <label><input v-model="videoSize" type="radio" name="male" value="2" /> {{ $t('write.min') }}</label>
                    </div>
                    <font style="vertical-align: inherit;">{{ $t('write.embed_code') }}</font>
                  </font>
                </label>
                <input v-model="videoUrl" class="link-input" type="text" dir="ltr" />
              </div>
              <div unselectable="on" style="margin-top:10px">
                <font style="vertical-align: inherit;">
                  <font style="vertical-align: inherit;">
                    <input type="button" class="button" :value="$t('write.define')" @click.prevent="closeVideoBtn" />
                  </font>
                </font>
              </div>
            </div>
            <!-- 磁力链上传 -->
            <div v-if="itemIndex === 0">
              <div unselectable="on">
                <label for="link" unselectable="on">
                  <font style="vertical-align: inherit;">
                    <div class="radio-box flex">
                      <span>{{ $t('write.choose-size') }}</span>
                      <label><input v-model="videoSize" type="radio" name="male" value="0" /> {{ $t('write.big') }}</label>
                      <label><input v-model="videoSize" type="radio" name="male" value="1" /> {{ $t('write.mid') }}</label>
                      <label><input v-model="videoSize" type="radio" name="male" value="2" /> {{ $t('write.min') }}</label>
                    </div>
                    <font style="vertical-align: inherit;">
                      {{ $t('write.insert-tip') }}
                      <span class="btn" style="color:#39d7d9;" @click="toPage('/detail/1044945')">{{ $t('write.use_before') }}</span></font
                    >
                  </font>
                </label>
                <input v-model="torrentUrl" class="link-input" placeholder="magnet:" type="text" dir="ltr" />
                <div class="log">
                  <span class="btn" style="color:#39d7d9;" @click="showTorrentBtn">{{ $t('write.video-show') }}</span>
                </div>
              </div>
              <div unselectable="on" style="margin-top:10px">
                <font style="vertical-align: inherit;">
                  <font style="vertical-align: inherit;">
                    <input type="button" class="button" :value="$t('write.define')" @click.prevent="closeBtn" />
                  </font>
                </font>
              </div>
            </div>
            <!-- 种子文件上传 -->
            <div v-if="itemIndex === 1">
              <div unselectable="on">
                <label for="link" unselectable="on">
                  <font style="vertical-align: inherit;">
                    <div class="radio-box flex">
                      <span>{{ $t('write.choose-size') }}</span>
                      <label><input v-model="videoSize" type="radio" name="male" value="0" /> {{ $t('write.big') }}</label>
                      <label><input v-model="videoSize" type="radio" name="male" value="1" /> {{ $t('write.mid') }}</label>
                      <label><input v-model="videoSize" type="radio" name="male" value="2" /> {{ $t('write.min') }}</label>
                    </div>
                  </font>
                </label>
                <label class="torrent" style="text-align: start"> {{ $t('write.video-title') }}<input ref="inputer" type="file" dir="ltr"/></label>
                <div class="log">
                  <!-- <Loading v-show="loadingComments" /> -->
                  <div class="btn" style="margin-bottom:10px;">
                    {{ $t('write.video-upload') }}<span style="color:#39d7d9;" @click="toPage('/detail/1044945')"> {{ $t('write.use_before') }}</span>
                  </div>
                  <span class="btn" style="color:#39d7d9;" @click="intoTorentFile">{{ $t('write.video-show') }}</span>
                </div>
              </div>
              <div unselectable="on" style="margin-top:10px">
                <font style="vertical-align: inherit;">
                  <font style="vertical-align: inherit;">
                    <input type="button" class="button" :value="$t('write.define')" @click.prevent="closeBtn" />
                  </font>
                </font>
              </div>
            </div>
          </div>
        </el-dialog>
      </div>

      <!-- 文章封面 -->
      <div class="item">
        <p class="sub-title fs-md">
          <span class="mar-right-20">{{ $t('write.banner') }}</span>
          <span class="fs-sm text-gray">{{ $t('write.use_default_banner') }}</span>
        </p>
        <div class="select-banner btn flex ai-center jc-center" :class="{ vertical: verticalMode }" @click="selectCover(false)">
          <!-- 0: 默认情况 1:上传中(显示loading)  2:上传成功(显示图片) -->
          <svg v-if="articleCoverState === 0" class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 626.7 626.7">
            <path
              d="M646.4,86.9H154.1a67.28,67.28,0,0,0-67.2,67.2V646.4a67.28,67.28,0,0,0,67.2,67.2H646.4a67.28,67.28,0,0,0,67.2-67.2V154.1A67.28,67.28,0,0,0,646.4,86.9ZM154.1,134H646.4a20.1,20.1,0,0,1,20.1,20.1V496.6L580.2,330.4c-16.8-32.4-61.9-35.7-83.3-6L415.8,437.3a25.15,25.15,0,0,1-34.1,6.5l-76.9-49.2a78.68,78.68,0,0,0-107,21.4L134,508V154.1A20.1,20.1,0,0,1,154.1,134Z"
              transform="translate(-86.9 -86.9)"
            />
          </svg>
          <Loading v-else-if="articleCoverState === 1" />
          <img v-else-if="articleCoverState === 2" :src="articleCover" class="cover-img" />
        </div>
      </div>

      <!-- 仅正式会员可见 -->
      <div class="item extra-options">
        <div class="flex">
          <span class="radio-btn btn" :class="{ checked: article.only_passer }" @click="article.only_passer = !article.only_passer"></span>
          <span class="flex-1">{{ $t('write.only_brave') }}</span>
        </div>
      </div>

      <!-- 内容锁定 -->
      <div class="item extra-options">
        <div class="flex">
          <span class="radio-btn btn" :class="{ checked: contentLock }" @click="contentLock = !contentLock"></span>
          <div>
            <p>{{ $t('write.post_price') }}</p>
            <div v-show="contentLock">
              <div class="payinfo flex">
                <div class="left">
                  <span>{{ $t('write.payinfo_coin') }}</span>
                  <span class="require">*</span>
                </div>
                <div class="right">
                  <input v-model="pay_info.coin" type="number" min="1" class="input-control" :placeholder="$t('write.input_integer')" />
                  <span>{{ $t('write.ge') }}</span>
                </div>
              </div>
              <div class="payinfo flex">
                <div class="left">
                  <span>{{ $t('write.payinfo_node') }}</span>
                  <span class="require">*</span>
                </div>
                <div class="right">
                  <input
                    v-model="pay_info.node"
                    type="number"
                    class="input-control"
                    min="10"
                    max="90"
                    step="10"
                    :placeholder="$t('write.input_100_integer')"
                    @blur="check_node()"
                  />
                  <span>%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 发布|更新 -->
      <p class="flex jc-center">
        <button v-if="isEditMode" class="btn btn-primary btn-publish" @click="updateArticle">{{ $t('write.confirm_save') }}</button>
        <button v-else class="btn btn-primary btn-publish" @click="createArticle">{{ $t('write.confirm_publish') }}</button>
      </p>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import _ from 'lodash';
import EmojiPanel from '@/components/EmojiPanel.vue';
import Loading from '@/components/Loading';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  layout: 'profile',
  middleware: ['auth'],
  components: {
    EmojiPanel,
    Loading
  },

  async fetch({ params, store }) {
    if (!params.id) {
      await store.dispatch('write/getArticleConfigs');
      await store.dispatch('emoji/getEmojis');
    }
  },

  async asyncData({ params, $axios, store, redirect }) {
    const data = {
      isEditMode: false
    };

    if (params.id) {
      // 修改文章
      data.isEditMode = true;
      const articleRes = await $axios.$post('/api/article/get-edit-detail', {
        aid: params.id
      });
      if (articleRes.code === 0) {
        await store.dispatch('write/getArticleConfigs');
        await store.dispatch('emoji/getEmojis');
        data.editArticle = articleRes.data;
      } else {
        return redirect('/write');
      }
    }

    return data;
  },

  data: () => ({
    showName: false, // 从分区内帖子进入
    series: [], // 合集数据
    uploadedImages: [], // 已经上传的内容图片(但是用户不能修改, 也不会影响UI, 所以直接放到 this 上)
    tempPreviewResMap: {}, // 预览的时候需要的资源Map
    tempContentImagesMap: [], // 内容图片 [{占位符, res_id, res_url}]
    contentAttachment: [], // 已经上传的附件
    contentImages: [], // 已经存在或者上传成功的文章内容图片 {id, url, name}
    isEditMode: false, // 是否是编辑文章
    editArticle: { title: '' }, // 修改文章
    rubyContent: '', // 上标内容
    selectionContents: [], // 编辑器内容数组
    showRubyLayer: false, // 显示上标
    subCollectionGroups: [], // 新建合集子分区
    uploadFormData: { md5: '', security_key: '' }, // 上传图片需要的参数
    showCrop: false, // 是否显示裁剪dialog
    corpPreviews: {}, // 裁剪的预览数据
    cropedFile: null, // 裁剪完之后的 File 对象
    cropedUploadConfirm: false, // 是否确定要上传, false 取消上传
    cropperSize1: {
      // 竖排封面尺寸
      w: 420 / 3,
      h: Math.ceil(590 / 3)
    },
    cropperSize2: {
      // 横排封面尺寸
      w: Math.ceil(716 / 3),
      h: 420 / 3
    },
    cropOptions: {
      img: '', // 要裁剪的图片地址
      outputType: '', // 图片裁剪后类型[jpeg|png]
      outputSize: 0.5, // 图片裁剪后质量
      size: 1, // 图片裁剪质量 0 - 1
      canMove: true, // 是否能移动图片
      canMoveBox: true, // 是否能移动裁剪框
      fixed: true, // 固定缩放比例
      fixedBox: false, // 是否固定裁剪框大小
      original: true, // 裁剪后图片按照原始比例渲染
      fixedNumber: [1, 0.72], // 缩放比例
      autoCropWidth: 210, // 裁剪框的宽度
      autoCropHeight: 295, // 裁剪框的高度
      centerBox: true, // 裁剪框是否被限制在图片里面
      full: true, // 是否输出原图比例的截图
      high: false // 	是否按照设备的dpr 输出等比例图片
    },
    subGroups: [], // 子分区
    verticalMode: false, // 是否是竖排封面
    verticalModeCollection: false, // 合集的封面是否是竖排的
    showCollectionDialog: false, // 显示新建合集
    showEmojiDrawer: false, // 是否显示表情弹框
    collectionCoverState: 0, // 合集 封面 上传框状态 0:未上传 1:上传中 2:上传成功
    collectionCover: '', // 合集封面url
    collection: {
      name: '',
      title: '',
      author: '',
      intro: ''
    },
    showVideoDialog: false, // 是否显示视频窗口
    videoUrl: '', // 输入的视频地址
    torrentUrl: '', // 输入磁力链
    showVoteDialog: false, // 是否显示投票弹窗
    isMultipleChoose: false, // 是否多选
    contentLock: false, // 内容锁定
    hasVotes: false, // 是否有投票
    articleCoverState: 0, // 文章 封面 上传框状态 0:未上传 1:上传中 2:上传成功
    articleCover: '', // 上传成功后的图片
    vote: {
      // 投票
      title: '',
      max_choices: '',
      expiration: 3,
      options: ['', '']
    },
    pay_info: {
      coin: '',
      node: ''
    },
    article: {
      title: '', // 文章标题
      content: '', // 文章正文
      banner_id: '', // 文章封面
      cover_id: '', // 封面id（文章图片的resid）
      gid: '', // 文章分区
      series_id: '', // 合集ID
      only_passer: false // 是否正式会员可见 0否 1是
    },
    videoSize: 0, // 此时的尺寸
    timer: null, // 缓存
    itemIndex: 2, // 默认是0，0磁力链，1种子，2第三方
    // client: {}, // webtorrent对象
    time: 0, // 缓存时间
    magnetURI: null // 磁力链或者种子文件解析的磁力链或作为开关
  }),

  computed: {
    ...mapGetters('login', ['loginUser']),
    ...mapGetters('write', ['articleConfigs']),
    // 上传文章内容图片地址
    uploadURL() {
      return `${process.env.VUE_APP_UPLOAD_URL}/upload/article-image`;
    },
    // 上传文章内容图片地址
    uploadAttachmentURL() {
      return `${process.env.VUE_APP_UPLOAD_URL}/upload/article-file`;
    },
    uploadImageJSONData() {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.uploadFormData.security_key = this.loginUser.security_key;
      const data = this.apiParamsGenerator(this.uploadFormData, true);
      return data;
    },
    sortContentImages() {
      if (!this.contentImages.length) return false;
      return _.sortBy(this.contentImages, ['name']);
    }
  },

  watch: {
    // 通过大分区id找到合集
    'article.gid'(val) {
      if (!val) this.series = [];
      const series = this.articleConfigs.series;
      if (series && series.length) {
        this.series = series.filter((item) => item.parent_gid === val);
      }
    }
  },

  mounted() {
    // 浏览器环境下: 初始化编辑器
    if (process.browser) {
      this.lastTriggerTime = 0;
      // this.initCkEditor();
      this.initSCEditor();
      // this.initWebTorrent();
      if (this.isEditMode) {
        /* eslint-disable */
        const { groups } = this.articleConfigs;
        const { banner, cover, group_id } = this.editArticle;
        this.articleCoverState = 2;
        this.articleCover = cover || banner;
        for (let i = 0; i < groups.length; i++) {
          if (groups[i].items.hasOwnProperty(group_id)) {
            this.selectSuperGroup({ target: { value: groups[i].gid } });
            break;
          }
        }
        this.selectCollectionSuperGroup(true);
        /* eslint-enable */
      } else {
        this.selectSuperGroup(true);
        this.selectCollectionSuperGroup(true);
      }
    }
    // 进入页面先读缓存,根据isEditMode判断是不是旧的帖子
    if (!this.isEditMode) {
      this.readCache();
      // 每隔3分钟自动缓存文章内容一次
      this.timer = setInterval(() => {
        this.toCache();
      }, 180000); // 180000
    }
  },

  beforeDestroy() {
    // 组件销毁清除计数器
    clearInterval(this.timer);
  },

  methods: {
    // 读缓存
    readCache() {
      if (storage.get('article')) {
        this.article = storage.get('article');
        this.sceditor.val(this.article.content);
      }
    },
    // 缓存方法
    toCache() {
      this.article.content = this.contentConverter(this.sceditor.val());
      storage.set('article', this.article);
    },
    // 显示 ruby 操作弹窗
    showRubyDialog() {
      const rangeHelper = this.sceditor.getRangeHelper();
      this.range = rangeHelper.cloneSelected();
      const { startOffset: start, endOffset: end } = this.range;
      if (start === end) {
        this.$message.error(this.$t('write.select_up_content'));
        return;
      }
      if (this.range.startContainer !== this.range.endContainer) {
        this.$message.error(this.$t('write.selected_cannot_ruby'));
        return;
      }
      const content = this.range.startContainer.data;
      this.selectionContents.push(content.substring(0, start));
      this.selectionContents.push(content.substring(start, end));
      this.selectionContents.push(content.substring(end, content.length));
      this.showRubyLayer = true;
    },

    // 初始化editor
    initSCEditor() {
      /*
      自定义 toolbar 按钮命令, 如果修改图标,
      需要修改 /static/sceditor/js/myicons.js
      window.sceditor.command.set('命令名称', {
        tooltip: '鼠标 hover 显示的 title',
        exec: '编辑模式: 按钮被点击时执行的函数',
        txtExec: '源码模式: 按钮被点击时执行的函数'
      });
      */
      const textarea = document.getElementById('sceditor');
      window.sceditor.command.set('upimg', {
        tooltip: '图片',
        exec: () => this.triggerUpload('upload-article-contents-img-btn'),
        txtExec: () => this.triggerUpload('upload-article-contents-img-btn')
      });
      window.sceditor.command.set('ruby', {
        tooltip: '上标',
        exec: this.showRubyDialog,
        txtExec: () => this.$message.info(this.$t('tips.please_op_in_edit_mode'))
      });

      window.sceditor.command.set('emoji', {
        tooltip: '表情',
        exec: () => (this.showEmojiDrawer = true),
        txtExec: () => (this.showEmojiDrawer = true)
      });

      const showVideo = () => {
        // if (this.isEditMode) {
        //   return this.$message.error(this.$t('tips.can_not_update_vote'));
        // }
        this.showVideoDialog = true;
      };
      window.sceditor.command.set('youtube', {
        tooltip: '插入视频或者音频',
        exec: showVideo,
        txtExec: showVideo
      });
      const showVotes = () => {
        if (this.isEditMode) {
          return this.$message.error(this.$t('tips.can_not_update_vote'));
        }
        this.showVoteDialog = true;
      };
      window.sceditor.command.set('votes', {
        tooltip: '投票',
        exec: showVotes,
        txtExec: showVotes
      });

      window.sceditor.command.set('attch', {
        tooltip: '附件',
        exec: () => this.triggerUpload('upload-article-contents-ext-file-btn'),
        txtExec: () => this.triggerUpload('upload-article-contents-ext-file-btn')
      });

      window.sceditor.create(textarea, {
        format: 'bbcode',
        locale: 'cn',
        icons: 'custom',
        spellcheck: false,
        allowedContent: true,
        autofocus: true,
        autoUpdate: true,
        height: '800px',
        resizeEnabled: false,
        style: '/sceditor/minified/themes/default.min.css',
        toolbar:
          'cut,copy,pastetext|' +
          'bold,italic,underline,strike|' +
          'left,center,right|' +
          'font,size,color,removeformat|' +
          'link,unlink|' +
          // 'bulletlist,orderedlist,indent,outdent|table|code,quote|horizontalrule,image,email,link,unlink|' +
          'ruby,emoji,youtube,votes,attch,upimg|' +
          'date,time|source,maximize',
        parserOptions: {
          removeEmptyTags: false
        }
      });
      this.sceditor = window.sceditor.instance(textarea);
      this.isEditMode && this.initEditModeContents();
    },

    // 移除附件
    removeAttachment(id) {
      this.contentAttachment = this.contentAttachment.filter((item) => item.id !== id);
    },

    // 文章附件上传之前
    attachmentBeforeUpload(file) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        // 是否超过允许的最大大小 10M
        if (file.size > 10485760) {
          this.$message.error(this.$t('write.file_too_max'));
          return reject(file);
        }

        // 限制上传附件的类型
        const allowFileTypes = [
          '001',
          '002',
          '003',
          '004',
          '005',
          '006',
          '007',
          '008',
          '009',
          '010',
          '011',
          '012',
          '013',
          '014',
          '015',
          '016',
          '017',
          '7z',
          'amr',
          'arc',
          'ass',
          'asx',
          'chm',
          'dll',
          'doc',
          'docx',
          'epub',
          'htm',
          'ico',
          'kip',
          'lrc',
          'mds',
          'mht',
          'mid',
          'mp3',
          'pak',
          'pdf',
          'psd',
          'rar',
          'rtf',
          'scr',
          'sdt',
          'sis',
          'skn',
          'srt',
          'ssf',
          'stk',
          'swf',
          'thm',
          'tif',
          'torrent',
          'txt',
          'umd',
          'wav',
          'wma',
          'wsz',
          'xls',
          'zip'
        ];

        // 判断上传文件类型是否支持
        const lastDotIndex = file.name.lastIndexOf('.');
        const fileExt = lastDotIndex > -1 ? file.name.substr(lastDotIndex + 1) : false;
        if (!fileExt || !allowFileTypes.includes(fileExt)) {
          this.$message.error(this.$t('write.upload_file_type_not_support'));
          return reject(file);
        }

        // 获取文件md5 && 判断文件是否存在
        const md5 = await this.getFileMD5(file);
        const res = await this.$axios.$post('/upload/get-article-file', { md5 });
        if (res.code === 0 && res.data) {
          this.attachmentUploadSuccess(res, { raw: file });
          return reject(file);
        }

        // 附件没有被上传过: 上传附件
        this.uploadFormData.md5 = md5;
        return resolve(file);
      });
    },

    // 文章附件上传完毕
    attachmentUploadSuccess(res, file) {
      if (res.code === 0 && res.data) {
        this.contentAttachment.push({
          id: res.data.res_id,
          name: file.raw.name
        });
      }
    },

    // 放大+缩小裁剪图片的操作
    scaleImage(num) {
      this.$refs.cropper.changeScale(num || 1);
    },

    // 确定上标
    rubyConfirm() {
      const sc = this.selectionContents;
      const content = `${sc[0]}[ruby=${this.rubyContent}]${sc[1]}[/ruby]${sc[2]}`;
      this.range.startContainer.textContent = content;
      this.selectionContents = [];
      this.showRubyLayer = false;
    },

    // 创建合集
    async createCollection() {
      if (this.checkCollectionData()) {
        const res = await this.$axios.$post('/api/series/create-series', this.collection);
        if (res.code === 0) {
          this.showCollectionDialog = false;
          await this.$store.dispatch('write/getArticleConfigs');
          this.series = this.articleConfigs.series.filter((item) => item.parent_gid === this.article.gid);
          this.$message.success(this.$t('tips.create_success'));
        } else {
          this.$message.error(this.$t('tips.create_fail'));
        }
      }
    },

    // 检查合集数据
    checkCollectionData() {
      const { collection } = this;
      // 修改创建合集时应该传大分区的gid

      if (!collection.gid) {
        this.$message.error(this.$t('write.select_group'));
        return false;
      }
      if (!collection.name.trim()) {
        this.$message.error(this.$t('write.input_collection_name'));
        return false;
      }
      return true;
    },

    // 处理内容
    contentConverter(content, isSubmit = true) {
      let str = String(content);
      const imgs = this.uploadedImages;
      if (isSubmit) {
        // 将百分比转数字: 发布/更新内容需要传这样的值
        // str = str.replace(/\[size=(\d{2,3})\](.+)\[\/size\]/gim, (...args) => { return `[size=${this.fontsizeMap[args[1]]}]${args[2]}[/size]`; });

        // 转换对齐显示的标签[left][right][center] => [align=center]
        str = str.replace(/\[(left|center|right)\]/gim, (...args) => `[align=${args[1]}]`);
        str = str.replace(/\[\/(left|center|right)\]/gim, (...args) => `[/align]`);

        // 处理磁力链url标签
        /* eslint-disable */
        const urlReg = /\[url.*?(?:]|\/])/gi; // 匹配url标签
        const arr = str.match(urlReg); // 筛选出所有的url
        if (arr && arr.length > 0) {
          for (let index = 0; index < arr.length; index++) {
            // 必须是url含有magnet
            if (/\b(magnet)\b/.test(arr[index])) {
              arr[index] = arr[index].replace(arr[index].slice(5, arr[index].indexOf('magnet')), '');
              str = str.replace(str.match(urlReg)[index], arr[index]);
            }
          }
        }

        // 将图片转res: 发布/更新内容需要传这样的值
        str = str.replace(/\[img\](.+)\[\/img\]/gim, (...args) => {
          for (let i = 0; i < imgs.length; i++) {
            if (args[1] === imgs[i].url) {
              this.tempContentImagesMap.push({
                index: args[2],
                id: imgs[i].id,
                url: imgs[i].url
              });
              return `[img]${args[2]}[/img]`;
            }
          }
          return args[0];
        });

        return str;
      }

      // 将数字转百分比: 编辑内容需要预览
      // str = str.replace(/\[size=(\d{1})\](.+)\[\/size\]/gim, (...args) => { return `[size=${this.mapFontsizes[args[1]]}]${args[2]}[/size]`; });

      // 转换对齐显示的标签[align=left] => [left][right][center]
      const alignReg = /(\[align\s{0,}=\s{0,}(left|center|right)\](.*?)\[\/align\])/gim;
      str = str.replace(alignReg, (...args) => `[${args[2]}]${args[3]}[/${args[2]}]`);

      // 将 res 转 img
      str = str.replace(/\[res\](.+)\[\/res\]/gim, (...args) => {
        const [fileType] = args[1].split(','); // 0: 图片 2:文件
        if (Number(fileType) !== 0) return '';
        if (this.tempPreviewResMap[args[1]]) {
          return `[img]${this.tempPreviewResMap[args[1]]}[/img]`;
        } else {
          return args[0];
        }
      });

      // 附件
      str = str.replace(/\[file\](.+)\[\/file\]/gim, '');
      return str;
    },

    // 发布文章
    async createArticle() {
      // const content = this.ckeditor.getData();
      if (this.creating) return;
      const content = this.sceditor.val();
      this.article.content = this.contentConverter(content);
      if (this.checkArticleData()) {
        this.creating = true;
        const res = await this.$axios.$post('/api/article/post-article', this.article);
        if (res.code === 0) {
          this.$message.success(this.$t('tips.publish_success'));
          // 发布文章，清除缓存
          if (this.timer) {
            clearInterval(this.timer);
          }
          storage.remove('article');
          this.toPage(`/publish_mgr/article/${this.loginUser.uid}`, 'push');
        } else {
          this.$message.success(this.$t('tips.publish_fail'));
        }
      }
    },

    // pay_info校验
    check_node(event) {
      const { node } = this.pay_info;
      if (node % 10 !== 0 || node < 0 || node > 90) {
        this.$message.warning(`${this.$t('write.input_100_integer')}`);
        this.pay_info.node = '';
      }
    },

    // 修改文章
    async updateArticle() {
      if (this.updating) return;
      const content = this.sceditor.val();
      this.article.content = this.contentConverter(content);
      if (this.checkArticleData(true)) {
        const params = { aid: this.editArticle.aid };

        // 内容
        params.content = this.article.content;

        // 附件
        const { files } = this.article;
        const ftype = Object.prototype.toString.call(files);
        if (files && ftype === '[object Object]' && Object.keys(files).length) {
          params.files = files;
        }
        // 处理公会大厅
        if (this.editArticle.parent_group_id !== 42 && this.article.gid === 42) {
          this.$message.warning(this.$t('write.only-guild'));
          return false;
        }
        if (this.editArticle.parent_group_id === 42 && this.article.gid !== 42) {
          this.$message.warning(this.$t('write.only-other'));
          return false;
        }
        // 分区
        params.parent_group_id = this.article.gid;
        params.group_id = this.article.group_id;
        // 封面
        if (this.article.banner_id) {
          params.cover_id = this.article.cover_id;
          params.banner_id = this.article.banner_id;
        }

        // 内容图片 && 文章锁定信息
        if (this.article.images) params.images = this.article.images;
        if (this.contentLock) params.need_pay = this.pay_info;

        // 遍历能够修改的其他属性, 判断是否修改过
        for (const k of ['series_id', 'title', 'only_passer']) {
          if (this.editArticle[k] !== this.article[k]) {
            params[k] = this.article[k];
          }
        }
        // 编辑模式下取消售价
        if (!this.contentLock && this.isEditMode) params.need_pay = {};
        this.updating = true;
        const res = await this.$axios.$post('/api/article/edit-article', params);
        if (res.code === 0) {
          this.$message.success(this.$t('tips.publish_success'));
          this.toPage(`/publish_mgr/article/${this.loginUser.uid}`, 'push', this.$route.query);
        } else {
          this.$message.error(this.$t('tips.publish_fail'));
        }
      }
    },

    // 检查文章数据
    checkArticleData(isUpdate = false) {
      const { article } = this;
      if (!article.title.trim()) {
        this.$message.error(this.$t('write.input_title'));
        return false;
      }
      if (!article.content.trim()) {
        this.$message.error(this.$t('write.input_content'));
        return false;
      }
      if (this.getByteSize(article.content) < 10) {
        this.$message.error(this.$t('write.content_too_less'));
        return false;
      }
      if (!article.group_id) {
        this.$message.error(this.$t('write.select_group'));
        return false;
      }
      if (this.contentLock) {
        // 如果需要付费就判断, 不需要就不判断
        if (!this.pay_info.node) {
          this.$message.error(this.$t('write.coin_node'));
          return false;
        }
        if (!this.pay_info.coin) {
          this.$message.error(this.$t('write.coin_price'));
          return false;
        }
        this.pay_info.node = Number(this.pay_info.node);
        this.pay_info.coin = Number(this.pay_info.coin);
        article.need_pay = this.pay_info;
      }

      // 仅会员可见 和 内容图片(tempContentImagesMap 在 contentConverter 方法中处理过)
      article.only_passer = Number(article.only_passer);
      if (this.tempContentImagesMap.length) {
        article.images = {};
        this.tempContentImagesMap.forEach((item) => {
          article.images[item.index] = item.id;
        });
      }

      // 如果有附件就处理附件
      if (this.contentAttachment.length) {
        article.files = {};
        this.contentAttachment.forEach((item, index) => {
          if (item.id) {
            article.files[index] = item.id;
            article.content += `[file]${index}[/file]`;
          }
        });
      }

      return true;
    },

    // 裁剪实时预览
    realTimePreview(preview) {
      this.corpPreviews = preview;
    },

    // 切换裁剪图片的 dialog 隐藏和显示
    toggleCropDialog(show, file) {
      this.cropOptions.img = file || '';
      this.showCrop = show;
    },

    // 裁剪图片: 获得裁剪后的图片 blob 对象
    cropImage(confirm) {
      if (confirm) {
        const minWidth = this.verticalMode ? this.cropperSize1.w : this.cropperSize2.w;
        if (this.corpPreviews.w < minWidth) {
          this.$message.error(this.$t('write.min_width') + minWidth);
          return;
        }
      }

      this.$refs.cropper.getCropBlob((blob) => {
        this.toggleCropDialog(false);
        this.cropedFile = new window.File([blob], this.uploadFilename, {
          type: blob.type
        });
        this.cropedUploadConfirm = confirm;
      });
    },

    // 上传之前处理 banner: 压缩裁剪获取md5
    beforeBannerUpload(file) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        this.uploadFilename = file.name;
        this.cropOptions.outputType = file.type.split('/').pop();

        // 是否超过允许的最大大小 10M
        if (file.size > 10485760) {
          this.$message.error(this.$t('write.file_too_max'));
          return reject(file);
        }

        // 将文件对象转为base64 对象 然后将开启图片裁剪 dialog
        const fileBase64 = await this.file2base64(file);
        this.toggleCropDialog(true, fileBase64);

        // 如果 showCrop 为 false 裁剪结束了
        this.$watch('showCrop', async (val) => {
          if (!val) {
            // 如果不上传就取消
            if (!this.cropedUploadConfirm) {
              return reject(file);
            }

            // 显示 loading
            if (this.isUploadCollectionCover) {
              this.collectionCoverState = 1;
            } else {
              this.articleCoverState = 1;
            }

            // 压缩图片(如果大于1MB就压缩到1M以下)
            const minFile = await this.imageCompression(this.cropedFile);

            // 获取文件md5 判断文件是否存在, 存在就不上传
            const md5 = await this.getFileMD5(minFile);
            const res = await this.$axios.$post('/upload/get-article-image', {
              md5
            });
            if (res.code === 0 && res.data) {
              this.coverUploadSuccess(res, { raw: minFile });
              return reject(minFile);
            }

            // 如果图片不存在就组合数据上传文件
            this.uploadFormData.md5 = md5;
            return resolve(minFile);
          }
        });
      });
    },

    // 文章正文内容上传成功后
    contentImgUploadSuccess(res, file) {
      if (res.code === 0) {
        const { res_id: id, res_url: url } = res.data;
        const img = this.contentImages.find((item) => item.id === id);
        if (!img) {
          this.contentImages.push({ id, url, name: file.raw.name });
          this.uploadedImages = this.contentImages;
        }
      }
    },

    // 上传之前处理图片: 压缩获取文件md5
    beforeUpload(file) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        // 是否超过允许的最大大小 10M
        if (file.size > 10485760) {
          this.$message.error(this.$t('write.file_too_max'));
          return reject(file);
        }

        // 压缩图片(如果大于1MB就压缩到1M以下)
        const minFile = await this.imageCompression(file);
        // 获取文件md5 判断文件是否存在, 存在就不上传
        const md5 = await this.getFileMD5(minFile);
        const res = await this.$axios.$post('/upload/get-article-image', { md5 });
        if (res.code === 0 && res.data) {
          this.contentImgUploadSuccess(res, { raw: file });
          return reject(minFile);
        }

        // 如果图片不存在就组合数据上传文件
        this.uploadFormData.md5 = md5;
        return resolve(minFile);
      });
    },

    // 将图片插入到内容当中
    appendImgToContents(url) {
      if (this.sceditor.inSourceMode()) {
        this.sceditor.insertText(`\n[img]${url}[/img]\n`);
      } else {
        this.sceditor.wysiwygEditorInsertHtml(`<br/><img src="${url}"/><br/>`);
      }
    },

    // 移除文件列表的文件
    removeContentImg(id) {
      this.contentImages = this.contentImages.filter((item) => item.id !== id);
    },

    // 文章封面上传失败
    coverUploadError(isArticle) {
      this.$message.error(this.$t('tips.cover_upload_fail'));
      if (this.isUploadCollectionCover) {
        this.collectionCoverState = 0;
      } else {
        this.articleCoverState = 0;
      }
    },

    // 选择封面图标(文章+合集)
    selectCover(isCollection) {
      this.isUploadCollectionCover = isCollection;
      if (isCollection) {
        // 合集 1:上传中
        if (this.collectionCoverState === 1) {
          this.$message.error(this.$t('tips.file_is_uploading'));
        } else {
          this.triggerUpload('upload-collection-img-btn');
        }
        return;
      }

      // 文章 1:上传中
      if (this.articleCoverState === 1) {
        this.$message.error(this.$t('tips.file_is_uploading'));
      } else {
        this.triggerUpload('upload-article-banner-img-btn');
      }
    },

    // 文章封面上传成功后
    coverUploadSuccess(res, { raw: file }) {
      if (res.code === 0) {
        // const base64 = await this.file2base64(file);
        const resId = res.data.res_id;
        if (this.isUploadCollectionCover) {
          // 合集封面图片
          this.collection.banner_id = this.collection.cover_id = resId;
          this.collectionCover = res.data.res_url;
          this.collectionCoverState = 2;
        } else {
          // 文章封面图片
          this.article.cover_id = this.article.banner_id = resId;
          this.articleCover = res.data.res_url;
          this.articleCoverState = 2;
        }
      }
    },

    // 输入表情
    inputEmoji(item) {
      // this.ckeditor.insertText(`${item.code}`);
      this.sceditor.insertText(`${item.code}`);
    },

    // 发文章: 选择大分区(轻小说漫画竖排封面, 其他分区横排封面)
    selectSuperGroup(e) {
      let selectedGroup = null;
      const groups = this.articleConfigs.groups;
      if (typeof e === 'boolean' && e) {
        selectedGroup = Object.values(groups)[0];
      } else {
        // 测试出现的问题解决方法
        if (e.target.value === 0 || e.target.value === '0') {
          e.target.value = 1;
        }
        selectedGroup = groups.find((el) => el.gid === Number(e.target.value));
      }
      this.verticalMode = !!selectedGroup.cover_type;
      this.subGroups = selectedGroup.items;
    },

    // 新建合集: 选择大分区
    selectCollectionSuperGroup(e) {
      let selectedGroup = null;
      const groups = this.articleConfigs.groups;
      if (typeof e === 'boolean' && e) {
        selectedGroup = Object.values(groups)[0];
      } else {
        selectedGroup = groups[e.target.value];
      }
      this.verticalModeCollection = !!selectedGroup.cover_type;
      this.subCollectionGroups = selectedGroup.items;
    },

    // 选择子分区
    selectSubGroups(e) {
      this.article.group_id = e.target.value;
    },

    // 取消投票
    closeVoteLayer() {
      this.hasVotes = false;
      this.showVoteDialog = false;
    },

    // 关闭视频
    closeVideoLayer() {
      this.showVideoDialog = false;
    },

    // 关闭视频按钮
    closeVideoBtn() {
      if (this.videoSize === 0 || this.videoSize === '0') {
        this.videoUrl = this.insertStr(this.videoUrl, 8, " width='100%' height= '495px' ");
      } else if (this.videoSize === 1 || this.videoSize === '1') {
        this.videoUrl = this.insertStr(this.videoUrl, 8, " width='65%' height= '322px' ");
      } else if (this.videoSize === 2 || this.videoSize === '2') {
        this.videoUrl = this.insertStr(this.videoUrl, 8, " width='45%' height= '223px' ");
      }
      this.sceditor.wysiwygEditorInsertText(`${this.videoUrl}`);
      this.videoUrl = '';
      this.showVideoDialog = false;
    },
    initWebTorrent() {
      // console.log(window, 'window');
      this.client = new window.WebTorrent();
      // console.log(this.client);
    },
    // 上传种子文件
    intoTorentFile() {
      this.helpItem = 0;
      const file = this.$refs.inputer.files;
      const that = this;
      if (file[0] && file[0].name && file[0].name.endsWith('.torrent')) {
        try {
          that.client.add(file[0], (torrent) => {
            torrent.files.forEach((file) => {
              if (
                file.name.endsWith('.mp4') ||
                file.name.endsWith('.mp3') ||
                file.name.endsWith('.mpeg') ||
                file.name.endsWith('.wma') ||
                file.name.endsWith('.aac') ||
                file.name.endsWith('.realaudio') ||
                file.name.endsWith('.flac')
              ) {
                that.magnetURI = 'magnet:?xt=urn:btih:' + torrent.infoHash.toUpperCase();
                // console.log(torrent.magnetURI); // 需要保存的磁力链
                file.appendTo('.log', (err) => {
                  if (err) {
                    that.$message.warning('无效操作');
                  }
                });
              }
            });
          });
        } catch (error) {
          if (error) that.$message.warning('无效操作');
        }
      } else {
        that.$message.warning('请上传种子文件');
      }
      // if (file[0] && file[0].name && file[0].name.endsWith('.mp4') && !file[0].name.endsWith('.torrent')) {
      //   const that = this;
      //   that.client.seed(file[0], function(torrent) {
      //     that.torrentUrl = torrent.magnetURI;
      //     this.$message.success('播种成功，切换到磁力链选项，即可看到支持webRtc的磁力链');
      //   });
      // }
      // console.log(file[0]);
      // this.loadingComments = true;
    },
    // 类型为0，磁力链按钮
    showTorrentBtn() {
      const that = this;
      if (that.torrentUrl) {
        try {
          that.client.add(that.torrentUrl, (torrent) => {
            torrent.files.forEach((file) => {
              if (
                file.name.endsWith('.mp4') ||
                file.name.endsWith('.mp3') ||
                file.name.endsWith('.mpeg') ||
                file.name.endsWith('.wma') ||
                file.name.endsWith('.aac') ||
                file.name.endsWith('.realaudio') ||
                file.name.endsWith('.flac')
              ) {
                that.magnetURI = 'magnet:?xt=urn:btih:' + torrent.infoHash.toUpperCase() + '&dn=' + torrent.name;
                // console.log(torrent.magnetURI, '233?'); // 需要保存的磁力链
                file.appendTo('.log', (err) => {
                  if (err) {
                    that.$message.warning('无效操作');
                  }
                });
              }
            });
          });
        } catch (error) {
          if (error) that.$message.warning('无效操作');
        }
      } else {
        this.$message.warning('请输入磁力链');
      }
      // this.torrentUrl = '';
    },
    // 处理尺寸
    changSize(val) {
      if (val === 0) {
        return 'l';
      } else if (val === '1') {
        return 'm';
      } else {
        return 's';
      }
    },
    // 关闭弹窗，确认按钮
    closeBtn() {
      // 必须确保该磁力链或者种子文件有效才能添加到编辑器
      if (this.magnetURI) {
        // console.log(this.videoSize);
        this.sceditor.wysiwygEditorInsertHtml(`[wt size=${this.changSize(this.videoSize)}]${this.magnetURI}[/wt]`);
        this.showVideoDialog = false;
      } else {
        this.$message.warning('请点击视频预览，以确保有效！');
      }
    },
    // 加入宽高
    insertStr(str1, n, str2) {
      let s1 = '';
      let s2 = '';
      if (str1.length < n) {
        return str1 + str2;
      } else {
        s1 = str1.substring(0, n);
        s2 = str1.substring(n, str1.length);
        return s1 + str2 + s2;
      }
    },
    // 投票: 确定
    confirmVote() {
      this.hasVotes = true;
      const { vote } = this;
      if (!vote.title.trim()) {
        this.$message.error(this.$t('write.input_vote_title'));
        return;
      }

      vote.options = vote.options.filter((item) => item.trim());

      if (!this.isMultipleChoose) {
        vote.max_choices = 1;
      } else {
        if (!vote.max_choices) {
          this.$message.error(this.$t('write.inpu_max_select'));
          return;
        }
        if (vote.max_choices < 2 || vote.max_choices > vote.options.length) {
          this.$message.error(this.$t('write.vote_multiple_select'));
          return;
        }
      }

      if (!this.vote.expiration) {
        this.$message.error(this.$t('write.input_vote_time'));
      }
      this.article.poll = vote;
      this.showVoteDialog = false;
    },

    // 增加投票选项
    addVoteItem() {
      for (const item of this.vote.options) {
        if (!item.trim()) {
          this.$message.error(this.$t('write.input_then_add'));
          return;
        }
      }
      if (this.vote.options.length < 20) {
        this.vote.options.push('');
      } else {
        this.$message.info(this.$t('write.vote_max_20'));
      }
    },

    // 删除投票选项
    delVoteItem(index) {
      if (this.vote.options.length > 2) {
        this.vote.options = this.vote.options.filter((i, k) => k !== index);
      } else {
        this.$message.info(this.$t('write.vote_min_2'));
      }
    },

    // 初始化编辑器
    // initCkEditor() {
    //   if (!window.CKEDITOR) return;
    //   this.ckeditor = window.CKEDITOR.replace('__ck_editor__', {
    //     height: 500,
    //     language: 'zh-cn',
    //     extraPlugins: 'bbcode,colorbutton,justify,myupload,myvotes,myemoji,myruby,myfujian',
    //     removePlugins:
    //       'elementspath,about,filebrowser,format,horizontalrule,pastetext,pastefromword,scayt,showborders,stylescombo,table,tabletools,tableselection,wsc',
    //     removeButtons: 'Copy,Cut,Paste,SpecialChar,Image,Anchor,BGColor,Font,Strike,Subscript,Superscript,JustifyBlock',
    //     disableObjectResizing: true,
    //     fontSize_sizes: '特小/25%;小/50%;正常/100%;中等/150%;大/200%;特大/250%'
    //   });
    //   // 内容图片
    //   window.addEventListener('ckeditormyupload', ({ detail }) => {
    //     // console.log('ckeditormyupload  ');
    //     this.triggerUpload('upload-article-contents-img-btn');
    //     // console.log(this.ckeditor.document);
    //   });

    //   // 投票
    //   window.addEventListener('ckeditormyvotesclick', () => {
    //     if (this.isEditMode) {
    //       return this.$message.error(this.$t('tips.can_not_update_vote'));
    //     }
    //     this.showVoteDialog = true;
    //   });

    //   // 表情
    //   window.addEventListener('ckeditormyemojiclick', () => {
    //     this.showEmojiDrawer = true;
    //   });

    //   // 附件
    //   window.addEventListener('ckeditormyfujianclick', () => {
    //     this.triggerUpload('upload-article-contents-ext-file-btn');
    //   });

    //   // 上标
    //   window.addEventListener('ckeditormyrubyclick', () => {
    //     const selection = this.ckeditor.document.getSelection().getNative();
    //     this.range = selection.getRangeAt(0);
    //     if (this.range.startContainer !== this.range.endContainer) {
    //       this.$message.error(this.$t('write.selected_cannot_ruby'));
    //       return;
    //     }
    //     const { startOffset: start, endOffset: end } = this.range;
    //     if (start === end) {
    //       this.$message.error(this.$t('write.select_up_content'));
    //       return;
    //     }
    //     const content = this.range.startContainer.data;
    //     this.selectionContents.push(content.substring(0, start));
    //     this.selectionContents.push(content.substring(start, end));
    //     this.selectionContents.push(content.substring(end, content.length));
    //     this.showRubyLayer = true;
    //   });
    //   this.isEditMode && this.initEditModeContents();
    // },

    // 初始化编辑模式下内容(初始化编辑器之后执行)
    initEditModeContents() {
      // 附件和文章内图片
      if (this.editArticle.files) {
        this.contentAttachment = this.editArticle.files;
      }
      if (this.editArticle.res) {
        let itemInfo;
        this.editArticle.res.ids.forEach((item) => {
          itemInfo = this.editArticle.res.res_info[item];
          if (itemInfo) {
            const [, resId] = item.split(',');
            const { url, filename } = itemInfo;
            this.tempPreviewResMap[item] = url;
            this.contentImages.push({ id: resId, url, name: filename });
          }
        });
        this.uploadedImages = this.contentImages;
      }

      this.article.aid = this.editArticle.aid;
      this.article.gid = this.editArticle.parent_group_id;
      this.article.group_id = this.editArticle.group_id;
      // 通过parent_gid来确认
      this.series = this.articleConfigs.series.filter((item) => item.parent_gid === this.editArticle.parent_group_id);
      this.article.series_id = this.editArticle.series_id;
      this.article.title = this.editArticle.title;
      const content = this.contentConverter(this.editArticle.content, false);
      this.article.content = content;
      // this.ckeditor.setData(content);
      this.sceditor.val(content);
      this.articleCover = this.editArticle.banner;
      this.article.only_passer = this.editArticle.only_passer;
      if (this.editArticle.pay_info) {
        this.contentLock = true;
        this.pay_info.coin = this.editArticle.pay_info.price;
        this.pay_info.node = this.editArticle.pay_info.free_node;
      }
    },

    // 手动触发上传图片事件(节流: 防止重复触发)
    triggerUpload(id) {
      const now = Date.now();
      const delay = 1000; // 1s
      if (now - this.lastTriggerTime > delay) {
        window.document.getElementById(id).click();
        this.lastTriggerTime = now;
      }
    }
  },

  head() {
    return {
      title: this.$t('write.head_title') + '-' + this.$t('title')
      // 按照需求来说: 这些编辑器需要的css和js都应该是在本页面才加载的,
      // 但是, 如果只在本页面才加载就会, 出现 js 还没有加载完成就已经
      // 执行初始化编辑器的操作了
      // link: [{ rel: 'stylesheet', href: '/sceditor/minified/themes/square.min.css' }],
      // script: [
      //   // { src: '/ckeditor4.14/ckeditor.js' },
      //   { src: '/sceditor/js/sceditor.js', async: true },
      //   { src: '/sceditor/minified/formats/bbcode.js', async: true },
      //   { src: '/sceditor/js/myicons.js', async: true },
      //   { src: '/sceditor/languages/cn.js', async: true }
      // ]
    };
  }
};
</script>
<style lang="scss" scope>
@import '@/assets/scss/write.scss';
</style>

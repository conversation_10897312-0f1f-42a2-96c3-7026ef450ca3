<template>
  <div class="user-item">
    <a :href="$i18n.path(`profile/${item.uid}`)" target="_blank" class="cover">
      <UserAvatar :avatar-img="item.avatar" />
    </a>
    <div class="info">
      <div class="user-box">
        <!-- eslint-disable vue/no-v-html -->
        <a :href="$i18n.path(`profile/${item.uid}`)" target="_blank" class="title" :title="item.nickname" v-html="highLight(item.nickname)"> </a>
        <div class="user-info">{{ item.comments }}{{ $t('components.user_item.articles') }}</div>
        <div class="user-info">{{ item.followers }}{{ $t('components.user_item.fans') }}</div>
        <button class="btn-attention" @click="follow(item.uid)">{{ $t('components.user_item.followings') }}</button>
      </div>
      <div class="user-message">
        {{ item.sign }}
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import UserAvatar from '@/components/UserAvatar.vue';

export default {
  components: {
    UserAvatar
  },
  props: {
    kw: {
      type: String,
      required: true
    },
    item: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapGetters('login', ['loginUser'])
  },
  methods: {
    highLight(title) {
      const regexpStr = new RegExp(this.kw, 'ig');
      let matchStr = title.match(regexpStr);
      if (matchStr && matchStr.length > 0) {
        matchStr = title.match(regexpStr)[0];
      }
      title = title.replace(regexpStr, `<span style="color: #39d7d9; ">${matchStr}</span>`);
      return title;
    },

    // 关注
    follow(uid) {
      this.loginNext(() => {
        if (uid === this.loginUser.uid) {
          this.$message.error(this.$t('tips.cannot_follow_self'));
          return;
        }
        this.$store
          .dispatch('user/followUser', { uid, act: 0 })
          .then(() => this.$message.success(this.$t('tips.follow_success')))
          .catch(() => this.$message.error(this.$t('tips.follow_fail')));
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.user-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 30px 0;
  border-bottom: 1px solid #eee;
  .cover {
    display: block;
    min-width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
  }
  .info {
    margin-left: 35px;
    overflow: hidden;
    .user-box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .title {
        display: block;
        color: $dark;
        font-size: 18px;
        height: 20px;
        display: flex;
        max-width: 145px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 15px;
        text-decoration: none;
        .high-light {
          display: block;
          color: $primary;
        }
      }
      .user-info {
        font-size: 13px;
        color: $gray;
        margin-right: 20px;
      }
      .btn-attention {
        cursor: pointer;
        width: 60px;
        height: 20px;
        line-height: 20px;
        background-color: $primary;
        border-radius: 10px;
        font-size: 15px;
        color: white;
        outline: none;
        border: none;
      }
    }
    .user-message {
      margin-top: 20px;
      color: $gray;
      font-size: 13px;
    }
  }
}
</style>

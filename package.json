{"name": "warrior-nuxt", "version": "1.0.0", "description": "My doozie Nuxt.js project", "author": "Moon Mk", "private": true, "scripts": {"dev": "nuxt", "mock": "nodemon ./mocks/server.js", "proxy": "nodemon ./mocks/proxy.js", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint": "eslint --ext .js,.vue --ignore-path .gitignore ."}, "dependencies": {"@nuxtjs/dotenv": "^1.4.0", "@nuxtjs/style-resources": "^1.0.0", "body-parser": "^1.17.2", "browser-image-compression": "^1.0.9", "browser-md5-file": "^1.1.1", "callapp-lib": "^3.1.3", "cookieparser": "^0.1.0", "cors": "^2.8.5", "crypto-js": "^4.0.0", "element-ui": "^2.13.2", "express": "^4.15.3", "http-proxy-middleware": "^1.0.3", "js-cookie": "^2.2.1", "js-rsa-lh5": "^1.0.0", "jsencrypt": "^3.0.0-rc.1", "lodash": "^4.17.19", "moment": "^2.24.0", "nuxt": "^2.0.0", "vue-cropper": "^0.5.2", "vue-v-clickoutside": "^1.0.2", "vuedraggable": "^2.23.2", "vuex-persistedstate": "^3.0.1", "wysibb": "^1.0.3"}, "devDependencies": {"@nuxtjs/axios": "^5.9.6", "@nuxtjs/dotenv": "^1.4.1", "@nuxtjs/eslint-config": "^2.0.0", "@nuxtjs/eslint-module": "^1.0.0", "@nuxtjs/proxy": "^1.3.3", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "eslint": "^6.1.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-nuxt": ">=0.4.2", "eslint-plugin-prettier": "^3.1.2", "express": "^4.17.1", "mockjs": "^1.1.0", "node-sass": "^4.14.1", "prettier": "^1.19.1", "sass-loader": "^8.0.2", "swiper": "^5.3.6", "vue-awesome-swiper": "^4.0.4", "vue-i18n": "^8.16.0"}}
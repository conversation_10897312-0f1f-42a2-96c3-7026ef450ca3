<template>
  <div class="loading-container flex flex jc-center ai-center">
    <div class="simple__loading" :style="getStyleSize"></div>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  props: {
    width: {
      type: Number,
      default: 5
    },
    size: {
      type: Number,
      default: 30
    }
  },
  computed: {
    getStyleSize() {
      const size = this.size < 30 ? 30 : this.size;
      const width = this.width < 5 ? 5 : this.width;
      return {
        width: `${size}px`,
        height: `${size}px`,
        'border-width': `${width}px`
      };
    }
  }
};
</script>

<style lang="scss" scoped>
@keyframes loading {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
.loading-container {
  width: 100%;
  height: 100%;
  .simple__loading {
    border-style: solid;
    border-top-color: $primary;
    border-right-color: $primary;
    border-bottom-color: #fff;
    border-left-color: #fff;
    border-radius: 50%;
    overflow: hidden;
    animation: loading 0.5s linear infinite;
  }
}
</style>

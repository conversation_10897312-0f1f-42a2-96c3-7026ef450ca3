<template>
  <div class="profile-page flex jc-between">
    <!-- 侧边栏 -->
    <ProfileSide
      :is-me="true"
      :uid="loginUser.uid"
      :level="loginUser.level.level"
      :level-name="loginUser.level.name"
      :avatar="loginUser.avatar"
      :fans="loginUser.followers"
      :follows="loginUser.following"
      :articles="loginUser.articles"
      :username="loginUser.nickname"
      @btn-click="toPage(`/settings/${loginUser.uid}`)"
    />

    <!-- ==================== 右边: 动态信息 ==================== -->
    <div class="right-contents-container">
      <!-- 顶部标题 -->
      <p class="top-title fs-md bg-white-item">
        {{ $t('profile_moments.my_followings') }}
      </p>

      <!-- 动态列表 -->
      <ul class="moments-wrapper">
        <li v-for="(item, key) in moments" :key="key" class="bg-white-item moment-item flex">
          <!-- 左边: 头像 -->
          <div class="avatar-wrapper">
            <nuxt-link class="nuxt-link" target="_blank" :to="$i18n.path(`profile/${item.author.uid}`)">
              <UserAvatar :avatar-img="item.author.avatar" />
            </nuxt-link>
          </div>

          <!-- 右边: 动态内容 -->
          <div class="right-comment-content flex flex-cols">
            <!--  用户名和日期和更多 -->
            <div class="name-wrapper flex jc-between">
              <div class="name">
                <p class="author-user-container fs-sm">
                  <nuxt-link target="_blank" class="nuxt-link mar-right-10" :to="$i18n.path(`profile/${item.author.uid}`)">
                    {{ item.author.nickname }}
                  </nuxt-link>
                  <span class="lv-icon mar-right-10" :class="[`lv-${item.author.level.level}`]">{{ item.author.level.name }}</span>
                  <el-image
                    v-if="item.author.medals.length > 0"
                    :src="item.author.medals[0].img"
                    :title="item.author.medals[0].name"
                    fit="contain"
                    class="user-medal-image mar-right-10"
                  ></el-image>
                </p>
                <p class="fs-xs text-gray mar-top-10">
                  {{ item.last_time | date2short }}
                </p>
              </div>
              <!-- 更多菜单按钮 -->
              <div v-clickoutside="hideMoreMenu" class="btn more-menu-btn" @click.self.stop="showMoreMenu(item)">
                <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 182.72 725.67">
                  <circle cx="91.36" cy="91.36" r="91.36" />
                  <circle cx="91.36" cy="362.83" r="91.36" />
                  <circle cx="91.36" cy="634.31" r="91.36" />
                </svg>
                <!-- 更多: 菜单 -->
                <ul class="more-menus hide tail-arrow-top" :class="{ show: item === showMoreMenuIdx }">
                  <li class="menu-item fs-sm text-gray text-center" @click="cancelFollow(item.uid)">
                    {{ $t('profile_moments.cancel_follow') }}
                  </li>
                </ul>
              </div>
            </div>

            <!-- 底部动态内容, 发布的文章|合集信息 vertical|horizontal -->
            <div class="moments-info-wrapper">
              <CollectionItem
                :mode="item.cover_type === 1 ? 'vertical' : 'horizontal'"
                :banner="item.cover"
                :title="item.title"
                :views="item.hits"
                :comments="item.comments"
                :show-delete-btn="false"
                :no-border="true"
                :has-page-index="false"
                :gid="item.parent_gid"
                @cover-click="toPage(`/detail/${item.aid}`)"
                @cover-click2="toPage(`/themereply/${item.aid}`)"
              />
            </div>
          </div>
        </li>
      </ul>
      <Loading v-show="loading" class="loading" />
      <p v-if="noMore">{{ $t('moments.no_more') }}</p>
    </div>

    <!-- 放回顶部 -->
    <button v-backtop class="btn btn-backtop">
      <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 740.47 440.2">
        <path
          d="M400,281.4,97.8,607.5a38.31,38.31,0,0,1-56.8,0c-14.9-16.1-14.9-42.2,0-58.3L371.6,192.5a38.31,38.31,0,0,1,56.8,0L759,549.2a43.25,43.25,0,0,1,0,58.3,38.42,38.42,0,0,1-56.9,0Z"
          transform="translate(-29.83 -179.9)"
        />
      </svg>
    </button>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import UserAvatar from '@/components/UserAvatar.vue';
import ProfileSide from '@/components/ProfileSide.vue';
import Loading from '@/components/Loading.vue';
import CollectionItem from '@/components/CollectionItem.vue';

export default {
  layout: 'profile',
  middleware: ['auth'],
  components: {
    ProfileSide,
    UserAvatar,
    CollectionItem,
    Loading
  },

  async asyncData({ $axios }) {
    const res = await $axios.$post('/api/recom/get-follows', {
      page: 1,
      pageSize: 20
    });

    if (res.code === 0) {
      return {
        moments: res.data
      };
    }
  },

  data: () => ({
    loading: false, // 是找正在请求数据
    page: 1, // 当前页
    noMore: false, // 没有更多动态
    moments: [], // 动态
    showMoreMenuIdx: -1 // 需要显示菜单的索引
  }),

  computed: {
    ...mapGetters('login', ['loginUser']),
    disabled() {
      return this.loading || this.noMore;
    }
  },

  mounted() {
    window.onscroll = () => {
      const scrollHeight = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      const clientHeight = window.innerHeight || Math.min(document.documentElement.clientHeight, document.body.clientHeight);
      if (clientHeight + scrollTop >= scrollHeight) {
        !this.loading && this.loadMore();
      }
    };
  },

  methods: {
    // 取消关注
    cancelFollow(uid) {
      this.toggleFollowState(uid, 1, (flag) => {
        flag && this.refreshMomments(1);
      });
    },

    // 刷新数据
    async refreshMomments(page) {
      this.loading = true;
      const res = await this.$axios.$post('/api/recom/get-follows', {
        page
      });
      this.loading = false;
      if (res.code === 0) {
        this.page = page;
        this.moments = res.data;
      }
    },

    // 加载更多
    async loadMore() {
      this.loading = true;
      const res = await this.$axios.$post('/api/recom/get-follows', {
        page: this.page + 1
      });
      this.loading = false;
      if (res.code === 0) {
        this.page += 1;
        this.moments = this.moments.concat(res.data);
      }
    },

    // 点击显示对应的更多菜单
    showMoreMenu(index) {
      this.showMoreMenuIdx = index;
    },

    // 隐藏已经显示的 menus
    hideMoreMenu() {
      this.showMoreMenuIdx = -1;
    }
  },

  head() {
    const title = this.$t('moments.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
.profile-page {
  margin-top: 30px;
  .btn-backtop {
    position: fixed;
    bottom: 280px;
    right: 50px;
    width: 50px;
    height: 50px;
    padding: 15px;
    background: #fff;
    .svg-icon {
      fill: $gray;
      &:hover {
        cursor: pointer;
        fill: $primary;
      }
    }
  }
  .loading {
    height: 100px;
  }
  .right-contents-container {
    width: 910px;
    .top-title {
      padding: 18px 15px;
    }
    .bg-white-item {
      background: #fff;
      margin-bottom: 10px;
    }
    // 动态
    .moments-wrapper {
      .moment-item {
        padding: 30px 15px 20px 15px;

        // 右边动态内容
        .right-comment-content {
          width: 100%;
          padding-left: 15px;

          // 用户名和日期和更多
          .name-wrapper {
            margin-bottom: 15px;
            // 更多菜单
            .more-menu-btn {
              position: relative;
              width: 30px;
              height: 20px;
              .svg-icon {
                position: absolute;
                top: 0;
                right: 50%;
                transform: translate(50%);
                height: 15px;
                fill: $gray;
              }
              .more-menus {
                background-color: #fff;
                box-shadow: 0px 0px 15px 0px rgba(1, 1, 1, 0.1);
                border-radius: 5px;
                padding: 10px 15px;
                position: absolute;
                top: 35px;
                left: -70px;
                min-width: 100px;
                &.tail-arrow-top::after {
                  left: 80%;
                  padding: 6px;
                  border-radius: 2px;
                }
                .menu-item {
                  text-align: center;
                  margin-bottom: 10px;
                  &:last-of-type {
                    margin-bottom: 0;
                  }
                  &:hover {
                    color: $primary;
                  }
                }
              }
            }
            .author-user-container {
              display: flex;
              align-items: center;
              .user-medal-image {
                width: 30px;
                height: 30px;
              }
            }
          }
          .text-hide-1 {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            word-break: break-all;
          }
          // 文章信息
        }
        .right-btns {
          width: 50px;
        }
      }
    }
  }
}
</style>

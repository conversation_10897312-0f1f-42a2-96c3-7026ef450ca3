<template>
  <div class="conf-page">
    <!-- 头部 -->
    <div class="conf-page-title">
      <div class="item-box">{{ $t('guardhouse.head') }}</div>
      <div class="item-box">{{ $t('guardhouse.ID') }}</div>
      <div class="item-box">{{ $t('guardhouse.detail') }}</div>
    </div>
    <!-- 灰色线 -->
    <div class="line"></div>
    <div class="conf-content">
      <ul>
        <li v-for="(item, idx) in confData" :key="idx">
          <div class="imgbox"><img src="@/assets/images/headImg.jpg" alt="" /></div>
          <span>{{ item.ID }}</span>
          <span>{{ item.detail }}</span>
        </li>
      </ul>
    </div>
    <div class="conf-btn">
      <div class="btn-box">
        <button>{{ $t('paginate.prev') }}</button>
        <button>1</button>
        <button>2</button>
        <button>3</button>
        <button>{{ $t('paginate.next') }}</button>
        <div class="inp-box">{{ $t('guardhouse.to') }}<input type="text" />{{ $t('guardhouse.page') }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'profile',
  data: () => ({
    confData: [
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      },
      {
        headImg: '',
        ID: '654548',
        detail: '在评论中发布引战言论，账号将封禁到 2020-09-04 01:27:09'
      }
    ]
  })
};
</script>
<style lang="scss">
.conf-page {
  width: 1160px;
  background-color: white;
  margin: 0 auto;
  margin-top: 30px;
  .line {
    height: 10px;
    background-color: #f5f8f9;
  }
  .conf-page-title {
    display: flex;
    height: 60px;
    .item-box {
      font-size: 18px;
      font-weight: 400;
      color: #424242;
      line-height: 60px;
      margin-left: 88px;
      &:nth-child(2) {
        margin-left: 175px;
      }
      &:last-of-type {
        margin-left: 439px;
      }
    }
  }
  .conf-content {
    ul {
      li {
        height: 70px;
        display: flex;
        border-bottom: 2px solid #f5f8f9;
        border-bottom-width: 90%;
        width: 95%;
        margin: 0 auto;
        .imgbox {
          margin-left: 56px;
          width: 40px;
          line-height: 70px;
          padding-top: 15px;
          img {
            width: 100%;
          }
        }
        span {
          font-size: 15px;
          line-height: 70px;
          &:nth-child(2) {
            margin-left: 156px;
          }
          &:last-of-type {
            margin-left: 252px;
          }
        }
      }
    }
  }
  .conf-btn {
    height: 110px;
    .btn-box {
      display: flex;
      height: 110px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      line-height: 30px;
      align-items: center;
      button {
        cursor: pointer;
        outline: none;
        border: 1px solid #eeeeee;
        transition: all 0.1s;
        background-color: white;
        border-radius: 5px;
        &:first-of-type,
        &:nth-child(5) {
          width: 60px;
          height: 30px;
          margin-right: 5px;
        }
        &:first-of-type:hover {
          color: #eeeeee;
        }
        &:nth-child(5):hover {
          color: #eeeeee;
        }
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4) {
          margin-right: 5px;
          width: 30px;
          height: 30px;
        }
        &:nth-child(2):hover {
          background-color: #39d7d9;
          color: white;
        }
        &:nth-child(3):hover {
          background-color: #39d7d9;
          color: white;
        }
        &:nth-child(4) :hover {
          background-color: #39d7d9;
          color: white;
        }
      }
      .inp-box {
        input {
          width: 60px;
          height: 30px;
          border: 1px solid #eeeeee;
          border-radius: 5px;
          margin-left: 5px;
          margin-right: 5px;
          outline: none;
        }
        input:focus {
          outline: 0;
          border-color: #39d7d9;
        }
      }
    }
  }
}
</style>

<template>
  <div class="medal-wall-container">
    <el-dialog :visible.sync="dialogVisible" width="300px" center top="30vh">
      <div class="medal-dialog-content emphasize">
        <p>{{ $t('medal_center.dialog_stock_tips') }} {{ dialogInfo.stock }} {{ $t('medal_center.dialog_stock_tips_end') }}</p>
        <p>{{ dialogInfo.duration }}{{ $t('medal_center.dialog_duration') }}</p>
        <p>{{ $t('medal_center.qualification') }}：{{ dialogInfo.qualification }}</p>
      </div>
      <p class="medal-dialog-content">{{ $t('medal_center.confirm_check') }}</p>
      <span slot="footer" class="dialog-footer">
        <el-button class="lk-primary-button confirm-button" round @click="handleConfirm($event, dialogInfo.k, dialogInfo.goodsInfo)">{{
          $t('medal_center.confirm')
        }}</el-button>
      </span>
    </el-dialog>
    <div class="medal-wall-header">
      <h4 class="medal-wall-title">{{ $t('medal_center.medal_redeem') }}</h4>
      <el-pagination
        class="my-pagination"
        layout="prev, pager, next, jumper"
        :prev-text="$t('paginate.prev')"
        :next-text="$t('paginate.next')"
        :hide-on-single-page="true"
        :disabled="loading"
        :current-page="currentPage"
        :total="totalCount"
        :page-size="12"
        style="padding-bottom: 18px;"
        @current-change="handleChangeCurrentPage"
      />
    </div>

    <el-row v-if="medals.length > 0" :gutter="16" justify="space-between">
      <el-col v-for="(medal, k) in medals" :key="medal.id" :xs="8" :sm="8" :md="6" :lg="4" :xl="4" class="medal-card-container">
        <medal-card :image-url="medal.imageUrl" :name="medal.name">
          <template v-slot:mask-info>
            <p>{{ $t('medal_center.stock_tips') }}{{ medal.stock }}{{ $t('medal_center.dialog_stock_tips_end') }}</p>
            <p>{{ medal.duration }}{{ $t('medal_center.dialog_duration') }}</p>
            <p>{{ $t('medal_center.qualification') }}：{{ medal.qualification }}</p>
          </template>
          <template v-slot:description>
            <p class="medal-cost">
              <i class="el-icon-circle-plus" />
              {{ medal.price }}
            </p>
            <p class="medal-description">{{ medal.description }}</p>
          </template>
          <template v-slot:action>
            <el-button
              size="mini"
              round
              class="lk-primary-button redeem-button"
              :disabled="medal.waiting || !canBuy(medal.status, medal.expiration, medal.level_limit, medal.stock)"
              @click="handleRedeem($event, medal, k)"
              >{{ buttonString(medal.status, medal.expiration, medal.waiting) }}</el-button
            >
          </template>
        </medal-card>
      </el-col>
    </el-row>

    <Loading v-if="medals.length === 0" />
  </div>
</template>

<script>
import moment from 'moment';
import { mapGetters } from 'vuex';
import Loading from '@/components/Loading';
import MedalCard from '@/components/medal/MedalCard';

export default {
  name: 'MedalRedeem',
  components: {
    MedalCard,
    Loading
  },
  data() {
    return {
      medals: [],
      currentPage: 1,
      totalCount: 0,
      // 弹窗相关
      dialogVisible: false,
      dialogInfo: {
        id: '',
        stock: 0,
        duration: '',
        qualification: '',
        goodsInfo: []
      },
      loading: false
    };
  },
  computed: {
    ...mapGetters('login', ['loginUser'])
  },
  mounted() {
    this.getMedals(2, 1);
  },
  methods: {
    // 按页面获取勋章墙
    getMedals(type, page) {
      this.$axios.$post('/api/medal/list', { type, page, page_size: 12 }).then((res) => {
        this.medals = [];
        if (res.code === 0) {
          this.currentPage = res.data.page_info.cur;
          this.totalCount = res.data.page_info.count;
          res.data.list.forEach((item, key) => {
            this.medals.push({
              id: item.id,
              medal_id: item.medal_id,
              name: item.name,
              imageUrl: item.img, // 图片链接
              price_type: item.price_type,
              price: item.price, // 耗费
              status: item.status, // 状态
              recharge_num: item.recharge_num,
              description:
                item.recharge_num === -1
                  ? this.$t('medal_center.permanent')
                  : this.$t('medal_center.expiration_date') + item.recharge_num + this.$t('medal_center.day'),
              qualification: this.$t('medal_center.level') + ' >= ' + this.$t(`settings.lv_${item.level_limit}`), // 兑换条件
              duration: item.end_time === '0000-00-00 00:00:00' ? '9999+' : moment(item.end_time).diff(moment(), 'days'), // 距离下架时间
              stock: item.stock === -1 ? '9999+' : item.stock, // 库存量
              level_limit: item.level_limit, // 等级限制
              waiting: false, // 购买等待
              expiration: item.expiration ?? '0000-00-00 00:00:00' // 勋章过期时间
            });
          });
        }
      });
    },
    handleChangeCurrentPage(page) {
      this.medals = [];
      this.getMedals(2, page);
    },
    handleRedeem(event, goodsInfo, k) {
      const { id, stock, duration, qualification } = goodsInfo;
      this.dialogInfo = { id, stock, duration, qualification, k, goodsInfo };
      this.dialogVisible = true;
    },
    // 确认兑换勋章。提交id。
    handleConfirm(event, k, goodsInfo) {
      this.medals[k].waiting = true;
      this.dialogVisible = false;
      this.dialogInfo = {
        id: '',
        stock: 0,
        duration: '',
        qualification: '',
        price: 0,
        k: 0,
        goodsInfo: []
      };

      if (goodsInfo.status === 2 && goodsInfo.expiration === '0000-00-00 00:00:00') return false;

      this.loginNext(async () => {
        const totalPrice = goodsInfo.price;
        const data = { params: goodsInfo.id };
        data.goods_id = 5;
        data.price = goodsInfo.price;
        data.total_price = totalPrice;
        data.number = 1;

        if (this.loginUser.balance.coin < data.total_price) {
          this.$message.info(this.$t('tips.insufficient_coin'));
          return;
        }

        const res = await this.$axios.$post('/api/coin/use', data);
        this.medals[k].waiting = false;
        if (res.code === 0) {
          this.$store.commit('login/setCoins', { num: totalPrice });
          if (this.medals[k].stock !== -1 || this.medals[k].stock !== 0) this.medals[k].stock -= 1;

          this.medals[k].status = 2;
          this.medals[k].expiration =
            goodsInfo.recharge_num === -1
              ? '0000-00-00 00:00:00'
              : moment()
                  .add(goodsInfo.recharge_num, 'd')
                  .format('YYYY-MM-DD HH:mm:ss');
        }
      });

      // 如有必要，刷新勋章列表
      // this.getMedals(1);
    },
    // 是否能买
    canBuy(status, expiration, levelLimit, stock) {
      return !(status === 2 && expiration === '0000-00-00 00:00:00') && this.loginUser.level.level >= levelLimit && stock !== 0;
    },
    buttonString(status, expiration, waiting) {
      if (waiting) {
        return this.$t('medal_center.redeeming');
      } else {
        return status === 2
          ? expiration === '0000-00-00 00:00:00'
            ? this.$t('medal_center.already_owned')
            : this.$t('medal_center.already_redeem')
          : this.$t('medal_center.redeem');
      }
    }
  }
};
</script>

<style lang="scss" scope>
.medal-wall-container {
  border-radius: 2px;
  background: $white;
  padding: 20px;
  margin: 13px 10px;
  min-height: 45vh;
  .loading-container {
    min-height: 45vh;
  }
}
.medal-wall-header {
  display: flex;
  justify-content: space-between;
}
.medal-wall-title {
  font-size: 18px;
  font-weight: 400;
  color: $dark;
  padding-bottom: 18px;
}
.medal-card-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}
.medal-cost {
  color: rgba(250, 221, 82, 1);
  margin-bottom: 8px;
}
/*按钮样式 */
.redeem-button {
  min-width: 70px;
}
.confirm-button {
  min-width: 140px;
  margin-bottom: 15px;
}

/*对话框样式 */
.medal-dialog-content {
  text-align: center;
  font-size: 18px;
  line-height: 32px;
}
.medal-dialog-content.emphasize {
  color: $primary;
}
</style>

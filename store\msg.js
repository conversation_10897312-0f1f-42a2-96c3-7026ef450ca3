/* eslint-disable */
export const state = () => ({
  messages: [],
  unReadMsg: {
    all_count: 0,
    reply: {
      count: 0
    },
    sys_msg: {
      count: 0
    },
    guild: {
      count: 0
    }
  },
  unDynamicMsg: {
    article: {
      unread: 0
    }
  },
});

export const actions = {
  // 获取消息
  async getMessages({ commit }, { page }) {
    const res = await this.$axios.$post('/api/msg/get-reply-msg', {
      page
    });
    if (res.code === 0) {
      commit('getMessages', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },
  // 获取未知消息,无进度条
  async getUnReadMsgNot({ commit }) {
    const res = await this.$axios.$post('/api/notify/unread', null, { progress: false });
    // console.log(res)
    if (res.code === 0) {
      commit('setUnReadMsg', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },
  // 获取动态信息,无进度条
  async getDynamicMsgNot({ commit }) {
    const res = await this.$axios.$post('/api/notify/dynamic', null, { progress: false });
    // console.log(res)
    if (res.code === 0) {
      commit('setDynamicMsg', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },
};

export const mutations = {
  // 设置消息
  getMessages(state, messages) {
    state.messages = messages;
  },
  // 设置未知消息
  setUnReadMsg(state, messages) {
    state.unReadMsg = messages;
  },
  // 设置未知动态信息
  setDynamicMsg(state, messages) {
    state.unDynamicMsg = messages;
  }
};

export const getters = {
  messages(state) {
    return state.messages || [];
  },
  unReadMsg(state) {
    return state.unReadMsg || {};
  },
  unDynamicMsg(state) {
    return state.unDynamicMsg || {};
  }
};

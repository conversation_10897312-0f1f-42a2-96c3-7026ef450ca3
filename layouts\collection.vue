<template>
  <div>
    <div class="navbar">
      <Nav />
    </div>
    <nuxt />
    <Footer />
  </div>
</template>

<script>
import Nav from '../components/Nav.vue';
import Footer from '../components/Footer.vue';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  components: {
    Nav,
    Footer
  },
  data() {
    return {};
  },
  mounted() {
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  }
};
</script>

<style lang="scss" scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}
</style>

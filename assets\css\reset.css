/*
  重置浏览器默认样式:
  http://meyerweb.com/eric/tools/css/reset/
  v2.0 | 20110126
  License: none (public domain)
*/
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
ol,
ul {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
.imgWrap {
  font-size: 0;
}
.imgWrap img {
  width: 100%;
}
/* init html font-family */
@font-face {
  font-family: 'webfont';
  font-display: wrap;
  src: url('../fonts/webfont.eot');
  src: url('../fonts/webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/webfont.woff2') format('woff2'),
    url('../fonts/webfont.woff') format('woff'), url('../fonts/webfont.ttf') format('truetype'), url('../fonts/webfont.svg#webfont') format('svg');
}

html,
body {
  color: #424242;
  /* font-family: webfont; */
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
html:lang(zh-Hans) {
  font-family: webfont, '微软雅黑', 'Microsoft YaHei', PingFangSC, sans-serif;
}

html:lang(zh-Hant) {
  font-family: webfont, '微軟正黑體', PingFangTC, sans-serif;
}

/* init outline style */
input:focus,
textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(57, 215, 217, 0.5);
}

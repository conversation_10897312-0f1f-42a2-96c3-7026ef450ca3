<template>
  <!-- eslint-disable vue/no-v-html -->
  <div>
    <div v-if="hasAccessPermission" class="guild-container">
      <div class="guild-left">
        <div class="img-box imgWrap">
          <img src="@/assets/images/gonghuidating.png" />
        </div>
        <nuxt-link
          v-for="(item, idx) in partitiontMenu"
          :key="idx"
          :class="{ active: article.gid === item.gid }"
          :to="{
            path: $i18n.path(`guildhall/${item.gid}`),
            params: { id: item.gid }
          }"
        >
          <div class="img-box1 imgWrap">
            <img :src="item.pic" alt="" />
          </div>
          <span>{{ item.name }}</span>
        </nuxt-link>
      </div>
      <div class="guild-right">
        <!-- 发评论区域 -->
        <div class="comment-list-container">
          <!-- 输入内容 -->
          <div class="comment-list-item flex">
            <div v-if="isLogin" class="flex-1 hover" @click="toPage(`/profile/${loginUser.uid}`)">
              <UserAvatar :avatar-img="loginUser.avatar" />
            </div>
            <div class="comment-list-item-content">
              <CommentInput
                ref="commentinput"
                btn-box-bottom-pad="30px"
                btn-box-top-pad="30px"
                upload-url="/upload/discuss-image"
                :pleaseholder="$t('guildhall.input_content')"
                :btn-content="$t('guildhall.publish_btn')"
                :show-image-btn="false"
                :show-other-image-btn="false"
                :show-emoji-btn="false"
                :show-vote-btn="false"
                :show-add-btn="false"
                :article-num="2000"
                :islogin="isLogin"
                @confirm="publishComment"
              >
                <template>
                  <span slot="cacheDrafts" class="cache-content">
                    {{ $t('guildhall.to_cache') }}
                  </span>
                </template>
                <!-- <template v-if="article.reward">
                  <p slot="isShowName" class="flex show-name" @click="showName = !showName">
                    <span class="radio-btn" :class="{ checked: showName === true }"></span>
                    <span>是否参加抽奖</span>
                  </p>
                </template> -->
              </CommentInput>
            </div>
          </div>
          <div class="comment-container">
            <div class="comment-box">
              <div class="box-top">
                <div class="top-left">
                  <div class="avatar imgWrap mar-right-5 hover" @click="toPage(`/profile/${article.author.uid}`)">
                    <img :src="article.author.avatar" alt="" />
                  </div>
                  <span class="top-time mar-right-5">{{ article.time | date1short }}</span>
                  <div class="top-title mar-right-5 ">{{ article.author.nickname }}</div>
                  <div class="lv-icon mar-right-5" :class="[`lv-${article.author.level.level}`]">{{ article.author.level.name }}</div>
                  <div v-for="(medItem, medIdx) in article.author.medals.slice(0, 5)" :key="medIdx" class="top-img imgWrap mar-right-5">
                    <img :src="medItem.img" alt="" />
                  </div>
                </div>
                <div class="top-right">
                  <div class="top-right-content">
                    <div v-if="article.only_passer === 1" class="img-box imgWrap" :title="$t('guildhall.brave')">
                      <img src="@/assets/images/yongzhe.png" />
                    </div>
                    <div v-if="article.has_reward === 1" class="img-box imgWrap" :title="$t('guildhall.re_back')">
                      <img src="@/assets/images/jinbi.png" />
                    </div>
                    <div v-if="article.has_pay === 1" class="img-box imgWrap" :title="$t('guildhall.pay')">
                      <img src="@/assets/images/fufei.png" />
                    </div>
                    <div v-if="article.is_top === 1" class="img-box imgWrap" :title="$t('guildhall.stick')">
                      <img src="@/assets/images/zhiding.png" />
                    </div>
                  </div>
                  <!-- <div class="right-end">
                    <span v-backtop class="hover">回复</span>
                  </div> -->
                </div>
              </div>
              <div v-if="article.reward" class="box-mid">
                <span
                  >{{ $t('guildhall.reply_article_1') }}{{ article.reward.odds }}{{ $t('guildhall.reply_article_2') }}{{ article.reward.coin
                  }}{{ $t('guildhall.reply_article_3') }}{{ article.reward.num }}{{ $t('guildhall.reply_article_4') }}{{ article.reward.stock
                  }}{{ $t('guildhall.reply_article_5') }}</span
                >
              </div>
              <div class="box-bottom">
                <!-- 评论内容 -->
                <div class="comment-content">
                  <article id="article-main-contents" v-html="article.content"></article>
                  <!-- 文章锁定 -->
                  <div v-if="article.pay_info && article.pay_info.is_paid == 0" class="article-content-lock flex flex-cols jc-center ai-center">
                    <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 152.47 170.92">
                      <path
                        d="M139.67,15.83H45.92a8.26,8.26,0,0,0-8.25,8.25v43a8.26,8.26,0,0,0,8.25,8.25h4.8A8.26,8.26,0,0,0,59,67.1V45.38a8.26,8.26,0,0,1,8.25-8.25h64.2a8.25,8.25,0,0,1,8.25,8.25V67.1a8.26,8.26,0,0,0,8.25,8.25h4.8A8.26,8.26,0,0,0,161,67.1v-43a8.26,8.26,0,0,0-8.25-8.24ZM167.32,84.7h-136A8.26,8.26,0,0,0,23.1,93V178.5a8.26,8.26,0,0,0,8.25,8.25h136a8.25,8.25,0,0,0,8.25-8.25V93A8.26,8.26,0,0,0,167.32,84.7Zm-46,85.3h-44a3.52,3.52,0,0,1-3-5.3L89,139.25a20.35,20.35,0,1,1,20.8,0l14.7,25.47a3.54,3.54,0,0,1-3.1,5.28Z"
                        transform="translate(-23.1 -15.83)"
                      />
                    </svg>
                    <span class="fs-md mar-left-5 text-primary">{{ $t('detail.content_need_unlock') }}</span>
                    <button class="btn fs-sd mar-top-20 btn-primary unlock" @click="unlockContent(article.aid)">
                      <span>{{ article.pay_info.price }}{{ $t('detail.qb_unlock') }}</span>
                    </button>
                  </div>
                </div>
                <!-- 投票选项 -->
                <div v-if="article.has_poll === 1" class="article-votes">
                  <p class="fs-md article-votes-title">{{ article.poll.title }}</p>
                  <table class="article-votes-options">
                    <tr v-for="(item, index) in article.poll.options" :key="index" class="vote-options flex">
                      <td class="options-item-radio flex ai-end jc-center">
                        <div class="options-item-radio flex ai-end jc-center">
                          <div
                            :class="{ active: selectedVoteIdx[item.oid] }"
                            class="outter-radio flex ai-end jc-center btn"
                            @click="selectVoteOption(item.oid)"
                          >
                            <span class="radio bg-gray"></span>
                          </div>
                        </div>
                      </td>
                      <td class="options-item-progress flex  flex-1">
                        <div class="progress-title">
                          <span class="title-name">{{ item.text }}</span>
                        </div>
                      </td>
                      <td class="box-right">
                        <div class="text-gray mar-right-5">{{ item.votes }} {{ $t('detail.vote') }}</div>
                      </td>
                      <td class="progress-box">
                        <div class="progress-bar mar-right-5" style="margin-top=5px;">
                          <!-- 进度条 -->
                          <p :style="{ width: `${item.percent}%` }" class="progress-bar-progress"></p>
                        </div>
                      </td>
                      <td class="progress-right">
                        <div class="text-gray">
                          <span style="display: inline-block;width: 42.39px;text-align: end;">{{ item.percent }}%</span>
                        </div>
                      </td>
                    </tr>
                  </table>

                  <!-- 投票按钮 -->
                  <div class="article-votes-submit flex ai-center jc-center">
                    <button class="article-votes-btn btn btn-primary" @click="castVote">{{ $t('detail.btn_vote') }}</button>
                  </div>
                </div>
                <!-- 锚点跳转的回复位置 -->
                <div id="comments"></div>
                <!-- 回复内容 -->
                <div class="reply-content">
                  <!-- 开始: 帖子  -->
                  <Loading v-show="loadingComments" />
                  <!-- 回复的回复 -->
                  <div v-if="comments.reply_list && comments.reply_list.length > 0 && !loadingComments">
                    <div
                      v-for="(item, key) in comments.reply_list"
                      :key="item.tid"
                      class="reply-box"
                      :data-id="`reply_${item.rid}`"
                      @mouseover="changeActive($event)"
                      @mouseout="removeActive($event)"
                    >
                      <div class="flex rtop-cont-box">
                        <div class="rtop-box">
                          <span class="r-name mar-right-5 btn" @click="toPage(`/profile/${item.user_info.uid}`)">{{ item.user_info.nickname }}</span>
                          <div class="lv-icon mar-right-5" :class="[`lv-${item.user_info.level.level}`]">{{ item.user_info.level.name }}</div>
                          <span v-if="item.r_user_info" class="fs-sm mar-right-5">
                            {{ $t('components.btn_reply') }}
                            <span class="to-name text-link">@{{ item.r_user_info.nickname }}</span>
                          </span>
                          <span class="r-time mar-right-5">{{ item.time | date1short }}</span>
                          <span v-if="item.reward" class="r-reward mar-right-5">
                            <span class="write-icon">
                              <span class="write-in-icon"></span>
                            </span>
                            <span style="color:#39d7d9;font-size: 15px;line-height: 16px;">回复奖励+{{ item.reward.coin }}轻币</span>
                          </span>
                        </div>
                        <div class="end-box">
                          <span class="relpyComment" @click="showCommentInput(key, item.user_info.nickname, false, { item, comments })">
                            {{ $t('guildhall.reply') }}
                          </span>
                          <span v-if="canDeleteDiscuss" class="relpy" @click="delDiscuss(comments.tid, item.rid, key)">{{
                            $t('guildhall.del')
                          }}</span>
                        </div>
                      </div>
                      <div class="rbottom-box">
                        <article class="r-content1" v-html="item.content"></article>
                      </div>
                    </div>
                  </div>
                  <!-- 底部分页 -->
                  <div class="top-tips" style="height=200px">
                    <div v-if="paginate" class="paginate">
                      <el-pagination
                        class="my-pagination"
                        layout="prev, pager, next, jumper"
                        :prev-text="$t('paginate.prev')"
                        :next-text="$t('paginate.next')"
                        :hide-on-single-page="true"
                        :disabled="loadingComments"
                        :current-page="paginate.cur"
                        :total="paginate.count"
                        :page-size="paginate.size"
                        @current-change="commentsPageChange($event, true)"
                      />
                    </div>
                  </div>
                </div>
                <!-- 底部回复输入框 -->
                <div v-if="showCommentInputIndex === true" class="comment-reply-input">
                  <CommentInput
                    btn-box-bottom-pad="15px"
                    btn-box-top-pad="15px"
                    :show-image-btn="false"
                    :islogin="isLogin"
                    :article-num="2000"
                    :pleaseholder="showCommentPleaseholder"
                    @confirm="publishReply"
                  >
                    <!-- <template>
                    <p slot="isShowName2" class="flex show-name2" @click="showName = !showName">
                      <span class="radio-btn" :class="{ checked: showName === true }"></span>
                      <span>是否匿名</span>
                    </p>
                  </template> -->
                  </CommentInput>
                </div>
              </div>
            </div>
          </div>
          <!-- 结束: 帖子列表 -->
        </div>
      </div>
    </div>
    <!-- 没有权限 -->
    <div v-else class="access-permission-denied-container flex jc-center ai-center">
      <div class="flex flex-cols">
        <div class="img-box1">
          <img src="@/assets/images/401_2.jpg" />
        </div>
        <div class="flex">
          <div class="img-box">
            <img src="@/assets/images/401_1.png" />
          </div>
          <ul>
            <li>{{ $t('write.401-con1') }}</li>
            <li>{{ $t('write.401-con2') }}</li>
            <li>{{ $t('write.401-con3') }}</li>
          </ul>
        </div>
        <div class="return-last" @click="toClosePage()"><span>&lt;</span> {{ $t('components.close_btn') }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import CommentInput from '@/components/CommentInput.vue';
import UserAvatar from '@/components/UserAvatar.vue';
import Loading from '@/components/Loading.vue';
import { category } from '@/api';

export default {
  layout: 'guild',
  components: {
    Loading,
    UserAvatar,
    CommentInput
  },
  async fetch() {
    await this.$store.dispatch('emoji/getEmojis');
  },
  // 请求数据
  async asyncData({ params, query, $axios, store, redirect }) {
    const data = {};
    const subCateRes = await $axios.$post(category.getCategories, { parent_gid: 42 });
    if (subCateRes.code === 0) {
      // 屏蔽130
      data.partitiontMenu = subCateRes.data.filter((el) => el.gid !== 130);
    }
    const aid = Number(params.id);
    const detailRes = await $axios.$post('/api/article/get-detail', {
      aid,
      simple: 0
    });
    // console.log(detailRes, 'detailRes');
    if (detailRes.code === 0) {
      data.article = detailRes.data;
      const tid = detailRes.data.main_tid;
      // console.log(detailRes.data);
      // 获取数据
      // 如果这是一个回复奖励帖，得重新获取回复数据
      let commentRes = {};
      if (detailRes.data.reward) {
        commentRes = await $axios.$post('/api/discuss/get-reply', {
          tid,
          aid,
          page: 1
        });
        data.comments = commentRes.data;
        data.paginate = commentRes.data.page_info || false;
      } else {
        commentRes = await $axios.$post('/api/discuss/get-reply', {
          tid,
          page: 1
        });
        // console.log(commentRes, 'commentRes');
        if (commentRes.code === 0) {
          data.comments = commentRes.data;
          data.paginate = commentRes.data.page_info || false;
        }
      }
      // 如果是瞄点跳过来的
      if (query && query.rid) {
        const commentAddress = await $axios.$post('/api/discuss/get-discuss-page', {
          aid,
          rid: query.rid || '',
          tid
        });
        data.commentAddress = commentAddress.code === 0 ? commentAddress.data : {};
      }
    }
    return data;
  },

  data: () => ({
    commentAddress: {}, // 当前页数
    isPublishComment: true, // 是否可以发帖
    showName: false,
    isHotline: false, // 是否有热门下划线
    isscroll: false, // 是否添加滚动效果
    casted: false, // 是否已经投过票
    repliesLoading: false, // 回复loading
    openedReplyKey: {}, // 已经展开的评论的回复列表key
    openedReplies: {}, // 已经展开的评论的回复列表
    loadingComments: false, // 是否在加载
    otherRecoms: [], // 合集其他帖子
    paginate: {},
    article: {
      author: { level: {}, medals: [] },
      already_follow: false, // 是否关注作者
      already_fav: false, // 是否收藏本文章
      already_coin: false, // 是否投币
      favorites: 0, // 收藏数量
      coins: 0, // 投币数量
      only_passer: 0, // 是否正式会员可见
      only_app: 0, // 是否仅APP可见
      time: '2020-10-2',
      poll: {
        options: [
          { oid: 1, text: '角色A', votes: '120', percent: 54 },
          { oid: 2, text: '角色b', votes: '120', percent: 36 }
        ]
      }
    },
    comments: {},
    headShowGiveCoins: false, // 头部的是否显示投币
    showShare: false,
    showGiveCoins: false, // 是否显示投币
    showArticleLock: false, // 是否显示文章锁定
    showCommentPleaseholder: '', // 显示的回复输入框的pleaseholder
    showCommentInputIndex: -1, // 当前评论的回复输入框显示的位置
    commentContent: '', // 评论内容
    commentImgs: [], // 是否回复显示预览图片
    loginStatus: false, // 是否登录
    showEmojiPanel: false, // 是否显示表情面板
    selectedVoteIdx: {}, // 被选中的投票选项的index
    canDeleteDiscuss: false, // 是否显示删除评论按钮
    ad: [],
    timer: '',
    key: '',
    commentReplyReplys: [], // 回复的回复
    curName: ''
  }),
  computed: {
    ...mapState('login', ['isLogin', 'loginUser']),
    hasAccessPermission() {
      // 仅APP可见的不能访问
      if (this.article.only_app) {
        return false;
      }

      // 没有设置权限所有人可以访问
      if (!this.article.only_passer) {
        return true;
      }

      // 设置了权限, 没有登录不能访问
      if (!this.isLogin) {
        return false;
      }

      // 设置了权限, 但是作者是自己或者登录的人是会员, 可以访问
      if (this.loginUser.uid === this.article.author.uid || this.loginUser.passer) {
        return true;
      }
      return false;
    }
  },
  watch: {
    isLogin(val) {
      val && this.addHistory();
      // 判断已登录的用户是否允许删除评论
      if (val) {
        if (this.article.operations && this.article.operations.delete_discuss === 1) {
          this.canDeleteDiscuss = true;
        } else {
          this.canDeleteDiscuss = false;
        }
      } else {
        this.canDeleteDiscuss = false;
      }
    }
  },
  async mounted() {
    // console.log(this.commentAddress, 'commentAddress');
    // console.log(this.article, 'article');
    // console.log(this.comments, 'comments');
    // 1.增加访问记录
    if (process.browser) {
      this.isLogin && this.initVotedOptions(this.article.poll);
      this.initContentLinks();

      // 处理瞄点
      if (this.commentAddress && this.commentAddress.reply) {
        if (this.commentAddress.topic.reply !== 1) {
          await this.commentsPageChange(this.commentAddress.reply.page);
        }
        this.getLocal();
      }
      this.$nextTick(() => {
        this.initViewPosition();
        this.isLogin && this.addHistory();
      });
    }
  },
  methods: {
    // 刷新文章
    // async refreshDelArticle() {
    //   const res = await this.$axios.$post('/api/article/get-detail', {
    //     aid: this.comments.pid,
    //     simple: 0
    //   });
    //   if (res.code === 0) {
    //     this.article = res.data;
    //     console.log(this.article, 'this.article');
    //   }
    // },
    // 移动到该内容显示删除按钮
    changeActive($event) {
      if (this.canDeleteDiscuss) {
        $event.currentTarget.className = 'reply-box1';
      }
    },
    removeActive($event) {
      if (this.canDeleteDiscuss) {
        $event.currentTarget.className = 'reply-box';
      }
    },
    // 关闭
    toClosePage() {
      window.close();
    },

    // 如果有 #锚点 就跳到指定位置
    initViewPosition() {
      const anchor = this.$route.hash && document.querySelector(this.$route.hash);
      anchor && anchor.scrollIntoView();
    },
    // 去到对应评论或者回复的位置
    getLocal() {
      this.timer = setInterval(() => {
        this.render();
      }, 1000);
    },
    render() {
      try {
        const comment = document.querySelector(`[data-id='reply_${this.$route.query.rid}']`);
        if (comment) {
          comment.scrollIntoView();
          clearInterval(this.timer);
        }
      } catch {}
    },
    // 初始化文章内容中的 a 标签, 所有a标签都在新标签页打开
    initContentLinks() {
      const aNodes = document.querySelectorAll('#article-main-contents a');
      aNodes && aNodes.length && Array.from(aNodes).forEach((item) => (item.target = '_blank'));

      // 限制图片宽度
      // const imgs = document.querySelectorAll('#article-main-contents img');
      // imgs && imgs.length && Array.from(imgs).forEach((item) => (item.style.maxWidth = '750px'));
    },

    // 初始化被选中的投票选项
    initVotedOptions(poll) {
      if (this.article.has_poll && poll.voted && poll.voted.length) {
        poll.voted.forEach((item) => (this.selectedVoteIdx[item] = item));
      }
    },

    // curChange
    async commentsPageChange(page, back = false) {
      const params = {};
      if (this.article.reward) {
        params.aid = this.comments.pid;
      }
      params.tid = this.comments.tid;
      params.page = page;
      this.loadingComments = true;
      const res = await this.$axios.$post('/api/discuss/get-reply', params);
      if (res.code === 0) {
        this.loadingComments = false;
        this.comments = res.data;
        this.paginate = res.data.page_info;
        this.$nextTick(() => {
          back && document.querySelector('#comments').scrollIntoView();
        });
      }
    },

    // 解锁文章内容(goods_id:  1:解锁文章 2:文章投币)
    unlockContent(aid) {
      this.loginNext(async () => {
        const data = { params: aid };
        const { price } = this.article.pay_info;
        data.goods_id = 1;
        data.price = price;
        data.total_price = price;
        data.number = 1;
        const res = await this.$axios.$post('/api/coin/use', data);
        res.code === 0 && window.location.reload(true);
      });
    },

    // 显示评论输入框
    showCommentInput(key = true, username, isComment = false, item) {
      // 跳到底部
      this.toBottomScroll();
      this.showCommentInputIndex = true;
      this.loginNext(() => {
        this.showCommentInputIndex = true;
        // console.log(this.showCommentInputIndex);
        this.showCommentReplyParams = item; // 当前显示评论回复输入框, 的额外参数
        // console.log(this.showCommentReplyParams);
        this.showCommentReplyIsComment = true; // 当前显示评论回复输入框, 是否是评论
        this.showCommentPleaseholder = this.$t('detail.publish_replay');
        if (!isComment) {
          this.showCommentReplyIsComment = false;
          this.showCommentPleaseholder += `@${username}`;
          this.curName = username;
        }
      });
    },
    toBottomScroll() {
      let x = document.body.scrollTop || document.documentElement.scrollTop;
      const clients = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      const wholeHeight = document.body.scrollHeight;
      const timer = setInterval(function() {
        x = x + 100;
        if (x >= wholeHeight - clients) {
          x = wholeHeight - clients;
          window.scrollTo(x, x);
          clearInterval(timer);
        }
        window.scrollTo(x, x);
      }, 16.7);
    },
    // 发布回复
    async publishReply({ content }) {
      // 发布评论避让需要先调用: showCommentInput 所以,
      // 是回复评论还是回复评论的回复: showCommentReplyIsComment
      // 当前点击那一项的额外参数: showCommentReplyParams
      // 如果正在发送中就停止避免重复发布
      if (this.publishReplyReqing) return;
      if (!content.trim()) {
        this.$message.error(this.$t('detail.please_input_reply'));
        return;
      }
      const params = {
        content,
        aid: this.article.aid
      };
      if (this.showCommentReplyIsComment) {
        // 回复评论:
        params.tid = this.showCommentReplyParams.tid;
      } else {
        // 回复评论的回复:
        if (this.article.reward && this.article.reward.status === 0 && this.article.reward.stock > 0) {
          params.reward = 1;
        }
        params.tid = this.showCommentReplyParams.comments.tid;
        params.r_uid = this.showCommentReplyParams.item.uid;
        params.r_rid = this.showCommentReplyParams.item.rid;
      }
      // console.log(params);
      this.publishReplyReqing = true; // 正在发送...
      const res = await this.$axios.$post('/api/discuss/post-reply', params);
      this.publishReplyReqing = false;
      if (res.code === 0) {
        this.showCommentInputIndex = -1;
        this.refreshArticle();
        this.$message.success(this.$t('detail.reply_success'));
      }
    },
    // 刷新评论(发评论后刷新评论内容)
    async refreshArticle() {
      const params = {};
      if (this.article.reward) {
        params.aid = this.comments.pid;
      }
      params.tid = this.comments.tid;
      params.page = 1;
      const res = await this.$axios.$post('/api/discuss/get-reply', params);
      if (res.code === 0) {
        this.comments = res.data;
      }
    },
    // /api/discuss/post-reply
    // 发布评论，节流操作
    publishComment({ content }) {
      // 如果正在发送中就停止避免重复发布
      if (this.publishCommentReqing) {
        return;
      }
      // console.log(content, 'content');
      this.loginNext(async () => {
        if (content.trim()) {
          const params = {};
          params.content = content;
          // !this.isEmptyObject(images) && (params.images = images);
          params.tid = this.comments.tid;
          params.aid = this.comments.pid;
          if (this.article.reward && this.article.reward.status === 0 && this.article.reward.stock > 0) {
            params.reward = 1;
          }
          if (this.isPublishComment) {
            // console.log(this.article, 'this.article');
            this.publishCommentReqing = true;
            const res = await this.$axios.$post('/api/discuss/post-reply', params);
            this.publishCommentReqing = false;
            if (res.code === 0) {
              this.$message.success(this.$t('tips.publish_success'));
              this.refreshArticle();
              this.$refs.commentinput.clearContent();
            } else {
              this.$message.success(this.$t('tips.publish_fail'));
            }
          } else {
            this.$message.warning('此操作需要间隔15秒');
          }
          if (!this.timer2) {
            this.isPublishComment = false;
            const context = this;
            this.timer2 = setTimeout(function() {
              context.isPublishComment = true;
              context.timer2 = null;
            }, 15000);
          }
        } else {
          this.$message.warning('请输入帖子内容');
        }
      });
    },

    // 增加访问记录
    addHistory() {
      this.$axios.post('/api/history/add-history', {
        fid: this.article.aid,
        class: 1
      });
    },

    // 选择投票选项
    selectVoteOption(id) {
      const index = id;
      const { multiple } = this.article.poll;
      // 单选
      if (!multiple) {
        this.selectedVoteIdx = { [index]: true };
        return;
      }

      // 多选
      const maxChooice = this.article.poll.max_choices;
      if (index in this.selectedVoteIdx) {
        this.selectedVoteIdx[index] = !this.selectedVoteIdx[index];
        return;
      }
      const selectedOptions = {};
      let selectedCount = 0;
      for (const key in this.selectedVoteIdx) {
        if (this.selectedVoteIdx[key]) {
          selectedOptions[key] = true;
          selectedCount++;
        }
      }
      this.selectedVoteIdx = selectedOptions;
      if (selectedCount < maxChooice) {
        this.$set(this.selectedVoteIdx, index, true);
      }
    },

    // 投票
    castVote() {
      this.loginNext(async () => {
        const poll = this.article.poll;
        if (poll.voted && poll.voted.length) {
          this.$message.error(this.$t('detail.already_cost'));
          return;
        }
        if (this.casting) {
          return;
        }
        const selected = Object.keys(this.selectedVoteIdx);
        if (selected && selected.length === 0) {
          this.$message.info(this.$t('detail.least_one_item'));
          return;
        }
        // console.log(selected, 'selected');
        this.casting = true;
        if (this.casted) {
          this.$message.error(this.$t('detail.already_cost'));
          return;
        }
        const res = await this.$axios
          .$post('/api/article/poll', {
            aid: this.article.aid,
            oids: selected
          })
          .finally(() => (this.casting = false));
        if (res.code === 0) {
          this.casted = true;
          this.toSaveVote(selected);
          this.$message.success(this.$t('detail.case_vote_success'));
        }
      });
    },
    // 前端保留投票结果并赋值(包含多选和单选的情况)
    toSaveVote(id) {
      const poll = this.article.poll;
      if (poll.voted && poll.voted.length) {
        this.$message.error(this.$t('detail.already_cost'));
        return false;
      }
      // console.log(this.article.poll);

      // 单选
      if (!poll.multiple) {
        // console.log(Number(id[0]));
        // 单选票数加1
        if (poll && poll.options) {
          this.article.poll.options.find((el) => el.oid === Number(id[0])).votes += 1;
          // console.log(Number(id[0]));
          // 百分比变化
          const all = poll.options[0].votes + poll.options[1].votes;
          this.article.poll.options[0].percent = Math.round((poll.options[0].votes / all) * 100);
          this.article.poll.options[1].percent = Math.round((poll.options[1].votes / all) * 100);
          return true;
        }
      }
      // 多选
      if (poll && poll.options) {
        for (let i = 0; i < id.length; i++) {
          for (let j = 0; j < poll.options.length; j++) {
            if (poll.options[j].oid === Number(id[i])) {
              this.article.poll.options[j].votes += 1;
            }
          }
        }
        // console.log(this.article.poll);
        // 计算总票数
        let alls = 0;
        poll.options.forEach((el) => {
          alls += el.votes;
        });
        // 算出各自百分比
        poll.options.forEach((el, idx) => {
          this.article.poll.options[idx].percent = Math.round((el.votes / alls) * 100);
        });
        return true;
      }
      // console.log(this.casting);
      // 多选
    },
    // 删除评论
    delDiscuss(tid, rid, keyReply) {
      this.loginNext(() => {
        this.$confirm(this.$t('tips.are_you_sure'), this.$t('components.type_warn'), {
          confirmButtonText: this.$t('components.btn_confirm'),
          cancelButtonText: this.$t('components.btn_cancel'),
          type: 'warning'
        })
          .then(() => {
            // 如果是回复
            // 如果是已展开
            this.comments.reply_list.splice(keyReply, 1);
            const url = '/api/discuss/del-reply';
            this.$axios.$post(url, { tid, rid }).then((res) => {
              // // 某人说不弹通知……
              // if (res.code === 0) {
              //   this.$message.success(this.$t('tips.delete_success'));
              // } else {
              //   this.$message.success(this.$t('tips.delete_fail'));
              // }
            });
          })
          .catch(Function.prototype);
      });
    }
  },
  head() {
    const title = this.$t('title');
    if (!this.$route.params.id) {
      return { title };
    }
    const curCate = this.partitiontMenu.find((item) => item.gid === Number(this.article.gid));
    const name = this.$t(`guildhall.${curCate.name}`);
    if (!curCate) {
      return { title };
    }
    return { title: name + '-' + title };
  }
};
</script>
<style lang="scss" scoped>
.guild-container {
  margin-top: 29px;
  display: flex;
  background-color: white;
  .guild-left {
    background-color: white;
    width: 140px;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 999;
    a {
      color: #b2b2b2;
      &:hover {
        color: #3ad8d9;
      }
      &.active,
      &.exact-active-link {
        span {
          color: #39d7d9;
        }
      }
    }
    .img-box {
      width: 110px;
      height: 95px;
      margin: 15px 15px 29px;
    }
    .img-box1 {
      width: 120px;
      height: 70px;
      margin: 19px 10px 0px 10px;
      overflow: hidden;
      & ~ span {
        font-size: 15px;
        display: inline-block;
        margin: 9px 10px 0px 10px;
      }
    }
  }
  .guild-right {
    width: 100%;
    padding: 31px 31px 5px;
    .comment-list-item-content {
      color: #b2b2b2;
      width: 100%;
      position: relative;
    }
    .comment-container {
      border-top: 1px solid #eeeeee;
      padding-top: 14px;
      text-align: justify;
      .comment-box {
        .box-top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .top-left {
            display: flex;
            align-items: center;
            .top-time {
              font-size: 15px;
              color: #b2b2b2;
            }
            .top-title {
              font-size: 15px;
              color: #424242;
            }
            .top-zhi {
              font-size: 13px;
              color: #ffffff;
              background-color: #56d2f5;
              height: 15px;
              width: 35px;
              line-height: 15px;
              border-radius: 5px;
              text-align: center;
            }
            .top-img {
              width: 30px;
              height: 30px;
              overflow: hidden;
              border-radius: 50%;
            }
            .avatar {
              width: 30px;
              height: 30px;
              overflow: hidden;
              border-radius: 50%;
              img {
                border-radius: 50px;
              }
            }
          }
          .top-right {
            display: flex;
            align-items: center;
            // width: 150px;
            justify-content: space-between;
            text-align: end;
            .top-right-content {
              display: flex;
              width: 111px;
              justify-content: flex-end;
              .img-box {
                width: 15px;
                height: 15px;
                cursor: pointer;
                margin-left: 5px;
              }
            }
            .right-end {
              height: 16px;
              font-size: 15px;
              color: #b2b2b2;
              line-height: 15px;
              cursor: pointer;
            }
          }
        }
        .box-mid {
          margin-top: 5px;
          background-color: rgb(235, 252, 252);
          line-height: 32px;
          border-radius: 5px;
          display: table;
          padding: 0 15px;
          span {
            color: #39d7d9;
            font-size: 15px;
          }
        }
        .box-bottom {
          margin-top: 6.5px;
          .comment-content {
            max-width: 1098px;
            font-size: 15px;
            color: #424242;
            line-height: 24px;
            margin-bottom: 15.5px;
            white-space: pre-wrap;
            word-break: break-word;
            padding: 0 20px;
            .article-content-lock {
              width: 100%;
              padding: 30px 0;
              .svg-icon {
                height: 15px;
                fill: $primary;
              }
              .unlock {
                height: 35px;
                line-height: 35px;
                padding: 0 20px;
                border-radius: 35px;
              }
            }
          }
          .article-votes {
            width: 750px;
            .article-votes-title {
              text-align: center;
              margin-bottom: 14px;
            }

            // 投票选项
            .vote-options {
              height: 15px;
              box-sizing: border-box;
              margin-bottom: 9px;
              .options-item-radio,
              .options-item-counter {
                width: 38px;
                margin-right: 22px;
                span {
                  &:first-of-type {
                    font-size: 15px;
                    color: #3ad8d9;
                  }
                }
              }
              // 左边选择按钮
              .options-item-radio {
                .outter-radio {
                  width: 14px;
                  height: 14px;
                  box-sizing: border-box;
                  padding: 2px;
                  overflow: hidden;
                  border-radius: 50%;
                  background-color: rgba(178, 178, 178, 0.2);
                  &.active {
                    background-color: rgba(57, 215, 217, 0.2);
                    .radio {
                      background-color: $primary;
                    }
                  }

                  .radio {
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    border-radius: 50%;
                  }
                }
              }

              // 中间进度条
              .options-item-progress {
                height: 18px;
                margin-left: 12px;
                margin-right: 12px;
                align-items: baseline;
                justify-content: space-between;
                .progress-title {
                  white-space: nowrap;
                  .title-name {
                    display: inline-block;
                    margin-right: 15px;
                  }
                }
                .progress-right {
                  width: 66%;
                  display: flex;
                  justify-content: flex-end;
                  align-items: baseline;
                  white-space: nowrap;
                  .box-right {
                    width: 100%;
                    display: flex;
                    align-items: center;
                  }
                }
              }
              .progress-box {
                position: relative;
                .progress-bar {
                  height: 10px;
                  max-height: 20px;
                  width: 300px;
                  background: $gray-white;
                  border-radius: 5px;
                  margin-top: 4px;
                  overflow: hidden;
                  position: relative;
                  .progress-bar-progress {
                    position: absolute;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    background: $primary;
                    height: 10px;
                  }
                }
              }
            }

            // 投票按钮
            .article-votes-submit {
              padding: 10px 0;
              .article-votes-btn {
                width: 70px;
                height: 25px;
                line-height: 25px;
                border-radius: 35px;
              }
            }
          }
          .reply-content {
            text-align: justify;
            .reply-box {
              padding: 15px;
              display: table;
              background: #fafafa;
              margin-bottom: 5px;
              border-radius: 5px;
              .rtop-cont-box {
                display: flex;
                justify-content: space-between;
                .rtop-box {
                  display: flex;
                  justify-content: space-between;
                  line-height: 15px;
                  height: 21px;
                  align-items: center;
                  margin-bottom: 13px;
                  max-width: 1098px;
                  .r-name {
                    color: #608ab8;
                  }
                  .r-time {
                    height: 15px;
                    font-size: 15px;
                    color: #b2b2b2;
                    line-height: 15px;
                  }
                  .r-reward {
                    .write-icon {
                      display: inline-flex;
                      background-color: #39d7d9;
                      width: 14px;
                      height: 14px;
                      border-radius: 50%;
                      vertical-align: bottom;
                      align-items: center;
                      .write-in-icon {
                        display: inline-block;
                        background-color: white;
                        width: 5px;
                        height: 5px;
                        transform: rotate(44deg);
                        margin: auto;
                      }
                    }
                  }
                }
                .end-box {
                  line-height: 22px;
                }
                .relpy {
                  display: none;
                }
                .relpyComment {
                  cursor: pointer;
                  margin-left: 5px;
                  height: 15px;
                  font-size: 15px;
                  color: #b2b2b2;
                  line-height: 15px;
                }
              }
              .rbottom-box {
                padding: 0 20px;
                .r-content1 {
                  font-size: 15px;
                  color: #424242;
                  line-height: 24px;
                  max-width: 1068px;
                  white-space: pre-wrap;
                  word-break: break-all;
                }
              }
            }
            .reply-box1 {
              padding: 15px;
              display: table;
              background: #fafafa;
              margin-bottom: 5px;
              border-radius: 5px;
              .rtop-cont-box {
                display: flex;
                justify-content: space-between;
                .rtop-box {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  line-height: 15px;
                  height: 21px;
                  margin-bottom: 13px;
                  max-width: 1098px;
                  .r-name {
                    color: #608ab8;
                  }
                  .r-time {
                    height: 15px;
                    font-size: 15px;
                    color: #b2b2b2;
                    line-height: 15px;
                  }
                  .r-reward {
                    .write-icon {
                      display: inline-flex;
                      background-color: #39d7d9;
                      width: 14px;
                      height: 14px;
                      border-radius: 50%;
                      vertical-align: bottom;
                      align-items: center;
                      .write-in-icon {
                        display: inline-block;
                        background-color: white;
                        width: 5px;
                        height: 5px;
                        transform: rotate(44deg);
                        margin: auto;
                      }
                    }
                  }
                }
                .end-box {
                  line-height: 22px;
                }
                .relpy {
                  cursor: pointer;
                  margin-left: 5px;
                  height: 15px;
                  font-size: 15px;
                  color: #b2b2b2;
                  line-height: 15px;
                }
                .relpyComment {
                  cursor: pointer;
                  margin-left: 5px;
                  height: 15px;
                  font-size: 15px;
                  color: #b2b2b2;
                  line-height: 15px;
                }
              }
              .rbottom-box {
                padding: 0 20px;
                .r-content1 {
                  font-size: 15px;
                  color: #424242;
                  line-height: 24px;
                  max-width: 1068px;
                  white-space: pre-wrap;
                  word-break: break-all;
                }
              }
            }
            .conf-btn {
              margin-top: 20px;
              margin-bottom: 24px;
              .btn-box {
                display: flex;
                line-height: 30px;
                align-items: center;
                button {
                  cursor: pointer;
                  outline: none;
                  border: 1px solid #eeeeee;
                  transition: all 0.1s;
                  background-color: white;
                  border-radius: 5px;
                  &:first-of-type,
                  &:nth-child(5) {
                    width: 60px;
                    height: 30px;
                    margin-right: 5px;
                  }
                  &:nth-child(2),
                  &:nth-child(3),
                  &:nth-child(4) {
                    margin-right: 5px;
                    width: 30px;
                    height: 30px;
                  }
                  &:hover {
                    background-color: #39d7d9;
                    color: white;
                  }
                }
                .inp-box {
                  input {
                    width: 60px;
                    height: 30px;
                    border: 1px solid #eeeeee;
                    border-radius: 5px;
                    margin-left: 5px;
                    margin-right: 5px;
                    outline: none;
                  }
                  input:focus {
                    outline: 0;
                    border-color: #39d7d9;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.comment-input-btns {
  position: relative;
}
// 没有权限
.access-permission-denied-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 999999999;
  .return-last {
    cursor: pointer;
    position: absolute;
    color: #73e2e4;
    transform: translateX(-50%);
    font-size: 25px;
    bottom: 198px;
    left: 49%;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    color: #3ad8da;
    line-height: 51px;
  }
  .btn-close {
    width: 150px;
    height: 40px;
    border-radius: 40px;
    margin: 0 auto;
  }
  .img-box {
    width: 210px;
    height: 257px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .img-box1 {
    width: 550px;
    height: 238px;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
    }
  }
  ul {
    margin-top: -10px;
    margin-left: 55px;
    letter-spacing: 3px;
    font-size: 22px;
    list-style: disc;
    li {
      margin-top: 25px;
    }
  }
}
.cache-content {
  font-size: 15px;
  position: absolute;
  color: #b2b2b2;
  line-height: 30px;
  right: 99px;
}
.show-name {
  position: absolute;
  bottom: 33px;
  left: 80px;
  .radio-btn {
    width: 15px;
    height: 15px;
    background: #dfdfdf;
    margin-right: 15px;
    border-radius: 50%;
    cursor: pointer;
    &.checked {
      background: $primary;
    }
    &.checked2 {
      background: $primary;
    }
  }
  span {
    &:last-of-type {
      line-height: 14px;
    }
  }
}
.mar-right-5 {
  margin-right: 5px;
}
.btn-publish {
  width: 130px;
  height: 35px;
  border-radius: 35px;
  text-align: center;
  margin: 0 auto;
}
.btn-publish1 {
  width: 130px;
  height: 35px;
  border-radius: 35px;
  text-align: center;
  margin: 0 auto;
  border: 1px solid gray;
}
.show-name2 {
  position: absolute;
  bottom: 22px;
  left: 82%;
  .radio-btn {
    width: 15px;
    height: 15px;
    background: #dfdfdf;
    margin-right: 15px;
    border-radius: 50%;
    cursor: pointer;
    &.checked {
      background: $primary;
    }
    &.checked2 {
      background: $primary;
    }
  }
  span {
    &:last-of-type {
      line-height: 14px;
    }
  }
}
</style>
<style lang="scss">
.r-content1 {
  img {
    max-width: 750px !important;
  }
}
#article-main-contents {
  img {
    max-width: 750px !important;
  }
}
</style>

<template>
  <div>
    <IndexMenu :menu="categoryMenu">
      <template v-slot:guildhall>
        <div>
          <img src="@/assets/images/liaotianshi.png" alt="" />
          <nuxt-link
            :to="{
              path: $i18n.path('guildhall/129'),
              params: { id: 129 }
            }"
            target="_blank"
          >
            公会大厅
          </nuxt-link>
        </div>
      </template>
      <!-- <template v-slot:confinement>
        <nuxt-link :to="$i18n.path(`/confinement/`)" target="_blank" class="">
          <span> {{ $t('links.guardhouse') }} </span>
        </nuxt-link>
      </template> -->
    </IndexMenu>
    <div class="category">
      <div class="tags-menu flex">
        <nuxt-link
          v-for="item in partitiontMenu"
          :key="item.gid"
          :to="{
            path: $i18n.path(`category/${$route.params.category}/${item.gid}`),
            params: { subCategory: item.gid },
            query: { type: $route.query.type }
          }"
          class="tag"
        >
          {{ $t(`linkSon.${item.name}`) }}
        </nuxt-link>
      </div>
      <nuxt-child :key="$route.params.subCatecory" />
    </div>
  </div>
</template>
<script>
import { category } from '@/api';
import IndexMenu from '@/components/IndexMenu';
export default {
  components: {
    IndexMenu
  },

  async asyncData({ store, params, query, $axios }) {
    const data = {};
    const resCategory = await $axios.$post(category.getArticleByCates, { depth: 2, cache: true });
    if (resCategory.code === 0) {
      data.categoryMenu = resCategory.data.slice(0, resCategory.data.length - 1);
    }
    const cateGid = Number(params.category);
    const subCateRes = await $axios.$post(category.getCategories, { parent_gid: cateGid });
    if (subCateRes.code === 0) {
      data.partitiontMenu = subCateRes.data;
    }

    // 如果是大分区是 "轻小说(cateGid: 3)" 删除 "全部(gid=0)" 分区
    // 如果需要修改这个弱智的需求: 请修改以下两个文件
    // (/pages/_lang/index.vue 和 /pages/_lang/category/_category/index.vue)
    // cateGid === 3 && data.partitiontMenu.shift();

    return data;
  },

  data: () => ({
    categoryMenu: [],
    partitiontMenu: []
  }),
  head() {
    const title = this.$t('title');
    if (!this.$route.params.category) {
      return { title };
    }
    const curCate = this.categoryMenu.find((item) => item.gid === Number(this.$route.params.category));
    const name = this.$t(`links.${curCate.name}`);
    if (!curCate) {
      return { title };
    }
    return { title: name + '-' + title };
  }
};
</script>
<style lang="scss" scoped>
.category {
  position: relative;
  width: 1160px;
  margin: 0 auto;
  .tags-menu {
    margin-top: $ml;
    margin-left: -20px;
    .tag {
      display: block;
      width: 82px;
      height: $maxlg;
      line-height: $maxlg;
      text-align: center;
      border-radius: $sm;
      color: $dark;
      text-decoration: none;
      border: 1px solid $gray-white;
      margin-left: 20px;
      &:hover,
      &.nuxt-link-active {
        color: $white;
        background-color: $primary;
      }
    }
  }
}
</style>

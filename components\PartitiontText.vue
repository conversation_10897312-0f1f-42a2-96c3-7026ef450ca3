<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="module-list">
    <div v-for="item in items" :key="item.aid" class="module-item flex jc-between">
      <div class="flex-1">
        <a :href="$i18n.path(`detail/${item.aid}`)" target="_blank" :title="item.title" class="module-title text-hide-3">
          <span v-if="item.is_top" class="lv-icon lv-0 border">{{ $t('components.part_card.fixed_top') }}</span>
          <span v-if="item.sid > 0" class="lv-icon lv-0">
            {{ $t('tags.collection') }}
          </span>
          <!-- <span class="title">{{ item.title }}</span> -->
          <span class="title" v-html="item.title"></span>
        </a>
        <div class="module-detail">
          <a :href="$i18n.path(`profile/${item.uid}`)" target="_blank" class="user-zone">
            <div class="user-avatar">
              <el-image :src="item.avatar" lazy></el-image>
            </div>
            <div class="user-name" :title="item.author">{{ item.author }}</div>
          </a>
          <div v-if="!item.is_top" class="create-time">{{ item.last_time | date2short }}</div>
          <div class="page-view">
            <span class="icon-views">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 730.49 426.86">
                <path
                  d="M400,577.54c-94.2,0-170.85-76.64-170.85-170.85S305.82,235.84,400,235.84s170.85,76.64,170.85,170.85S494.22,577.54,400,577.54Zm0-303.06c-72.89,0-132.2,59.31-132.2,132.21s59.31,132.2,132.2,132.2,132.21-59.31,132.21-132.2S472.92,274.48,400,274.48Z"
                  transform="translate(-34.78 -193.26)"
                />
                <path
                  d="M408.76,620.11c-199.46,0-318-118.77-359.76-169.84h0a62.65,62.65,0,0,1-1.08-78.12c41.42-53.8,158.53-178.88,351.35-178.88,194.14,0,311.45,126.31,352.89,180.62a62.78,62.78,0,0,1,1.34,75C716.65,500.36,609.31,620.11,408.76,620.11ZM78.93,425.81c38.23,46.81,146.92,155.66,329.83,155.66,182.61,0,279.94-108.46,313.3-155.1a24.33,24.33,0,0,0-.62-29.06c-38-49.73-145.3-165.41-322.17-165.41-175.64,0-282.79,114.55-320.72,163.8a24.16,24.16,0,0,0,.38,30.11Z"
                  transform="translate(-34.78 -193.26)"
                />
              </svg>
            </span>
            {{ item.hits }}
          </div>
        </div>
      </div>
      <div class="module-comment flex ai-center jc-center flex-cols">
        <div class="comment-count">{{ item.comments }}</div>
        <div class="comment-text">{{ $t('components.part_card.comments') }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    items: {
      type: Array,
      default: () => []
    }
  }
};
</script>
<style lang="scss" scoped>
.module-item {
  margin-top: 20px;
  width: 100%;
  border-bottom: 1px solid $gray-white;
  color: $dark;
  .module-title {
    color: $dark;
    font-size: 15px;
    text-decoration: none;
    .title {
      line-height: 1.5;
    }
  }
  .module-detail {
    position: relative;
    z-index: 1;
    margin: 15px 0;
    color: $gray;
    font-size: 13px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .user-zone {
      display: flex;
      align-items: center;
      text-decoration: none;
      .user-avatar {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        overflow: hidden;
        img {
          width: 100%;
        }
      }
      .user-name {
        color: $gray;
        margin-left: 5px;
      }
    }
    .create-time {
      margin-left: 30px;
    }
    .page-view {
      margin-left: 30px;
      .icon-views {
        svg {
          width: 14px;
          height: 10px;
          fill: $gray;
        }
      }
    }
  }
  .module-comment {
    width: 50px;
    .comment-count {
      color: $primary;
      font-size: 20px;
      text-align: center;
    }
    .comment-text {
      margin-top: 10px;
      font-size: 13px;
      color: $gray;
      text-align: center;
    }
  }
}
</style>

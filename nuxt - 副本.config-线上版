/* eslint-disable */
export default {
  mode: 'universal',
  /*
   ** Headers of the page
   */
  head: {
    title: '輕之國度-專註分享的NACG社群',
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      {
        hid: 'description',
        name: 'description',
        content: '輕之國度是專註于分享的NACG社群，这里有用户分享的最新的NACG资源，有很好的社群与创作氛围'
      },
      {
        hid: 'keywords',
        name: 'keywords',
        content: '轻小说,轻之国度,轻小说原创,轻小说阅读,轻小说交流,轻国,LK,LK社群，ANIME,动漫,动漫音乐,游戏,动画,番组,新番,手机游戏,网络游戏,单机游戏'
      }
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      { rel: 'stylesheet', href: '/sceditor/minified/themes/square.min.css' }
    ],
    script: [
      { src: 'https://www.googletagmanager.com/gtag/js?id=G-1CBCNG3QX7' },
      { src: 'https://www.googletagmanager.com/gtag/js?id=G-WRYWE6NM3Z' },//new
      { src: '/js/analytics.js' },
      { src: '/sceditor/js/sceditor.js' },
      { src: '/sceditor/minified/formats/bbcode.js' },
      { src: '/sceditor/js/myicons.js' },
      { src: '/sceditor/languages/cn.js' },
      { src: 'https://www.recaptcha.net/recaptcha/api.js?render=explicit&hl=zh-CN' },
      { src: 'https://s9.cnzz.com/z_stat.php?id=1279087353&web_id=1279087353', defer: true, body: true },
      // { src: 'https://cdn.jsdelivr.net/npm/webtorrent@latest/webtorrent.min.js', body: true }
    ]
  },
  /*
   ** Customize the progress-bar color
   */
  loading: { color: '#39d7d9' },
  /*
   ** Global CSS
   */
  css: [
    // 重置浏览器样式
    '~/assets/css/reset.css',

    // 全局工具样式
    '~/assets/scss/global.scss'

    // 动画库 animate.css
    // '~/assets/css/animate.min.css'
  ],

  // 注入全局 scss 变量和 mixin
  styleResources: {
    scss: '~/assets/scss/variables.scss'
  },

  /*
   ** Plugins to load before mounting the App
   */
  plugins: [
    '~/plugins/i18n.js',
    '~/plugins/element-ui.js',
    '~/plugins/apiParamsGenerator.js',
    '~/plugins/axios.js',
    '~/plugins/directives.js',
    '~/plugins/filters.js',
    { src: '~plugins/swiper.js', ssr: false },
    { src: '~/plugins/utils.js', ssr: false },
    { src: '~/plugins/localStorage.js', ssr: false },
    { src: '~/plugins/vue-cropper.js', ssr: false },
    { src: '~plugins/ga.js', mode: 'client' },
    { src: '~/plugins/zhi.js', ssr: false },
    { src: '~/plugins/mindLocalStorage.js', ssr: false },
  ],

  /*
   ** Nuxt.js dev-modules
   */
  buildModules: [
    // Doc: https://github.com/nuxt-community/eslint-module
    '@nuxtjs/eslint-module'
  ],
  /*
   ** Nuxt.js modules
   */
  modules: ['@nuxtjs/axios', '@nuxtjs/proxy', '@nuxtjs/style-resources', '@nuxtjs/dotenv'],
  axios: {
    proxy: true,
    credentials: false
  },
  // proxy: {
  //   '/apis': {
  //     // target: 'https://api-dev-lvoq4mx4bnr5.lightnovel.us',
  //     // target: 'http://localhost:2333',   // nuxt地址
  //     target: 'http://**************:801/', // 线上地址
  //     // target: process.env.VUE_APP_BASE_URL, // proxy 地址
  //     secure: false,
  //     changeOrigin: true,
  //     pathRewrite: {
  //       '^/apis': '/'
  //     }
  //   }
  // },
  /*
   ** Build configuration
   */
  build: {
    transpile: [/^element-ui/],
    babel: {
      // presets: [['es2015', { modules: false }]],
      plugins: [
        [
          'component',
          {
            libraryName: 'element-ui',
            styleLibraryName: 'theme-chalk'
          }
        ]
      ]
    },
    /*
     ** You can extend webpack config here
     */
    extend(config, ctx) { }
  },
  router: {
    middleware: 'i18n'
    // linkExactActiveClass: 'exact-active-link'
  },

  // 配置nuxt服务端代理请求中间件
  serverMiddleware: ['~/server/proxy.js'],

  // 修改服务端运行的端口: https://zh.nuxtjs.org/faq/host-port/
  server: {
    port: 3001
  }
};

<template>
  <div class="publish_mgr_container">
    <TitleTabbar ref="tabs" :title="$t('publish_mgr.publish_mgr')" all-count="123" book-count="435" :show-more="false" @tab-change="tabChange">
      <!-- 内容区域: 全部 -->
      <template v-slot:articles>
        <div v-if="publish_article_articles.length">
          <div v-for="item in publish_article_articles" :key="item.aid" class="collect-item">
            <CollectionItem
              :show-edit-btn="true"
              :banner="item.cover"
              :title="item.title"
              :date="item.time | date2short"
              :author="item.group_name"
              :views="item.hits"
              :comments="item.comments"
              :coins="item.coins"
              :collections="item.favorites"
              :shares="item.shares"
              :zans="item.likes"
              :gid="item.parent_gid"
              @cover-click="toPage(`/detail/${item.aid}`)"
              @cover-click2="toPage(`/themereply/${item.aid}`)"
              @delete-btn-click="deleteArticleConfirm(item.aid)"
              @edit-btn-click="toWrite(item.aid)"
            />
          </div>
          <div class="pagination">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="articlesLoading"
              :hide-on-single-page="true"
              :current-page="publish_article_articles_paginate.cur"
              :total="publish_article_articles_paginate.count"
              :page-size="publish_article_articles_paginate.size"
              @current-change="articlesChangePage"
            />
          </div>
        </div>
        <!-- 如果没有帖子显示: 还没有发表过帖子, 立即发帖 -->
        <!-- loading books -->
        <Loading v-if="articlesLoading && publish_article_articles.length === 0" />
        <div v-if="!articlesLoading && publish_article_articles.length === 0" class="flex ai-center jc-center fs-sm text-gray" style="height: 300px">
          <span>{{ $t('settings.not_published_and') }}</span>
          <nuxt-link target="_blank" class="btn btn-sm tdn flex ai-center jc-center btn-primary mar-left-5" :to="$i18n.path(`write`)">
            <span>{{ $t('settings.publish') }}</span>
          </nuxt-link>
        </div>
      </template>

      <!-- 内容区域: 图书 -->
      <template v-slot:books>
        <div v-show="publish_article_books.length">
          <div v-for="(item, key) in publish_article_books" :key="key" class="collect-item">
            <CollectionItem
              :banner="item.cover"
              :title="item.title"
              :date="item.time | date2short"
              :author="item.group_name"
              :views="item.hits"
              :comments="item.comments"
              :coins="item.coins"
              :collections="item.favorites"
              :shares="item.shares"
              :gid="item.parent_gid"
              :zans="item.likes"
              mode="vertical"
              :show-edit-btn="true"
              @cover-click="toPage(`/detail/${item.aid}`)"
              @cover-click2="toPage(`/themereply/${item.aid}`)"
              @edit-btn-click="toWrite(item.aid)"
              @delete-btn-click="deleteArticleConfirm(item.aid, true)"
            />
          </div>

          <div class="pagination">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="bookLoading"
              :hide-on-single-page="true"
              :current-page="publish_article_books_paginate.cur"
              :total="publish_article_books_paginate.count"
              :page-size="publish_article_books_paginate.size"
              @current-change="booksChangePage"
            />
          </div>
        </div>

        <!-- 如果没有数据显示: 暂无数据 -->
        <div v-if="publish_article_books.length === 0" class="empty-tips flex ai-center jc-center fs-sm text-gray">
          {{ $t('settings.no_data') }}
        </div>
      </template>
    </TitleTabbar>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import TitleTabbar from '@/components/TitleTabbar.vue';
import CollectionItem from '@/components/CollectionItem.vue';
import Loading from '@/components/Loading.vue';

export default {
  layout: 'settings',
  middleware: ['auth'],
  components: {
    TitleTabbar,
    CollectionItem,
    Loading
  },

  async fetch({ store, params }) {
    const uid = params.id;
    // await store.dispatch('userPublish/getPublishArticleArticles', { uid });
    await store.dispatch('userPublish/getPublishArticleBooks', { uid });
  },

  asyncData({ params }) {
    return {
      uid: params.id
    };
  },

  data: () => ({
    uid: 0,
    tabIndex: 0, // 展示的 tab
    bookLoading: false,
    articlesLoading: false
  }),

  computed: {
    ...mapState('userPublish', [
      'publish_article_articles',
      'publish_article_articles_paginate',
      'publish_article_books',
      'publish_article_books_paginate'
    ])
  },

  mounted() {
    if (process.browser) {
      this.$nextTick(() => this.initTabView());
    }
  },

  methods: {
    // tab 位置
    initTabView() {
      const { tab } = this.$route.query;
      tab && this.$refs.tabs.changeTabbar(Number(tab));
    },

    // 修改文章
    toWrite(id) {
      this.toPage(`/write/${id}`, 'push', {
        tab: this.tabIndex
      });
    },

    // 文章分页
    articlesChangePage(page) {
      this.articlesLoading = true;
      this.$store
        .dispatch('userPublish/getPublishArticleArticles', {
          uid: this.uid,
          page
        })
        .finally(() => {
          this.articlesLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // 图书分页
    booksChangePage(page) {
      this.bookLoading = true;
      this.$store
        .dispatch('userPublish/getPublishArticleBooks', {
          uid: this.uid,
          page
        })
        .finally(() => {
          this.bookLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // tabbar 切换效果
    tabChange(i) {
      this.tabIndex = i;
      if (i === 1 && this.publish_article_articles.length === 0) {
        this.articlesChangePage(1);
      }
    },

    // 删除文章
    deleteArticleConfirm(aid, isBook = false) {
      const payload = {
        fid: aid,
        _class: 1,
        dataInfo: {
          key: 'aid',
          id: aid,
          data: 'publish_article_articles'
        }
      };
      if (isBook) {
        payload.dataInfo.data = 'publish_article_books';
      }
      this.$confirm(this.$t('tips.are_you_sure'), this.$t('components.type_warn'), {
        confirmButtonText: this.$t('components.btn_confirm'),
        cancelButtonText: this.$t('components.btn_cancel'),
        type: 'warning'
      })
        .then(() => this.deleteArticle(payload))
        .catch(Function.prototype);
    },

    // 执行删除
    deleteArticle(payload) {
      this.$store
        .dispatch('userPublish/deletePublish', payload)
        .then(() => {
          this.$message.success(this.$t('tips.delete_success'));
        })
        .catch(() => {
          this.$message.error(this.$t('tips.delete_fail'));
        });
    }
  },

  head() {
    const title = this.$t('publish_mgr.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
@import '@/assets/scss/collect.scss';
</style>

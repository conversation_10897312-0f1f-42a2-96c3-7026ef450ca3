/* eslint-disable no-async-promise-executor */

export const state = () => ({
  articles: [], // 文章
  publish_article_articles: [],
  publish_article_books: [], // 发布管理文章图书
  publish_series_books: [], // 发布管理合集图书

  history: [], // 历史
  history_article_books: [], // 浏览历史文章图书
  history_series_books: [], // 浏览历史合集图书

  collects: [], // 收藏
  collects_article_books: [], // 我的收藏文章图书
  collects_series_books: [] // 我的收藏合集图书
});

export const actions = {
  // 关注用户
  async followUser(vuex, { uid, act }) {
    const res = await this.$axios.$post('/api/user/follow', {
      uid,
      act
    });
    if (res.code === 0) {
      this.dispatch('login/refreshUserinfo');
      return Promise.resolve();
    } else {
      return Promise.reject(res);
    }
  },

  // 增加收藏 fid: 文章或合集id  _class: 是否是文章(1文章 0合集)
  async addCollection({ commit }, { fid, _class }) {
    const res = await this.$axios.$post('/api/history/add-collection', {
      fid,
      class: _class
    });
    return res.code === 0 ? Promise.resolve() : Promise.reject(res);
  },

  // 删除收藏 fid: 文章或合集id  _class: 是否是文章(1文章 2合集)
  deleteCollection({ commit }, { fid, _class }) {
    return new Promise(async (resolve, reject) => {
      const res = await this.$axios.$post('/api/history/del-collection', {
        fid,
        class: _class // 是否是文章(1文章 2合集)
      });
      if (res.code !== 0) {
        return reject(res);
      }
      commit('deleteCollection', fid);
      return resolve();
    });
  },

  // 删除历史
  deleteHistory({ commit }, { aid }) {
    return new Promise(async (resolve, reject) => {
      const res = await this.$axios.$post('/api/history/del-history', {
        fid: aid,
        class: 1 // 是否是文章(1文章 0合集)
      });
      if (res.code !== 0) {
        return reject(res);
      }
      commit('deleteHistory', aid);
      return resolve();
    });
  }
};

export const mutations = {
  // 文章
  getArticles(state, payload) {
    state.articles = payload;
  },

  // 历史
  getHistory(state, payload) {
    state.history = payload;
  },

  // 收藏
  getCollects(state, payload) {
    state.collects = payload;
  },

  // 删除文章
  deleteArticle(state, id) {
    state.articles = state.articles.filter((item) => item.aid !== id);
  },

  // 删除收藏
  deleteCollection(state, id) {
    state.collects = state.collects.filter((item) => item.fid !== id);
  },

  // 删除历史
  deleteHistory(state, id) {
    state.history = state.history.filter((item) => item.fid !== id);
  }
};

export const getters = {
  // 文章(前10条数据)
  articles_10(state) {
    if (state.articles.length) {
      return state.articles.slice(0, 10);
    } else {
      return [];
    }
  },

  // 历史(前10条数据)
  history_10(state) {
    if (state.history.length) {
      return state.history.slice(0, 10);
    } else {
      return [];
    }
  },

  // 收藏(前10条数据)
  collects_10(state) {
    if (state.collects.length) {
      return state.collects.slice(0, 10);
    } else {
      return [];
    }
  },

  // 获取发布文章数据
  publishArticleBooks(state) {
    const books = state.publish_article_books;
    return books.length ? books : [];
  },

  // 发布管理-集合-books
  publishSeriesBooks() {
    const books = state.publish_article_books;
    return books.length ? books : [];
  },

  // 收藏-文章-books
  collectionArticleBooks() {
    const books = state.collects_article_books;
    return books.length ? books : [];
  },

  // 收藏-合集-books
  collectionSeriesBooks() {
    const books = state.collects_series_books;
    return books.length ? books : [];
  },

  // 获取发布管理-文章-books
  publishBooks10(state) {
    const books = state.publish_article_books;
    return books.length ? books.slice(0, 10) : [];
  },

  // 获取历史记录-文章-books
  historyBooks10(state) {
    const books = state.history_article_books;
    return books.length ? books.slice(0, 10) : [];
  },

  // 获取收藏数据
  collectionBooks10(state) {
    const books = state.collects_article_books;
    return books.length ? books.slice(0, 10) : [];
  }
};

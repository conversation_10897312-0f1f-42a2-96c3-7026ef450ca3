<template>
  <client-only>
    <swiper class="emoji-panel-container" :options="swiperOption">
      <swiper-slide v-for="(emoji, key) in emojis" :key="key" class="swiper-slide-item">
        <ul v-if="emoji.type === 1 || emoji.type === 2" class="emoji-panel-container-item">
          <!-- 表情和颜文字 -->
          <li v-for="(item, index) in emoji.items" :key="index" class="emoji-item hover" @click="inputEmoji(item)">
            {{ item.url }}
          </li>
        </ul>

        <ul v-else class="emoji-panel-container-item">
          <!-- 图片表情 -->
          <li v-for="(item, index) in emoji.items" :key="index" class="emoji-item hover emoji-item-img" @click="inputEmoji(item)">
            <img :src="item.url" class="emoji-img" />
          </li>
        </ul>
      </swiper-slide>
      <div slot="pagination" class="swiper-pagination swiper-pagination-bullets">
        <!-- tabbar -->
      </div>
    </swiper>
  </client-only>
</template>

<script>
import { mapState } from 'vuex';
// import { Swiper, SwiperSlide } from 'vue-awesome-swiper';
import 'swiper/css/swiper.css';
export default {
  components: {
    // Swiper,
    // SwiperSlide
  },
  data() {
    return {
      swiperOption: {
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          renderBullet: (index, className) => {
            if (this.emojis.length) {
              const { icon } = this.emojis[index];
              return `<div class="${className} tabbar"><img src="${icon}" class="emoji-icon"></div>`;
            }
          }
        }
      }
    };
  },
  computed: {
    ...mapState('emoji', ['emojis'])
  },
  // created() {
  //   this.$store.dispatch('emoji/getEmojis');
  // },
  methods: {
    inputEmoji(item) {
      this.$emit('input-emoji', item);
    }
  }
};
</script>

<style lang="scss" scoped>
.emoji-panel-container {
  height: 100%;
  width: 100%;
  $tabbarHeight: 35px;
  .swiper-slide-item {
    overflow-y: auto !important;
  }
  .emoji-panel-container-item {
    // overflow-y: scroll !important;
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    background-color: $white;
    padding-bottom: $tabbarHeight;
    box-sizing: border-box;
    .emoji-item {
      padding: 10px 15px;
      &.emoji-item-img {
        margin: 5px 10px;
        padding: 0 !important;
        max-width: 50px;
        .emoji-img {
          width: 100%;
        }
      }
    }
  }
  .swiper-pagination {
    background: $gray-white;
    position: absolute;
    bottom: 0;
    height: $tabbarHeight;
    display: flex;
    align-items: center;
    /deep/ .tabbar {
      border-radius: 0;
      width: 50px;
      height: 100%;
      margin: 0;
      background: $white;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      .emoji-icon {
        max-width: 20px;
      }
    }
  }
}
</style>

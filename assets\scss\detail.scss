// 修复编辑器样式
.article-content {
  em {
    font-style: italic !important;
  }
  b {
    font-weight: bold !important;
  }
  pre {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    *white-space: normal !important;
  }
  // 实现文本对齐的功能
  .inline-align-left,
  .inline-align-right,
  .inline-align-center {
    width: 100%;
    display: inline-block;
    // flex-direction: column;
  }
  .inline-align-left {
    text-align: left;
    // justify-content: flex-start;
    // align-items: flex-start;
  }
  .inline-align-right {
    text-align: right;
    // justify-content: flex-end;
    // align-items: flex-end;
  }
  .inline-align-center {
    text-align: center;
    // justify-content: center;
    // align-items: center;
  }
}
.sidebar-buttons {
  width: 50px;
  position: fixed;
  right: 20px;
  bottom: 30%;
  .btn-item {
    border: 1px solid $gray-white;
    width: 100%;
    height: 50px;
    margin-bottom: 10px;
    padding: 15px;
    a {
      width: 100%;
      height: 100%;
    }
    &:hover {
      background: $primary;
      .svg-icon {
        fill: $white;
      }
    }
    .svg-icon {
      fill: $dark;
    }
  }
}

// 没有权限
.access-permission-denied-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 999999999;
  .btn-close {
    width: 150px;
    height: 40px;
    border-radius: 40px;
    margin: 0 auto;
  }
  .img-box {
    width: 210px;
    height: 257px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .img-box1 {
    width: 550px;
    height: 238px;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
    }
  }
  ul {
    margin-top: -10px;
    margin-left: 55px;
    letter-spacing: 3px;
    font-size: 22px;
    list-style: disc;
    li {
      margin-top: 25px;
    }
  }
}

.detail-contents {
  margin-top: 40px;

  // 左边内容部分
  .left-contents {
    width: 880px;
    .article {
      border-bottom: 1px solid $gray-white;
      &-title {
        // 文章标题
        font-size: 30px;
        font-weight: 400;
        color: $dark;
        line-height: 46px;
      }
      &-infos {
        // 文章信息
        padding-top: 25px;
        padding-bottom: 50px;
        &-item {
          margin-right: 20px;
        }
        .counter-item {
          display: inline-flex;
          align-items: center;
          margin-right: 20px;
          position: relative;
          .svg-icon {
            margin-right: 5px;
            width: 15px;
            fill: $gray;
            // sizes
            &.coins,
            &.collections,
            &.share,
            &.zan {
              width: 13px;
            }

            // fill colors
            &.coins {
              fill: #fadd52;
            }
            &.collections {
              fill: #b6ed6f;
            }
            &.share {
              fill: #69cff9;
            }
            &.zan {
              fill: #ffb236;
            }
          }
          .coins-container {
            position: absolute;
            top: 130%;
            left: -50%;
            width: 150px;
            background: $white;
            box-shadow: 0px 0px 15px 0px rgba(1, 1, 1, 0.1);
            border: 1px solid $gray-white;
            border-radius: 5px;
            padding: 20px 15px;
            &::after {
              left: 25%;
            }
            .give-coin-item {
              filter: grayscale(100%);
              &:hover {
                filter: grayscale(0%);
              }
            }
          }
          .other-coins-container {
            position: absolute;
            top: 130%;
            left: -447%;
            width: 354px;
            background: $white;
            box-shadow: 0px 0px 15px 0px rgba(1, 1, 1, 0.1);
            border: 1px solid $gray-white;
            border-radius: 5px;
            padding: 20px 15px;
            &::after {
              left: 25%;
            }
            .give-coin-item {
              filter: grayscale(100%);
              &:hover {
                filter: grayscale(0%);
              }
            }
          }
        }
        .reply-link {
          text-decoration: none;
          float: right;
        }
      }

      // 文章内容
      &-content {
        font-size: 18px;
        line-height: 31px;
        word-wrap: break-word;
        word-break: normal;
        &-lock {
          width: 100%;
          padding: 30px 0;
          .svg-icon {
            height: 15px;
            fill: $primary;
          }
          .unlock {
            height: 35px;
            line-height: 35px;
            padding: 0 20px;
            border-radius: 35px;
          }
        }
      }
      &-votes {
        &-title {
          text-align: center;
          padding: 30px;
        }

        // 投票选项
        .vote-options {
          height: 80px;
          box-sizing: border-box;
          padding: 15px 0;
          .options-item-radio,
          .options-item-counter {
            width: 60px;
          }

          // 左边选择按钮
          .options-item-radio {
            .outter-radio {
              width: 30px;
              height: 30px;
              box-sizing: border-box;
              padding: 5px;
              overflow: hidden;
              border-radius: 50%;
              background-color: rgba(178, 178, 178, 0.2);
              &.active {
                background-color: rgba(57, 215, 217, 0.2);
                .radio {
                  background-color: $primary;
                }
              }

              .radio {
                width: 100%;
                height: 100%;
                overflow: hidden;
                border-radius: 50%;
              }
            }
          }

          // 中间进度条
          .options-item-progress {
            .progress-title {
              width: 100%;
              .title-name {
                display: inline-block;
                margin-right: 15px;
              }
            }
            .progress-bar {
              max-height: 20px;
              width: 100%;
              background: $gray-white;
              border-radius: 5px;
              overflow: hidden;
              position: relative;
              .progress-bar-progress {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                background: $primary;
              }
            }
          }
        }

        // 投票按钮
        .article-votes-submit {
          padding: 30px 0;
          .article-votes-btn {
            width: 130px;
            height: 35px;
            line-height: 35px;
            border-radius: 35px;
          }
        }
      }

      // 附加文件 & 连接
      &-extra-file {
        padding: 50px 0;
        &-item {
          margin-bottom: 20px;
          .link {
            margin-left: 15px;
            text-decoration: underline;
          }
        }
      }

      // 收藏 投币 点赞 分享
      &-actions {
        padding-bottom: 30px;
        .article-actions-left {
          width: 300px;
          .article-actions-left-item {
            user-select: none;
            color: $gray;
            .num {
              margin-left: 5px;
            }

            // 投币
            &.action-coin {
              position: relative;
              top: 0;
              left: 0;
              .coins-container {
                position: absolute;
                top: 130%;
                left: -50%;
                width: 150px;
                background: $white;
                box-shadow: 0px 0px 15px 0px rgba(1, 1, 1, 0.1);
                border: 1px solid $gray-white;
                border-radius: 5px;
                padding: 20px 15px;
                &::after {
                  left: 25%;
                }
                .give-coin-item {
                  filter: grayscale(100%);
                  &:hover {
                    filter: grayscale(0%);
                  }
                }
              }
              .tips {
                padding-top: 15px;
              }
            }

            .svg-icon {
              width: 25px;
              fill: $gray;
              &.active {
                fill: $primary !important;
              }
            }

            &.active {
              color: $primary;
              .svg-icon {
                fill: $primary !important;
              }
            }
          }
        }
        // 分享
        &-right {
          flex: 1;
          background: red;
          // width: 150px;
          // .share-platforms {
          //   margin-left: 10px;
          //   .share-platforms-item {
          //     width: 20px;
          //     height: 20px;
          //     :hover {
          //       cursor: pointer;
          //     }
          //     img {
          //       width: 100%;
          //     }
          //     // background: red;
          //     margin-right: 5px;
          //   }
          // }
        }
      }
    }

    // 评论
    #comments {
      .top-tips {
        padding: 30px 0;
      }
      // 评论区域
      .comment-list-container {
        .comment-list-item {
          width: 100%;
          padding: 15px 0;
          position: relative;
          .have-hotline {
            width: 90.9%;
            position: absolute;
            border-bottom: 1px solid #eeeeee;
            bottom: 0;
            text-align: center;
            span {
              position: absolute;
              bottom: -7px;
              left: 50%;
              z-index: 999;
              color: #b2b2b2;
              width: 120px;
              background-color: white;
              transform: translateX(-50%);
            }
          }
          .no-hotline {
            width: 90.9%;
            position: absolute;
            border-bottom: 1px solid #eeeeee;
            bottom: 0;
            text-align: center;
            span {
              display: none;
            }
          }
          .comment-list-item-content {
            width: 800px;
            // border-bottom: 1px solid $gray-white;
            padding: 15px 0;
            // 评论
            .content-item-pinglun {
              .content-item-reply {
                // 回复
                padding: 20px 10px 1px 10px;
                background-color: #fafafa;
                word-break: break-word;
              }
            }
            .comment-reply-input {
              padding: 15px 0;
            }
          }
        }
      }
    }
  }

  // ------------ 右边 ------------
  .scroll {
    transform: translateY(-72px);
  }
  .right-recommend {
    width: 260px;
    margin-left: 920px;
    position: fixed;
    transition: all 0.1s;
    $followBtnHeight: 30px;
    .author {
      border: 1px solid $gray-white;
      padding-bottom: 35px;
      padding-top: 15px;
      position: relative;
      background-color: $white-fa;
      &-avatar {
        width: 60px;
        height: 60px;
        overflow: hidden;
        border-radius: 50%;
      }
      &-name {
        padding-top: 20px;
        padding-bottom: 10px;
      }
      &-signature {
        text-align: center;
        padding-bottom: 20px;
      }
      &-medals {
        margin-bottom: 14px;
        padding-left: 5px;
        .item-medal-image {
          width: 30px;
        }
      }
      &-infos {
        width: 100%;
        &-item {
          flex: 1;
          display: inline-flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          &-text {
            display: inline-block;
            padding-top: 15px;
            color: $gray;
          }
        }
      }
      &-follow-btn {
        width: 100px;
        line-height: $followBtnHeight;
        border-radius: $followBtnHeight;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateY(50%) translateX(-50%);
      }
    }

    // 其他推荐
    .other-recommends {
      padding-top: 15 + ($followBtnHeight / 2);
      clear: both;
      &-title {
        font-size: 18px;
        .icons-text-img-container {
          height: 100%;
          // width: 50px;
          .svg-icon {
            fill: $gray;
            &.active {
              fill: $primary !important;
            }
          }
          .text-icon {
            height: 16px;
          }
          .img-icon {
            height: 16px;
          }
        }
        // 上下帖按钮
        .btn-box {
          button {
            width: 65px;
            height: 20px;
            border: 1px solid rgba(58, 216, 218, 1);
            border-radius: 5px;
            cursor: pointer;
            outline: none;
            font-size: 13px;
            background-color: white;
            color: rgba(58, 216, 218, 1);
            line-height: 20px;
          }
        }
      }
      &-article {
        font-size: 14px;
        font-weight: normal;
        padding-top: 10px;
        &-item {
          margin-bottom: 5px;
        }
        .show-more-btn {
          background: #fafafa;
          text-align: center;
          height: 35px;
          line-height: 35px;
        }
      }
    }
    // 插入广告
    .poster {
      background: $gray-f7;
      margin-top: 25px;
      .poster-box {
        cursor: pointer;
        width: 260px;
        height: 145px;
        margin-bottom: 10px;
        .el-image {
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
      }
    }
  }
}

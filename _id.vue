<template>
  <div class="psd-container">
    <div class="psd-title">{{ $t('forget_psw.find_psw') }}</div>
    <div class="psd-mid">
      <span :class="{ active: isActive === 0 }"> {{ $t('forget_psw.sure_user') }}</span>
      <span :class="{ active: isActive === 1 }">{{ $t('forget_psw.reset_psw') }}</span>
      <span :class="{ active: isActive === 2 }">{{ $t('forget_psw.success_psw') }}</span>
    </div>
    <div v-if="isActive === 0" class="psd-input">
      <p>
        <span v-if="seconds">{{ $t('forget_psw.sended_email') }}</span>
        <input v-model="userEmail" type="email" class="input-control" autocomplete="off" maxlength="30" placeholder="请输入绑定邮箱" />
      </p>
      <!-- reCAPTCHA V3 自动验证，无需显示UI -->
      <!-- <div class="input-item">
        <div ref="google-recaptcha-view-container"></div>
      </div> -->
      <button v-if="!seconds" class="btn" @click="sendCode">{{ $t('forget_psw.send_email') }}</button>
      <button v-else class="btn btn-stop" disabled="false">{{ $t('forget_psw.reply_send') }}{{ seconds }}s</button>
    </div>
    <div v-else-if="isActive === 1" class="psd-input">
      <p>
        <span v-show="isLike">{{ $t('forget_psw.two_no_like') }}</span>
        <input
          v-model="newPsw"
          type="password"
          class="input-control"
          autocomplete="new-password"
          maxlength="30"
          :placeholder="$t('forget_psw.new_psw')"
        />
        <input
          v-model="copyNewPsw"
          type="password"
          class="input-control"
          autocomplete="new-password"
          maxlength="30"
          :placeholder="$t('forget_psw.copy_psw')"
        />
      </p>
      <button class="btn" @click="surePsw">{{ $t('forget_psw.sure') }}</button>
    </div>
    <div v-else-if="isActive === 2" class="psd-input psd-success">
      <div class="img-box imgWrap">
        <img src="@/assets/images/chongzhichenggong.png" alt="" />
      </div>
      <button class="btn" @click="toPage('')">{{ $t('forget_psw.to_login') }}</button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
// import Loading from '@/components/Loading.vue';
export default {
  layout: 'profile',
  // 请求数据
  asyncData({ params, query }) {
    const data = {};
    if (query && query.code) {
      data.isActive = 1;
      data.code = query.code;
    }
    return data;
  },

  data: () => ({
    code: '', // 邮箱跳转过来的验证码
    userEmail: '', // 邮箱地址
    seconds: 0, // 发送验证码多少秒后才可以发送
    isSend: false, // 是否发送成功
    isActive: 0, // 忘记密码三个步骤,0:发送验证,1:重置密码，2:重置成功
    newPsw: null, // 新密码
    copyNewPsw: null, // 重复新密码
    isLike: false, // 两次密码是否一致
    notRobot: false, // 是否进行了机器人验证
    GRecaptchaResponse: null // google recaptcha 返回的结果
  }),
  computed: {
    ...mapState('login', ['isLogin', 'loginUser'])
  },
  mounted() {
    // reCAPTCHA V3 不需要初始化DOM
    // if (this.isActive === 0) {
    //   this.$nextTick(this.initGoogleRecaptchaWrapper);
    // }
  },
  methods: {
    // 验证 google recaptcha v3
    checkGRecaptcha(callback = null) {
      try {
        window.grecaptcha.ready(() => {
          const sitekey = '6LdzVLgrAAAAANg28qWlEX4xUbz7PVF9cUE1vZq_';
          const action = 'user/reset_pw';
          window.grecaptcha
            .execute(sitekey, { action })
            .then((token) => {
              callback && typeof callback === 'function' && callback(action, token);
            })
            .catch((e) => {
              this.$message.error(this.$t('tips.google_recaptcha_check_fail'));
              console.log(e);
            });
        });
      } catch (e) {
        this.$message.error(this.$t('tips.google_recaptcha_check_fail'));
        console.log(e);
      }
    },
    // 开始倒计时
    startTimer() {
      this.seconds = 60;
      this.timer = setInterval(() => {
        this.seconds--;
        this.seconds === 0 && clearInterval(this.timer);
      }, 1000);
    },
    // 发送验证码
    async sendCode() {
      if (!this.seconds) {
        // eslint-disable-next-line camelcase
        const reg = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
        if (!this.userEmail || !reg.test(this.userEmail)) {
          this.$message.error(this.$t('tips._err_email'));
          return;
        }

        // 使用 reCAPTCHA V3 进行验证
        this.checkGRecaptcha(async (action, token) => {
          this.isSend = true;
          this.startTimer();
          const res = await this.$axios.$post('/api/captcha/email', {
            email: this.userEmail,
            type: 101,
            recaptcha: { action, token }
          });
          if (res.code === 0) {
            this.$message.success(this.$t('tips.send_success'));
          } else {
            this.$message.error(this.$t('tips.send_fail'));
          }
        });
      }
    },
    // 确认重置密码按钮
    async surePsw() {
      /* eslint-disable */
      const reg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[\da-zA-Z~!@#$%\^&*()_+`\-={}\[\];':",./<>?\\|]{8,16}$/;
      if (!reg.test(this.newPsw)) {
        this.$message.error(this.$t('nav.password_fmt_err'));
        return;
      }
      if (this.newPsw !== this.copyNewPsw) {
        this.isLike = true;
        return;
      }
      const res = await this.$axios.$post('/api/user/reset-password', {
        code: this.code,
        new_password: this.newPsw
      });
      if (res.code === 0) {
        this.isActive = 2;
        this.$message.success(this.$t('tips.send_success'));
      } else {
        this.$message.error(this.$t('tips.send_fail'));
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.psd-container {
  height: 600px;
  background: #ffffff;
  margin-top: 29px;
  padding: 30px 31px;
  .psd-title {
    font-size: 20px;
  }
  .psd-mid {
    text-align: center;
    margin-top: 59px;
    span {
      background: #f3f3f3;
      color: #cccccc;
      border-radius: 50px;
      padding: 7px 16px;
      font-size: 15px;
      &:nth-of-type(2) {
        margin-left: 176px;
        position: relative;
        margin-right: 176px;
        &::after {
          content: '';
          position: absolute;
          width: 180px;
          left: 92px;
          top: 16px;
          height: 2px;
          background: #f3f3f3;
        }
        &::before {
          content: '';
          position: absolute;
          width: 180px;
          right: 92px;
          top: 16px;
          height: 2px;
          background: #f3f3f3;
        }
      }
    }
    .active {
      background: #39d7d9;
      color: white;
    }
  }
  .psd-input {
    margin-top: 137px;
    p {
      text-align: start;
      margin: 0 auto;
      width: 300px;
      span {
        font-size: 15px;
        color: #39d7d9;
        line-height: 35px;
      }
      input {
        height: 38px;
        &:last-of-type {
          margin-top: 10px;
        }
      }
    }
    .img-box {
      width: 180px;
      height: 180px;
      margin: 0 auto;
    }
    .btn {
      display: block;
      margin: 0 auto;
      width: 140px;
      height: 40px;
      border-radius: 40px;
      background: #39d7d9;
      color: #fff;
      font-size: 18px;
      margin-top: 40px;
    }
    .btn-stop {
      background: #f3f3f3;
      color: #bbbbbb;
      cursor: default;
    }
    .input-item {
      max-width: 304px;
      margin: 35px auto 0px;
    }
  }
  .psd-success {
    margin-top: 57px;
  }
}
</style>

.tdn {
  text-decoration: none;
}
.tabbars-container {
  padding: 15px 0;
  .my-tabbar-item {
    padding: 15px;
    .empty-tips {
      height: 100px;
      .btn {
        width: 70px;
        height: 30px;
        border-radius: 30px;
      }
    }
  }
}

.publish_mgr_container {
  padding: 30px 15px;

  // 分页
  .pagination {
    padding: 30px 0;
  }

  // 顶部: 批量管理
  .right-btns {
    float: right;
    &.btn-select-muilt {
      height: 30px;
      width: 80px;
      border-radius: 5px;
      border: 1px solid $gray-white;
    }
  }

  // 每一条文章数据
  .collect-item {
    position: relative;
    padding-top: 30px;
    &:first-of-type {
      padding: 0;
      .check-box {
        top: 0 !important;
      }
    }
    .check-box {
      width: 20px;
      height: 20px;
      background-color: rgba(#000, 0.4);
      position: absolute;
      top: 30px;
      left: 0;
      z-index: 2;
      .check-svg-icon {
        margin-top: 3px;
        fill: #fff;
      }
      &.checked {
        background: $primary;
      }
    }
  }
}

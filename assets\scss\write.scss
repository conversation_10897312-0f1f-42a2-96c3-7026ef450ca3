.write-page {
  padding-top: 20px;
  .cover-img {
    width: 100%;
  }

  // 已经上传的内容图片
  .uploaded-imgs-wrapper {
    padding-bottom: 20px;
    .img-item-wrapper {
      width: 45%;
      padding: 10px;
      border: 1px solid $gray-white;
      margin-bottom: 10px;
      border-radius: 5px;
      position: relative;
      .file-tag {
        border-radius: 0 !important;
        position: absolute;
        top: 0;
        left: 0;
      }
      p {
        height: 100%;
        line-height: 40px;
        padding: 0 15px;
      }
      img {
        width: 40px;
        height: 40px;
      }
      .btn-box {
        position: absolute;
        right: 5px;
        top: 5px;
        width: 20px;
        height: 20px;
        padding: 4px;
        svg {
          height: 100%;
          fill: $gray;
          &:hover {
            fill: $primary;
          }
        }
      }
    }
  }

  /* 图片裁剪插件 */
  .crop-dialog {
    #crop-container {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .crop-box {
        width: 800px;
        height: 500px;
        .scale-btns {
          padding: 20px 0;
          .btn-img-scale {
            width: 100px;
            height: 30px;
          }
        }
      }
      .preview-box {
        border: 1px solid $primary;
        overflow: hidden;
      }
    }
    .dialog-footer {
      .btn {
        width: 100px;
        height: 35px;
        border-radius: 35px;
      }
    }
  }

  @mixin box {
    width: 100%;
    padding: 0 40px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 5px;
  }
  .top-tips {
    height: 50px;
    margin-bottom: 20px;
    .tip {
      border-top: 2px solid $primary;
      width: 80px;
      height: 100%;
    }
    @include box();
  }
  .contents {
    @include box();
    padding-top: 40px;
    padding-bottom: 40px;
    width: 100%;
  }
  .contents,
  .votes-dialog,
  .new-collection-dialog {
    .btn-publish {
      width: 130px;
      height: 35px;
      border-radius: 35px;
      text-align: center;
      margin: 0 auto;
    }
    .item {
      position: relative;
      .sub-title{
        display: flex;
        align-items: center;
        .radio-btn {
          width: 15px;
          height: 15px;
          background: #dfdfdf;
          margin-right: 15px;
          border-radius: 50%;
          cursor: pointer;
          &.checked {
            background: $primary;
          }
          &.checked2 {
            background: $primary;
          }
        }
      }
      .contents-files {
        padding-bottom: 20px;
      }
      padding-bottom: 30px;
      width: 100%;
      box-sizing: border-box;
      .collection-intro {
        height: 60px;
        padding: 10px 15px;
        resize: none;
      }
      .text-counter {
        min-width: 20px;
        font-size: 13px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        &.btn-del {
          .svg-icon {
            height: 13px;
            fill: $gray;
          }
        }
      }
      .require {
        color: #f00;
      }
      .article-title {
        max-width: 500px;
      }
      .sub-title {
        margin-bottom: 15px;
        .new-collection {
          background: none;
        }
      }
      .sub-titleCon{
        display: flex;
        justify-content: space-between;
        .require-right{
          font-size: 15px;
          color: #b2b2b2;
          line-height: 30px;
        }
      }
      .mar-right-20 {
        margin-right: 20px;
      }
      .select {
        width: 250px;
        height: 30px;
        border: 1px solid $gray-white;
        padding: 0 15px;
        border-radius: 5px;
        color: $dark;
        appearance: none;
        background-color: #fff;
        background-image: url(../../../assets/images/down_arrow.svg);
        background-repeat: no-repeat;
        background-size: 12px auto;
        background-position: 95% 55%;
        &.padrig-30 {
          padding-right: 30px !important;
        }
      }
      .select-banner {
        width: 160px;
        height: 100px;
        background: rgba(250, 250, 250, 1);
        border: 1px solid rgba(238, 238, 238, 1);
        border-radius: 5px;
        background-size: 40px 40px;
        overflow: hidden;
        .svg-icon {
          width: 30px;
          fill: #b2b2b2;
        }
        &.vertical {
          width: 160px;
          height: 225px;
        }
        fill: #ccc;
        .cover-img {
          width: 100%;
          object-fit: cover;
        }
      }
    }
    .extra-options,
    .checkboxs {
      .radio-btn {
        width: 15px;
        height: 15px;
        background: #dfdfdf;
        margin-right: 15px;
        border-radius: 50%;
        cursor: pointer;
        &.checked {
          background: $primary;
        }
      }
      .payinfo {
        padding: 15px 0;
        .left {
          width: 140px;
          text-align: right;
        }
        .right {
          padding-left: 15px;
          input {
            width: 250px;
          }
        }
      }
    }
  }
  .video-dialog {
    position: absolute;
    overflow: none;
    text-align: center;
    .three-btn{
      display: flex;
      justify-content: space-around;
      margin-bottom: 10px;
    }
    .active{
      color: $primary;
    }
    .el-dialog {
      right: -160px;
      margin-top: 6.5vh;
      .el-dialog__header {
        display: none;
      }
    }
    .radio-box {
      align-items: baseline;
      justify-content: space-between;
      span {
        margin-right: 5px;
      }
      label {
        cursor: pointer;
        input {
          cursor: pointer;
        }
      }
    }
    label {
      display: block;
      font-weight: 700;
      color: #3c3c3c;
      padding: 4px 0;
      margin-left: 5px;
    }
    .torrent{
      line-height: 30px;
    }
    input {
      margin-bottom: 5px;
    }
    .link-input{
      font-family: Arial,"Helvetica Neue",Helvetica,sans-serif;
      outline: 0;
      padding: 4px;
      border: 1px solid #ccc;
      border-top-color: #888;
      border-radius: 1px;
      background-clip: padding-box;
    }
    .log{
      text-align: start;
      video{
        width: 100%;
        margin-top: 10px;
      }
      audio{
        width: 100%;
        margin-top: 10px;
      }
    }
    .button {
      font-weight: 700;
      color: #444;
      padding: 2px 12px;
      background: padding-box #ececec;
      border: 1px solid #ccc;
      border-radius: 2px;
      cursor: pointer;
      margin: 0.3em 0 0;
    }
  }
}

/* eslint-disable */
import Vue from 'vue';

// 错误码
const errno = {
  '-2': '服务器维护中',
  '-1': '底层未知错误',
  0: 'OK',
  1: '未知错误',
  2: '密码错误',
  3: '参数错误',
  4: '不支持',
  5: '受限',
  6: '权限限制',
  7: '验证码',
  8: '拒绝访问',
  9: '网络错误',
  20: '数据写入错误',
  21: '数据读取错误',
  1000: '自定义错误',
  101: '文件类型错误或与实际类型不符',
  102: '文件大小错误',
  103: '文件上传失败',
  104: '文件移动失败',
  105: '创建目录失败',
  106: '图片二压失败',
  107: '创建缩略图失败',
  108: '图片不存在',
  109: '图片违规',
  110: '图片尺寸超过限制',
  111: '上传文件不存在',
  112: '图片宽高比不符合规范',
  1001: '用户不存在',
  1002: '用户已存在',
  1003: '用户已被绑定',
  1005: '用户无需绑定',
  1006: '昵称已存在',
  1007: '昵称长度有误',
  1008: '异国有重复的手机号，无法使用账号密码登录',
  1009: '签名长度有误',
  1011: '手机已存在',
  1012: '必须输入强密码',
  2001: '商品不存在',
  2002: '商品库存',
  2003: '商品状态',
  2004: '商品单价',
  2005: '商品总价',
  2006: '商品数量',
  2007: '订单扩展属性有误',
  2008: '超出商品可购买数量',
  2101: '生成订单失败',
  2102: '添加订单详情失败',
  2103: '订单不存在',
  2105: '收件人信息有误',
  2106: '订单状态有误',
  2107: '超出支付时间',
  2108: '订单金额有误',
  2201: '文章已支付',
  2202: '文章已免费',
  2203: '文章付费类型错误',
  2301: '用户余额不足',
  2501: '签约协议不存在',
  3101: '短信套餐包余量不足，请购买短信套餐包。',
  3102: '发送短信过于频繁',
  3103: '请不要频繁的发送邮件',
  5001: '文章不存在',
  5002: '已达到文章最大投币数',
  5003: '不允许给自己投币'
};
export default ({ $axios }) => {
  // 定义一个缓存池用来缓存数据
  let cache = {};
  const EXPIRE_TIME = 1800000; // 30分钟
  // 利用axios的cancelToken来取消请求
  const CancelToken = $axios.CancelToken;
  // 客户端请求地址: /proxy
  // 因为nuxt服务端配置了中间件 nuxt.config => serverMiddleware
  // 这个中间件会处理以 /proxy 开头的请求, 将这个请求代理到远程服务器
  // 因为有了nuxt服务端代理, 所以开发模式下也不需要nuxt.config.js中的 proxy 代理了
  if (process.client) {
    $axios.setBaseURL('/proxy');
  }

  // 服务端没有跨域问题直接请求
  if (process.server) {
    $axios.setBaseURL(process.env.VUE_APP_BASE_URL);
  }

  // 全局请求拦截器: 添加请求头| 处理请求参数
  $axios.interceptors.request.use((request) => {
    // 如果需要缓存--考虑到并不是所有接口都需要缓存的情况
    if (request.data && request.data.cache) {
      let source = CancelToken.source()
      request.cancelToken = source.token
      let data = null;
      // 去缓存池获取缓存数据
      // console.log(cache, "首屏加载")
      if (cache && cache[`${request.url}`]) {
        data = cache[`${request.url}`]
      }
      // 获取当前时间戳
      let expire_time = getExpireTime()
      // 判断缓存池中是否存在已有数据 存在的话 再判断是否过期
      // 未过期 source.cancel会取消当前的请求 并将内容返回到拦截器的err中
      if (data && expire_time - data.expire < EXPIRE_TIME) {
        source.cancel(data)
        data = null
      }
    }
    request.headers['Content-Type'] = 'application/json';
    request.data = Vue.prototype.apiParamsGenerator(request.data, request.url);
    return request;
  });

  // 全局响应拦截器
  // 响应拦截器中用于缓存数据 如果数据没有缓存的话
  $axios.interceptors.response.use((res) => {
    if (process.browser && res.data.code !== 0) {
      const msg = errno[res.data.code] || 'request error';
      console.warn({ msg, errno: res.data.code });
      Vue.prototype.$message.error(msg);
    }
    if (res.config.url === '/api/category/get-article-cates') {
      // 缓存数据 并将当前时间存入 方便之后判断是否过期
      let data = {
        expire: getExpireTime(),
        data: res
      }
      cache[`${res.config.url}`] = data
      data = null
    }
    return res;
  }, error => {
    // 请求拦截器中的source.cancel会将内容发送到error中
    // 通过axios.isCancel(error)来判断是否返回有数据 有的话直接返回给用户
    if ($axios.isCancel(error)) return Promise.resolve(error.message.data)
    // 如果没有的话 则是正常的接口错误 直接返回错误信息给用户
    return Promise.reject(error)
  });
};
// 获取当前时间
function getExpireTime() {
  return new Date().getTime()
}
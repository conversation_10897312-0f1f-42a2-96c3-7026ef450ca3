<template>
  <div>
    <Nav />
    <div class="container">
      <div class="left"></div>
      <div class="right">
        <nuxt />
      </div>
    </div>
  </div>
</template>
<script>
import Nav from '@/components/Nav';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  components: {
    Nav
  },
  mounted() {
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  }
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 1160px;
  margin: 0 auto;
  margin-top: 30px;
  display: flex;
  justify-content: space-between;

  .left {
    width: 240px;
  }

  .right {
    width: 910px;
  }
}
</style>

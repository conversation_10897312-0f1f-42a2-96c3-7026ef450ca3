<template>
  <div v-if="show" class="fixed-download">
    <div class="container">
      <div class="fixed-container">
        <div class="logo">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 778 768.04">
            <polygon points="279.88 0 290.19 766.44 411.27 766.44 417.71 628.61 426.72 468.88 451.2 0 279.88 0" />
            <path d="M206.23,696.47,176.3,18.91H47.48l-30,744L258,786.66c15.11.58,22.61-19.34,11.33-30.08Z" transform="translate(-17.49 -18.91)" />
            <path
              d="M551.71,133.54a45,45,0,0,1-7.5-16.61c-2.54-11.08-3.42-29.09,12.39-41.31,9.11-7,19.29-8,28.46-6.44C601.89,72,615.8,84,622.24,99.79c6.84,16.79,14.33,44.3,10.8,79.47C633,179.26,575.88,166.41,551.71,133.54Z"
              transform="translate(-17.49 -18.91)"
            />
            <path
              d="M679.38,90.34a34.25,34.25,0,0,1,10-9.56c7.3-4.61,20-9.78,32.74-1.52,7.34,4.76,10.59,11.82,11.79,18.79,2.2,12.79-2.87,25.77-12.59,34.37-10.32,9.14-28.16,21.44-54.28,27.76C667.07,160.18,661.9,116,679.38,90.34Z"
              transform="translate(-17.49 -18.91)"
            />
            <path
              d="M754.73,406.84a6.94,6.94,0,0,0,9.31-.64l29.52-30.74a7,7,0,0,0,.57-8.94l-22.75-30.66L656.52,202.77l-221.36,241-9.8,173.72L678.93,787l99.33-107.28a4.09,4.09,0,0,0-2.58-6.87l-56.8-5.55Q638.74,597.89,561.25,525L600,490.1l27.51,28a7,7,0,0,0,9.46.41l24.9-21.26a6.93,6.93,0,0,0,0-10.55l-29.82-25.47,38.45-34.61,37.14,30.68a7,7,0,0,0,9.21-.32l27.27-26a6.93,6.93,0,0,0,.14-9.91L712.4,389l9.24-8.32ZM511.8,490.08a31,31,0,1,1,31-31A31,31,0,0,1,511.8,490.08Z"
              transform="translate(-17.49 -18.91)"
            />
          </svg>
        </div>
        <p class="text">{{ $t('components.fixed_download.dl_client') }}</p>
        <button class="download bg-white">
          <span> {{ $t('components.fixed_download.download') }} </span>
          <img class="qrcode" src="@/assets/images/qr_code.png" />
        </button>
        <div class="close fs-la" @click="show = false">&times;</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    show: true
  })
};
</script>
<style lang="scss" scoped>
.fixed-download {
  position: fixed;
  z-index: 2;
  left: 0;
  bottom: 0;
  width: 100%;
  min-width: 1160px;
  background-color: white;
  min-width: 1160px;
  height: 50px;
  border: 1px solid #f3f3f3;
  display: flex;
  align-items: center;
  .container {
    position: relative;
    width: 1160px;
    margin: 0 auto;

    .fixed-container {
      width: 360px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      .logo {
        width: 30px;
        height: 30px;
        border: 1px solid rgba(57, 215, 217, 1);
        border-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1px;
        svg {
          width: 100%;
          fill: #39d7d9;
        }
      }

      p.text {
        margin: 0;
        margin-left: 10px;
      }

      .download {
        margin-left: 20px;
        display: block;
        width: 45px;
        height: 20px;
        line-height: 20px;
        font-size: 13px;
        text-align: center;
        border: 1px solid #39d7d9;
        border-radius: 5px;
        color: #39d7d9;
        text-decoration: none;
        position: relative;
        &:hover .qrcode {
          display: block;
        }
        .qrcode {
          display: none;
          width: 100px;
          height: 100px;
          position: absolute;
          z-index: 99;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      .close {
        cursor: pointer;
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
      }
    }
  }
}
</style>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零宽字符水印测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .highlight {
            background: yellow;
            padding: 2px 4px;
        }
    </style>
</head>
<body>
    <h1>🔬 零宽字符水印测试</h1>
    <p>这个测试将验证浏览器对零宽字符的支持情况，这是隐形水印技术的基础。</p>
    
    <div id="results"></div>
    
    <script>
        function runTest() {
            let output = '';
            let allTestsPassed = true;
            
            // 测试1：基础显示测试
            output += '<div class="test-section">';
            output += '<h2>📝 测试1：基础显示测试</h2>';
            
            const normalText = "这是正常文本";
            const watermarkedText = "这是\u200B正常\u200C文本\u200D";
            
            output += '<p><strong>正常文本:</strong> <span class="highlight">' + normalText + '</span></p>';
            output += '<p><strong>水印文本:</strong> <span class="highlight">' + watermarkedText + '</span></p>';
            
            const visuallySame = normalText !== watermarkedText;
            output += '<p><strong>视觉效果:</strong> ' + (visuallySame ? '✅ 看起来完全一样' : '❌ 看起来不一样') + '</p>';
            output += '<p><strong>长度对比:</strong> 正常:' + normalText.length + '字符, 水印:' + watermarkedText.length + '字符</p>';
            
            if (!visuallySame || watermarkedText.length <= normalText.length) {
                allTestsPassed = false;
            }
            output += '</div>';
            
            // 测试2：零宽字符检测
            output += '<div class="test-section">';
            output += '<h2>🔍 测试2：零宽字符检测</h2>';
            
            function detectWatermark(text) {
                const invisibleChars = text.match(/[\u200B\u200C\u200D]/g);
                return invisibleChars ? invisibleChars.length : 0;
            }
            
            const watermarkCount = detectWatermark(watermarkedText);
            const normalCount = detectWatermark(normalText);
            
            output += '<p><strong>水印文本中的零宽字符数量:</strong> ' + watermarkCount + '</p>';
            output += '<p><strong>正常文本中的零宽字符数量:</strong> ' + normalCount + '</p>';
            
            if (watermarkCount !== 3 || normalCount !== 0) {
                allTestsPassed = false;
            }
            output += '</div>';
            
            // 测试3：编码解码功能
            output += '<div class="test-section">';
            output += '<h2>🔄 测试3：编码解码功能</h2>';
            
            function encodeToInvisible(text) {
                return text.split('').map(char => {
                    const binary = char.charCodeAt(0).toString(2).padStart(8, '0');
                    return binary.split('').map(bit => bit === '1' ? '\u200B' : '\u200C').join('');
                }).join('\u200D');
            }
            
            function decodeFromInvisible(invisibleText) {
                const parts = invisibleText.split('\u200D');
                return parts.map(part => {
                    const binary = part.split('').map(char => char === '\u200B' ? '1' : '0').join('');
                    const chunks = binary.match(/.{8}/g) || [];
                    return chunks.map(chunk => String.fromCharCode(parseInt(chunk, 2))).join('');
                }).join('');
            }
            
            const testData = "lightnovel.fun";
            const encoded = encodeToInvisible(testData);
            const decoded = decodeFromInvisible(encoded);
            
            output += '<p><strong>原始数据:</strong> ' + testData + '</p>';
            output += '<p><strong>编码后长度:</strong> ' + encoded.length + ' 个零宽字符</p>';
            output += '<p><strong>解码结果:</strong> ' + decoded + '</p>';
            output += '<p><strong>编码解码正确性:</strong> ' + (testData === decoded ? '✅ 正确' : '❌ 错误') + '</p>';
            
            if (testData !== decoded) {
                allTestsPassed = false;
            }
            output += '</div>';
            
            // 测试4：复制粘贴测试
            output += '<div class="test-section">';
            output += '<h2>📋 测试4：复制粘贴测试</h2>';
            output += '<p>请复制下面这段带水印的文本到记事本，然后再粘贴回来：</p>';
            output += '<textarea id="copyTest" style="width: 100%; height: 60px;">' + watermarkedText + '</textarea>';
            output += '<p><em>如果粘贴后的文本长度仍然是' + watermarkedText.length + '个字符，说明零宽字符被保留了。</em></p>';
            output += '</div>';
            
            // 测试5：随机水印生成测试
            output += '<div class="test-section">';
            output += '<h2>🎲 测试5：随机水印生成</h2>';
            
            function generateRandomWatermark(articleId) {
                const templates = [
                    'LN_{timestamp}',
                    'art_{articleId}',
                    'site_{timestamp}_{articleId}'
                ];
                const template = templates[Math.floor(Math.random() * templates.length)];
                const timestamp = Date.now();
                
                return template
                    .replace('{timestamp}', timestamp)
                    .replace('{articleId}', articleId);
            }
            
            output += '<p><strong>生成5个随机水印:</strong></p>';
            for (let i = 0; i < 5; i++) {
                const watermark = generateRandomWatermark('test123');
                const encoded = encodeToInvisible(watermark);
                output += '<p>' + (i+1) + '. 原始: ' + watermark + ' (编码后: ' + encoded.length + '字符)</p>';
            }
            output += '</div>';
            
            // 最终结果
            output += '<div class="test-section">';
            output += '<h2>📊 测试结果</h2>';
            
            if (allTestsPassed) {
                output += '<p class="success">🎉 所有核心测试通过！你的浏览器完全支持零宽字符水印技术。</p>';
                output += '<p><strong>下一步:</strong> 可以继续测试SCEditor编辑器的兼容性。</p>';
            } else {
                output += '<p class="error">⚠️ 部分测试失败，可能存在兼容性问题。</p>';
                output += '<p>请检查浏览器版本或尝试其他浏览器。</p>';
            }
            
            output += '<p><strong>测试环境:</strong></p>';
            output += '<ul>';
            output += '<li>浏览器: ' + navigator.userAgent.split(' ').pop() + '</li>';
            output += '<li>时间: ' + new Date().toLocaleString() + '</li>';
            output += '</ul>';
            output += '</div>';
            
            document.getElementById('results').innerHTML = output;
        }
        
        // 页面加载后自动运行测试
        window.onload = runTest;
    </script>
</body>
</html>

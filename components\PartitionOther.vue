<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="module-list">
    <nuxt-link v-for="item in items" :key="item.aid" class="module-item" :to="$i18n.path(`detail/${item.aid}`)" target="_blank">
      <div class="module-cover" :style="{ 'background-image': `url(${item.cover})` }">
        <span v-if="item.sid > 0" class="lv-icon lv-0 tag">{{ $t('components.part_card.collection') }}</span>
      </div>
      <div class="module-detail">
        <div :title="item.title" class="module-title text-hide-2">
          <span v-if="item.is_top" class="fixedtop lv-icon lv-0 border">{{ $t('components.part_card.fixed_top') }}</span>
          <span v-html="item.title"></span>
        </div>
        <div class="module-footer">
          <nuxt-link class="module-author" :title="item.author" target="_blank" :to="$i18n.path(`profile/${item.uid}`)">
            {{ item.author }}
          </nuxt-link>
          <div v-if="!item.is_top" class="module-time" style="text-align: right;">
            {{ item.last_time | date2short }}
          </div>
        </div>
      </div>
    </nuxt-link>
  </div>
</template>
<script>
export default {
  props: {
    items: {
      type: Array,
      default: () => []
    }
  }
};
</script>
<style lang="scss" scoped>
.module-list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-left: -20px;
  a.module-item {
    position: relative;
    display: block;
    width: 160px;
    height: 300px;
    margin-top: 20px;
    text-decoration: none;
    color: $dark;
    margin-left: 20px;

    .module-cover {
      width: 100%;
      height: 225px;
      border-radius: 5px;
      overflow: hidden;
      background-size: cover;
      background-position: top center;
      background-repeat: no-repeat;
      position: relative;
      .tag {
        position: absolute;
        top: 5px;
        right: 5px;
      }
    }

    .module-detail {
      margin-top: 7px;
      .module-title {
        height: 48px;
        overflow: hidden;
        font-size: $sm;
        line-height: 24px;
        color: $dark;
        word-break: break-all;
        .fixedtop {
          line-height: 1;
        }
      }
      .module-footer {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: $gray;
        .module-author,
        .module-time {
          color: $gray;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-decoration: none;
        }
      }
    }
  }
}
</style>

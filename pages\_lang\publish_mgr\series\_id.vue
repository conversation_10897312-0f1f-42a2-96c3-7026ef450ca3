<template>
  <div class="publish_mgr_container">
    <!-- 上传图片 -->
    <div v-show="false">
      <el-upload
        :action="uploadURL"
        :data="uploadImageJSONData"
        :before-upload="beforeBannerUpload"
        :on-success="coverUploadSuccess"
        :on-error="coverUploadError"
      >
        <button id="upload-collection-img-btn" slot="trigger"></button>
      </el-upload>
    </div>

    <!-- 裁剪图片 -->
    <el-dialog
      title=""
      width="90%"
      top="10vh"
      :fullscreen="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :visible="showCrop"
      class="crop-dialog"
    >
      <div id="crop-container">
        <div class="crop-box">
          <!-- 竖排封面裁剪 -->
          <vue-cropper
            v-if="verticalMode"
            ref="cropper"
            mode="cover"
            :img="cropOptions.img"
            :output-size="cropOptions.outputSize"
            :output-type="cropOptions.outputType"
            :full="cropOptions.full"
            :high="cropOptions.high"
            :fixed="cropOptions.fixed"
            :can-move="cropOptions.canMove"
            :can-move-box="cropOptions.canMoveBox"
            :fixed-box="cropOptions.fixedBox"
            :original="cropOptions.original"
            :center-box="cropOptions.centerBox"
            :auto-crop="true"
            :fixed-number="[cropperSize1.w / cropperSize1.h, 1]"
            @real-time="realTimePreview"
          />
          <!-- 横排封面裁剪 -->
          <vue-cropper
            v-else
            ref="cropper"
            mode="cover"
            :img="cropOptions.img"
            :output-size="cropOptions.outputSize"
            :output-type="cropOptions.outputType"
            :full="cropOptions.full"
            :high="cropOptions.high"
            :fixed="cropOptions.fixed"
            :can-move="cropOptions.canMove"
            :can-move-box="cropOptions.canMoveBox"
            :fixed-box="cropOptions.fixedBox"
            :original="cropOptions.original"
            :auto-crop="true"
            :fixed-number="[cropperSize2.w / cropperSize2.h, 1]"
            :center-box="cropOptions.centerBox"
            @real-time="realTimePreview"
          />

          <!-- 放大缩小 -->
          <div class="scale-btns">
            <button class="btn-img-scale btn btn-primary" @click="scaleImage(1)">
              {{ $t('write.scale_max') }}
            </button>
            <button class="btn-img-scale btn btn-primary border" @click="scaleImage(-1)">
              {{ $t('write.scale_min') }}
            </button>
          </div>
        </div>
        <div class="preview-box" :style="corpPreviews.div">
          <img :src="corpPreviews.url" :style="corpPreviews.img" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <button class="btn btn-primary" @click="cropImage(true)">
          {{ $t('write.btn_confirm') }}
        </button>
        <button class="btn btn-primary border" @click="cropImage(false)">
          {{ $t('write.btn_cancel') }}
        </button>
      </div>
    </el-dialog>

    <!-- ======================================== 新建合集 ======================================== -->
    <el-dialog
      :visible="showCollectionDialog"
      :close-on-click-modal="false"
      top="10vh"
      width="580px"
      class="new-collection-dialog"
      @close="showCollectionDialog = false"
    >
      <p slot="title" class="fs-ml text-dark">
        <span>{{ $t('write.edit_collection') }}</span>
      </p>
      <!-- 新建合集: 分区 -->
      <div class="item">
        <p class="sub-title">
          {{ $t('write.category') }}
          <span class="require">*</span>
        </p>
        <select class="select mar-right-20" @change="selectSupperGroup">
          <option v-for="(item, index) in articleConfigs.groups" :key="index" :value="index" :selected="selectedSupGid === item.gid">
            {{ item.name }}
          </option>
        </select>
        <select v-model="collection.gid" class="select">
          <option v-for="(item, index) in subCollectionGroups" :key="index" :value="item.gid">
            {{ item.name }}
          </option>
        </select>
      </div>
      <!-- 新建合集: 名称 -->
      <div class="item">
        <p class="sub-title">{{ $t('write.collection_name') }} <span class="require">*</span></p>
        <div class="flex">
          <input v-model="collection.name" type="text" class="input-control" maxlength="80" :placeholder="$t('write.input_collection_name')" />
          <p class="flex ai-center text-counter">
            {{ 80 - collection.name.length }}
          </p>
        </div>
      </div>
      <!-- 新建合集: 作者 -->
      <div class="item">
        <p class="sub-title">{{ $t('write.author') }}</p>
        <div class="flex">
          <input v-model="collection.author" type="text" class="input-control" :placeholder="$t('write.input_collection_author')" />
          <p class="flex ai-center text-counter">
            {{ 20 - collection.author.length }}
          </p>
        </div>
      </div>
      <!-- 新建合集: 简介 -->
      <div class="item">
        <p class="sub-title">{{ $t('write.intro') }}</p>
        <div class="flex">
          <textarea
            v-model="collection.intro"
            type="text"
            class="collection-intro input-control"
            :placeholder="$t('write.input_collection_intro')"
            maxlength="150"
          ></textarea>
          <p class="flex ai-center text-counter">
            {{ 150 - collection.intro.length }}
          </p>
        </div>
      </div>
      <!-- 新建合集: 封面 -->
      <div class="item">
        <p class="sub-title">
          {{ $t('write.banner') }}
        </p>
        <div class="select-banner btn flex ai-center jc-center" :class="{ vertical: verticalMode }" @click="selectCover(true)">
          <!-- 0: 默认情况 1:上传中(显示loading)  2:上传成功(显示图片) -->
          <svg v-if="collectionCoverState === 0" class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 626.7 626.7">
            <path
              d="M646.4,86.9H154.1a67.28,67.28,0,0,0-67.2,67.2V646.4a67.28,67.28,0,0,0,67.2,67.2H646.4a67.28,67.28,0,0,0,67.2-67.2V154.1A67.28,67.28,0,0,0,646.4,86.9ZM154.1,134H646.4a20.1,20.1,0,0,1,20.1,20.1V496.6L580.2,330.4c-16.8-32.4-61.9-35.7-83.3-6L415.8,437.3a25.15,25.15,0,0,1-34.1,6.5l-76.9-49.2a78.68,78.68,0,0,0-107,21.4L134,508V154.1A20.1,20.1,0,0,1,154.1,134Z"
              transform="translate(-86.9 -86.9)"
            />
          </svg>
          <Loading v-else-if="collectionCoverState === 1" />
          <img v-else-if="collectionCoverState === 2" :src="collectionCover" class="cover-img" />
        </div>
      </div>
      <!-- 新建合集: 底部按钮 -->
      <p slot="footer" class="dialog-footer flex ai-center jc-center">
        <button class="btn btn-publish btn-primary" @click="updateCollection(collection.sid)">
          {{ $t('write.confirm') }}
        </button>
      </p>
    </el-dialog>
    <!-- ==================== 新建合集结束 ==================== -->

    <TitleTabbar :title="$t('publish_mgr.publish_mgr')" all-count="123" book-count="435" :show-more="false" @tab-change="tabChange">
      <!-- 内容区域: 全部 -->
      <template v-slot:articles>
        <div v-if="publish_series_articles.length">
          <div v-for="item in publish_series_articles" :key="item.sid" class="collect-item">
            <CollectionItem
              :show-delete-btn="false"
              :show-edit-btn="true"
              :banner="item.cover"
              :title="item.name"
              :date="item.last_time | date2short"
              :author="item.group_name"
              :views="item.hits"
              :comments="item.comments"
              :coins="item.coins"
              :collections="item.favorites"
              :shares="item.shares"
              :zans="item.likes"
              :is-series="false"
              :has-icon="true"
              :page-indexs="item.articles"
              :sid="item.sid"
              @cover-click="toPage(`/series/${item.sid}`)"
              @delete-btn-click="deleteArticleConfirm(item.sid)"
              @edit-btn-click="showEditSeriesLayer(item.sid)"
              @delete-page-index="removePageIndex($event, item.sid)"
            />
          </div>
          <div class="pagination">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="articleLoading"
              :hide-on-single-page="true"
              :current-page="publish_series_articles_paginate.cur"
              :total="publish_series_articles_paginate.count"
              :page-size="publish_series_articles_paginate.size"
              @current-change="articlesChangePage"
            />
          </div>
        </div>
        <!-- 如果没有帖子显示: 还没有发表过帖子, 立即发帖 -->
        <Loading v-if="articleLoading && publish_series_articles.length === 0" />
        <div v-if="!articleLoading && publish_series_articles.length === 0" class="flex ai-center jc-center fs-sm text-gray" style="height: 300px">
          <span>{{ $t('settings.not_published_and') }}</span>
          <nuxt-link target="_blank" class="btn btn-sm tdn flex ai-center jc-center btn-primary mar-left-5" :to="$i18n.path(`write`)">
            <span>{{ $t('settings.publish') }}</span>
          </nuxt-link>
        </div>
      </template>

      <!-- 内容区域: 图书 -->
      <template v-slot:books>
        <div v-if="publish_series_books.length">
          <div v-for="item in publish_series_books" :key="item.sid" class="collect-item">
            <CollectionItem
              :show-delete-btn="false"
              :show-edit-btn="true"
              :banner="item.cover"
              :title="item.name"
              :date="item.last_time | date2short"
              :author="item.group_name"
              :views="item.hits"
              :comments="item.comments"
              :coins="item.coins"
              :collections="item.favorites"
              :shares="item.shares"
              :zans="item.likes"
              :is-series="false"
              :has-icon="true"
              :page-indexs="item.articles"
              :sid="item.sid"
              mode="vertical"
              @cover-click="toPage(`/series/${item.sid}`)"
              @delete-btn-click="deleteArticleConfirm(item.sid, true)"
              @edit-btn-click="showEditSeriesLayer(item.sid)"
              @delete-page-index="removePageIndex($event, item.sid)"
            />
          </div>
          <div class="pagination">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="bookLoading"
              :hide-on-single-page="true"
              :current-page="publish_series_books_paginate.cur"
              :total="publish_series_books_paginate.count"
              :page-size="publish_series_books_paginate.size"
              @current-change="booksChangePage"
            />
          </div>
        </div>

        <!-- 如果没有数据显示: 暂无数据 -->
        <div v-else class="empty-tips flex ai-center jc-center fs-sm text-gray">
          {{ $t('settings.no_collects') }}
        </div>
      </template>
    </TitleTabbar>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import TitleTabbar from '@/components/TitleTabbar.vue';
import CollectionItem from '@/components/CollectionItem.vue';
import Loading from '@/components/Loading';

export default {
  layout: 'settings',
  middleware: ['auth'],
  components: {
    Loading,
    TitleTabbar,
    CollectionItem
  },

  async fetch({ store, params }) {
    const uid = params.id;
    // await store.dispatch('userPublish/getPublishSeriesArticles', { uid });
    await store.dispatch('userPublish/getPublishSeriesBooks', { uid });
    await store.dispatch('write/getArticleConfigs');
  },

  asyncData({ params }) {
    return {
      uid: params.id
    };
  },

  data: () => ({
    uid: 0,
    tabIndex: 0, // tabbar 索引
    articleLoading: false, // 是否显示加载文章 loading
    bookLoading: false, // 是否显示加载图书 loading
    uploadFormData: { md5: '', security_key: '' }, // 上传图片需要的参数
    sending: false, // 是否在发送请求
    showCrop: false, // 是否显示裁剪图片的弹窗
    verticalMode: false, // 封面排版
    showCollectionDialog: false, // 是否显示修改合集的弹窗
    selectedSupGid: 0, // 当前选中的 gid
    subCollectionGroups: [], // 编辑合集子分区
    collectionCover: '', // 合集上传成功后的 url
    collectionCoverState: 0, // 合集 封面 上传框状态 0:未上传 1:上传中 2:上传成功
    cropperSize1: {
      // 竖排封面尺寸
      w: 420 / 3,
      h: Math.ceil(590 / 3)
    },
    cropperSize2: {
      // 横排封面尺寸
      w: Math.ceil(716 / 3),
      h: 420 / 3
    },
    corpPreviews: {}, // 裁剪的预览数据
    cropOptions: {
      img: '', // 要裁剪的图片地址
      outputType: '', // 图片裁剪后类型[jpeg|png]
      outputSize: 0.5, // 图片裁剪后质量
      size: 1, // 图片裁剪质量 0 - 1
      canMove: true, // 是否能移动图片
      canMoveBox: true, // 是否能移动裁剪框
      fixed: true, // 固定缩放比例
      fixedBox: false, // 是否固定裁剪框大小
      original: true, // 裁剪后图片按照原始比例渲染
      fixedNumber: [1, 0.72], // 缩放比例
      autoCropWidth: 210, // 裁剪框的宽度
      autoCropHeight: 295, // 裁剪框的高度
      centerBox: true, // 裁剪框是否被限制在图片里面
      full: true, // 是否输出原图比例的截图
      high: false // 	是否按照设备的dpr 输出等比例图片
    },
    // 合集数据
    collection: {
      name: '',
      cover_id: '',
      author: '',
      intro: '',
      banner_id: ''
    }
  }),

  computed: {
    ...mapGetters('login', ['loginUser']),
    ...mapState('userPublish', [
      'publish_series_articles',
      'publish_series_articles_paginate',
      'publish_series_books',
      'publish_series_books_paginate'
    ]),
    ...mapGetters('write', ['articleConfigs']),
    uploadURL() {
      return `${process.env.VUE_APP_UPLOAD_URL}/upload/article-image`;
    },
    uploadImageJSONData() {
      const data = this.apiParamsGenerator(this.uploadFormData, true);
      return data;
    }
  },

  mounted() {
    if (process.browser) {
      this.uploadFormData.security_key = this.loginUser.security_key;
    }
  },

  methods: {
    // TODO: 将文章从合集中删除, 删除后刷新 page-indexs
    removePageIndex(aid, sid) {
      console.log({ aid, sid });
    },

    // 放大+缩小裁剪图片的操作
    scaleImage(num) {
      this.$refs.cropper.changeScale(num || 1);
    },

    // 文章分页
    articlesChangePage(page) {
      this.articleLoading = true;
      this.$store
        .dispatch('userPublish/getPublishSeriesArticles', {
          uid: this.uid,
          page
        })
        .finally(() => {
          this.articleLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // 图书分页
    booksChangePage(page) {
      this.bookLoading = true;
      this.$store
        .dispatch('userPublish/getPublishSeriesBooks', {
          uid: this.uid,
          page
        })
        .finally(() => {
          this.bookLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // 提交编辑合集信息
    async updateCollection() {
      /* eslint-disable camelcase */
      const { gid, sid, cover_id } = this.collection;
      if (!this.collection.gid) {
        this.$message.error(this.$t('write.select_group'));
        return false;
      }
      if (!this.collection.name.trim()) {
        this.$message.error(this.$t('write.input_collection_name'));
        return false;
      }

      const params = { gid, sid };
      if (cover_id) params.cover_id = cover_id;
      Object.keys(this.oldData).forEach((keyItem) => {
        if (this.oldData[keyItem] !== this.collection[keyItem]) {
          params[keyItem] = this.collection[keyItem];
        }
      });

      const res = await this.$axios.$post('/api/series/edit-info', params);
      if (res.code === 0) {
        // 修改成功后: 隐藏弹窗, 刷新数据
        this.showCollectionDialog = false;
        await this.$store.dispatch('userPublish/getPublishSeriesArticles', {
          uid: this.uid
        });
      }
    },

    // 切换裁剪图片的 dialog 隐藏和显示
    toggleCropDialog(show, file) {
      this.cropOptions.img = file || '';
      this.showCrop = show;
    },

    // 上传之前处理 banner: 压缩裁剪获取md5
    beforeBannerUpload(file) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        this.uploadFilename = file.name;
        this.cropOptions.outputType = file.type.split('/').pop();

        // 是否超过允许的最大大小 10M
        if (file.size > 10485760) {
          this.$message.error(this.$t('write.file_too_max'));
          return reject(file);
        }

        // 将文件对象转为base64 对象 然后将开启图片裁剪 dialog
        const fileBase64 = await this.file2base64(file);
        this.toggleCropDialog(true, fileBase64);

        // 如果 showCrop 为 false 裁剪结束了
        this.$watch('showCrop', async (val) => {
          if (!val) {
            // 如果不上传就取消
            if (!this.cropedUploadConfirm) {
              return reject(file);
            }

            // 显示 loading
            if (this.isUploadCollectionCover) {
              this.collectionCoverState = 1;
            } else {
              this.articleCoverState = 1;
            }

            // 压缩图片(如果大于1MB就压缩到1M以下)
            const minFile = await this.imageCompression(this.cropedFile);

            // 获取文件md5 判断文件是否存在, 存在就不上传
            const md5 = await this.getFileMD5(minFile);
            const res = await this.$axios.$post('/upload/get-article-image', {
              md5
            });
            if (res.code === 0 && res.data) {
              this.coverUploadSuccess(res, { raw: minFile });
              return reject(minFile);
            }

            // 如果图片不存在就组合数据上传文件
            this.uploadFormData.md5 = md5;
            return resolve(minFile);
          }
        });
      });
    },

    // 封面上传成功后
    async coverUploadSuccess(res, { raw: file }) {
      if (res.code === 0) {
        const base64 = await this.file2base64(file);
        const resId = res.data.res_id;
        this.collection.banner_id = this.collection.cover_id = resId;
        this.collectionCover = base64;
        this.collectionCoverState = 2;
      }
    },

    // 文章封面上传失败
    coverUploadError() {
      this.$message.error(this.$t('tips.cover_upload_fail'));
      this.collectionCoverState = 0;
    },

    // 选择封面
    selectCover() {
      document.querySelector('#upload-collection-img-btn').click();
    },

    // 选择大分区
    selectSupperGroup(e) {
      const groups = this.articleConfigs.groups;
      const selectedGroup = Array.isArray(e) ? e[0] : groups[e.target.value];
      this.verticalMode = !!selectedGroup.cover_type;
      this.subCollectionGroups = selectedGroup.items;
      this.selectedSupGid = selectedGroup.gid;
    },

    // 裁剪图片: 获得裁剪后的图片 blob 对象
    cropImage(confirm) {
      const minWidth = this.verticalMode ? this.cropperSize1.w : this.cropperSize2.w;
      if (this.corpPreviews.w < minWidth) {
        this.$message.error(this.$t('write.min_width') + minWidth);
        return;
      }
      this.$refs.cropper.getCropBlob((blob) => {
        this.toggleCropDialog(false);
        this.cropedFile = new window.File([blob], this.uploadFilename, {
          type: blob.type
        });
        this.cropedUploadConfirm = confirm;
      });
    },

    // 裁剪实时预览
    realTimePreview(preview) {
      this.corpPreviews = preview;
    },

    // 获取对应合集的信息, 显示编辑合集弹窗
    async showEditSeriesLayer(sid) {
      if (this.sending) return;

      if (this.collection.sid === sid) {
        this.showCollectionDialog = true;
        return;
      }
      this.sending = true;
      const res = await this.$axios.$post('/api/series/get-info', { sid });
      this.sending = false;
      if (res.code === 0) {
        const { name, author, intro } = res.data;
        this.oldData = { name, author, intro };
        this.collection = res.data;
        this.collectionCoverState = 2;
        this.collectionCover = res.data.cover;
        this.showCollectionDialog = true;
        /* eslint-disable-next-line */
        const supperGroup = this.articleConfigs.groups.find((group) => group.items.hasOwnProperty(res.data.gid));
        this.selectSupperGroup([supperGroup]);
      }
    },

    // tab切换
    tabChange(i) {
      this.tabIndex = i;
      if (i === 1 && this.publish_series_articles.length === 0) {
        this.articlesChangePage(1);
      }
    },

    // 删除文章
    deleteArticleConfirm(id, isBook = false) {
      const payload = {
        fid: id,
        _class: 2,
        dataInfo: {
          id,
          key: 'sid',
          data: 'publish_series_articles'
        }
      };
      if (isBook) {
        payload.dataInfo.data = 'publish_series_books';
      }
      this.$confirm(this.$t('tips.are_you_sure'), this.$t('components.type_warn'), {
        confirmButtonText: this.$t('components.btn_confirm'),
        cancelButtonText: this.$t('components.btn_cancel'),
        type: 'warning'
      })
        .then(() => this.deleteArticle(payload))
        .catch(Function.prototype);
    },

    // 执行删除
    deleteArticle(payload) {
      this.$store
        .dispatch('userPublish/deletePublish', payload)
        .then(() => {
          this.$message.success(this.$t('tips.delete_success'));
        })
        .catch(() => {
          this.$message.error(this.$t('tips.delete_fail'));
        });
    }
  },

  head() {
    const title = this.$t('publish_mgr.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
@import '@/assets/scss/collect.scss';
/* 图片裁剪插件 */
.crop-dialog {
  #crop-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .crop-box {
      width: 800px;
      height: 500px;
      .scale-btns {
        padding: 20px 0;
        .btn-img-scale {
          width: 100px;
          height: 30px;
        }
      }
    }
    .preview-box {
      border: 1px solid $primary;
      overflow: hidden;
    }
  }
  .dialog-footer {
    .btn {
      width: 100px;
      height: 35px;
      border-radius: 35px;
    }
  }
}

.new-collection-dialog {
  .btn-publish {
    width: 130px;
    height: 35px;
    border-radius: 35px;
    text-align: center;
    margin: 0 auto;
  }
  .item {
    .contents-files {
      padding-bottom: 20px;
    }
    padding-bottom: 30px;
    width: 100%;
    box-sizing: border-box;
    .collection-intro {
      height: 60px;
      padding: 10px 15px;
      resize: none;
    }
    .text-counter {
      min-width: 20px;
      font-size: 13px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      &.btn-del {
        .svg-icon {
          height: 13px;
          fill: $gray;
        }
      }
    }
    .require {
      color: #f00;
    }
    .article-title {
      max-width: 500px;
    }
    .sub-title {
      margin-bottom: 15px;
      .new-collection {
        background: none;
      }
    }
    .mar-right-20 {
      margin-right: 20px;
    }
    .select {
      width: 250px;
      height: 30px;
      border: 1px solid $gray-white;
      padding-left: 15px;
      padding-right: 10px;
      border-radius: 5px;
      color: $dark;
      appearance: none;
      background-color: #fff;
      background-image: url(../../../../assets/images/down_arrow.svg);
      background-repeat: no-repeat;
      background-size: 12px auto;
      background-position: 95% 55%;
    }
    .select-banner {
      width: 160px;
      height: 100px;
      background: rgba(250, 250, 250, 1);
      border: 1px solid rgba(238, 238, 238, 1);
      border-radius: 5px;
      background-size: 40px 40px;
      overflow: hidden;
      .svg-icon {
        width: 30px;
        fill: #b2b2b2;
      }
      &.vertical {
        width: 160px;
        height: 225px;
      }
      fill: #ccc;
      .cover-img {
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  }
  .extra-options,
  .checkboxs {
    .radio-btn {
      width: 15px;
      height: 15px;
      background: #dfdfdf;
      margin-right: 15px;
      border-radius: 50%;
      cursor: pointer;
      &.checked {
        background: $primary;
      }
    }
    .payinfo {
      padding: 15px 0;
      .left {
        width: 140px;
        text-align: right;
      }
      .right {
        padding-left: 15px;
        input {
          width: 250px;
        }
      }
    }
  }
}
</style>

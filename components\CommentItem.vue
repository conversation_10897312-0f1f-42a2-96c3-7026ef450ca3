<template>
  <div class="comment-item-container flex flex-cols">
    <div class="comment-user-container">
      <span class="fs15 mar-right-10">{{ username }}</span>
      <span class="lv-icon mar-right-10" :class="[`lv-${lv}`]">{{ lvName }}</span>
      <span v-if="floorMaster" class="lv-icon lv-0 mar-right-10">{{ $t('detail.floor_master') }}</span>
      <el-image
        v-if="typeof medal.img !== undefined && medal.img"
        :src="medal.img"
        :title="medal.name"
        fit="contain"
        lazy
        class="user-medal-image mar-right-10"
      ></el-image>
      <el-rate v-if="rate" class="my-rates" :value="rate" :disabled="true" />
    </div>
    <!-- <div class="content">{{ content }}</div> -->
    <!-- eslint-disable-next-line vue/no-v-html -->
    <pre class="content" v-html="content"></pre>
    <p class="infos">
      <span class="mar-r20 text-gray fs15">{{ date }}</span>
      <span class="mar-r20 text-gray dis-select btn fs15" :class="{ zaned: zaned }" @click="zan">
        <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 753.02 721.5">
          <path
            d="M178,267.55c-30.28,0-54.82,28.61-54.82,63.9v297.9c0,35.29,24.54,63.9,54.82,63.9s54.83-28.61,54.83-63.9V331.45C232.87,296.16,208.32,267.55,178,267.55Z"
            transform="translate(-123.22 -75)"
          />
          <path
            d="M632.53,288.62H474.17l39.23-96.08c16-39.12-6-117.54-59.07-117.54-34.22,0-64.5,17.17-74.81,42.41l-91.1,156.25a64.09,64.09,0,0,0-8.72,32.27V634.32c0,37.23,39.15,67.4,87.45,67.4h197c42,0,78.1-23,85.92-54.85l68.41-278.3C728.65,327.06,687.35,288.62,632.53,288.62Z"
            transform="translate(-123.22 -75)"
          />
        </svg>
        <span>{{ zans }}</span>
      </span>
      <span v-if="showReply" class="mar-r20 text-gray btn fs15" @click="reply">{{ $t('components.btn_reply') }}</span>
      <span v-if="showDelete" class="mar-r20 text-gray btn fs15" @click="del">{{ $t('components.btn_delete') }}</span>
    </p>
  </div>
</template>

<script>
export default {
  props: {
    floorMaster: {
      // 是否是楼主
      type: Boolean,
      default: false
    },
    username: {
      // 用户名
      type: [String, Number],
      default: ''
    },
    lv: {
      // 等级
      type: [String, Number],
      required: true
    },
    lvName: {
      // 等级名称
      type: String,
      required: true
    },
    content: {
      // 评论内容
      type: [String, Number],
      required: true
    },
    date: {
      // 发布日期
      type: String,
      required: true
    },
    zans: {
      // 点赞数量
      type: [String, Number],
      default: 0
    },
    zaned: {
      // 是否已经点赞
      type: [Boolean, Number],
      default: 0
    },
    showReply: {
      // 是否显示回复
      type: Boolean,
      default: true
    },
    showDelete: {
      // 是否显示删除
      type: Boolean,
      default: false
    },
    rate: {
      // 评分
      type: [Number, String],
      default: ''
    },
    medal: {
      // 勋章
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    zan() {
      // 点赞
      this.$emit('zan');
    },
    reply() {
      // 回复
      this.$emit('reply');
    },
    del() {
      // 删除
      this.$emit('del');
    }
  }
};
</script>

<style lang="scss" scope>
.comment-item-container {
  .my-rates {
    display: inline-block;
    .el-rate__item {
      width: 18px;
    }
  }
  .fs15 {
    font-size: 15px;
  }
  .mar-r20 {
    margin-right: 20px;
  }
  .content {
    width: 800px;
    overflow: hidden;
    white-space: pre-line;
    padding: 20px 0;
    line-height: 1.5;
    word-wrap: break-word;
    word-break: normal;
    max-width: 1080px;
    .quote {
      padding: 10px 15px;
      background-color: #f9f9f9;
      border-radius: 5px;
    }
    img {
      vertical-align: text-top !important;
    }
  }
  .infos {
    height: 20px;
    .zaned {
      color: $primary;
      .svg-icon {
        fill: $primary;
      }
    }
    .svg-icon {
      vertical-align: top;
      width: 18px;
      fill: $gray;
    }
  }
  .comment-user-container {
    display: flex;
    align-items: center;
    .user-medal-image {
      width: 30px;
      height: 30px;
    }
  }
}
</style>

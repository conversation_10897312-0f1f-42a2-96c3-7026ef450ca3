const offIfDevEnv = () => process.env.NODE_ENV === 'production' ? 2 : 0;

module.exports = {
  root: true,

  env: {
    browser: true,
    node: true
  },

  parserOptions: {
    parser: 'babel-eslint'
  },

  extends: [
    '@nuxtjs',
    'prettier',
    'prettier/vue',
    'plugin:prettier/recommended',
    'plugin:nuxt/recommended'
  ],

  plugins: [
    'prettier'
  ],

  // add your custom rules here
  rules: {
    'semi': ['error', 'always'],
    'no-debugger': offIfDevEnv(),
    'no-console': offIfDevEnv(),
  }
}

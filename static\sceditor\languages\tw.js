/**
 * <AUTHOR> Name> <Your e-mail/Website if you would like>
 * @license [MIT](http://www.opensource.org/licenses/mit-license.php)
 */
(function () {
	'use strict';

	sceditor.locale['tw'] = {
		'Bold': '粗體',
		'Italic': '斜體',
		'Underline': '底線',
		'Strikethrough': '删除線',
		'Subscript': '下標',
		'Superscript': '上標',
		'Align left': '靠左對齊',
		'Center': '置中',
		'Align right': '靠右對齊',
		'Justify': '兩端對齊',
		'Font Name': '字形',
		'Font Size': '字體大小',
		'Font Color': '文字顏色',
		'Remove Formatting': '清除格式',
		'Cut': '剪下',
		'Your browser does not allow the cut command. Please use the keyboard shortcut Ctrl/Cmd-X': '您的瀏覽器不支持剪下命令，請使用快速键 Ctrl/Cmd-X',
		'Copy': '拷貝',
		'Your browser does not allow the copy command. Please use the keyboard shortcut Ctrl/Cmd-C': '您的瀏覽器不支持拷貝命令，請使用快速键 Ctrl/Cmd-C',
		'Paste': '貼上',
		'Your browser does not allow the paste command. Please use the keyboard shortcut Ctrl/Cmd-V': '您的瀏覽器不支持貼上命令，請使用快速键 Ctrl/Cmd-V',
		'Paste your text inside the following box:': '請在下面貼上您的文字',
		'Paste Text': '貼上纯文字',
		'Bullet list': '符號列表',
		'Numbered list': '编號列表',
		'Undo': '復原',
		'Redo': '重做',
		'Rows:': '行數',
		'Cols:': '列數',
		'Insert a table': '插入表格',
		'Insert a horizontal rule': '插入分隔線',
		'Code': '原始碼',
		'Width (optional):': '寬度(選填)',
		'Height (optional):': '高度(選填)',
		'Insert an image': '插入圖片',
		'E-mail:': 'Email',
		'Insert an email': '插入Email',
		'URL:': '網址',
		'Insert a link': '插入超鏈結',
		'Unlink': '取消超鏈結',
		'More': '更多',
		'Insert an emoticon': '插入表情符號',
		'Video URL:': '影片網址',
		'Insert': '插入',
		'Insert a YouTube video': '插入 YouTube 影片',
		'Insert current date': '插入目前日期',
		'Insert current time': '插入目前時間',
		'Print': '列印',
		'View source': '查看原始碼',
		'Description (optional):': '描述(選填)',
		'Enter the image URL:': '輸入圖片網址',
		'Enter the e-mail address:': '輸入 Email',
		'Enter the displayed text:': '輸入顯示文字',
		'Enter URL:': '輸入網址',
		'Enter the YouTube video URL or ID:': '輸入 YouTube 網址或影片编號',
		'Insert a Quote': '插入引用',
		'Invalid YouTube video': '無效的YouTube影片',

		dateFormat: 'year-month-day'
	};
})();

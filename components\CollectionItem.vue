<template>
  <!-- eslint-disable vue/no-v-html -->
  <div class="collection-item-container" :class="{ 'no-border': noBorder }">
    <!-- 顶部: 文章信息 -->
    <div class="flex">
      <!-- 图片 -->
      <div
        v-if="gid !== 42"
        class="radius-5 left-banner hover horizontal"
        :class="{ vertical: mode === 'vertical' }"
        :style="{ 'background-image': `url(${banner})` }"
        @click="toPageById"
      >
        <span v-if="hasIcon" class="lv-icon fs-xs lv-0">
          {{ $t('components.collection_item.collection') }}
        </span>
      </div>
      <div class="cneter-infos flex-1 flex flex-cols jc-between">
        <!-- 标题 -->
        <div class="flex-1">
          <p class="text-hide-1 collection-title hover fs-md" :title="title" @click="toPageById">
            {{ title }}
          </p>
        </div>

        <!-- 收藏日期 + 底部信息 -->
        <div class="btm-infos fs-xs text-gray">
          <div class="info-date">
            <span class="mar-right-25">
              {{ date }}
            </span>
            <span class="mar-right-25">
              {{ author }}
            </span>
          </div>
          <!-- 底部信息 -->
          <div v-if="isSeries" class="mar-top-20 infos-counter">
            <div class="flex">
              <!-- 查看次数 -->
              <div class="counter-item">
                <svg class="svg-icon views" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 730.49 426.84">
                  <path
                    d="M365.22,384.28c-94.2,0-170.85-76.64-170.85-170.85S271,42.58,365.22,42.58s170.85,76.64,170.85,170.85S459.44,384.28,365.22,384.28Zm0-303.06c-72.89,0-132.2,59.31-132.2,132.21s59.31,132.2,132.2,132.2,132.21-59.31,132.21-132.2S438.14,81.22,365.22,81.22Z"
                    transform="translate(-0.01 -0.01)"
                  />
                  <path
                    d="M374,426.85C174.52,426.85,56,308.08,14.22,257h0a62.65,62.65,0,0,1-1.08-78.12C54.56,125.09,171.67,0,364.49,0,558.63,0,675.94,126.32,717.38,180.63a62.79,62.79,0,0,1,1.34,75C681.87,307.1,574.53,426.85,374,426.85ZM44.15,232.55C82.38,279.36,191.07,388.21,374,388.21c182.61,0,279.94-108.46,313.3-155.1a24.32,24.32,0,0,0-.62-29.06c-38-49.73-145.3-165.41-322.17-165.41-175.64,0-282.79,114.55-320.72,163.8a24.16,24.16,0,0,0,.38,30.11Z"
                    transform="translate(-0.01 -0.01)"
                  />
                </svg>
                <span>{{ views }}</span>
              </div>

              <!-- 评论列表 -->
              <div class="counter-item">
                <svg class="svg-icon comments" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 624 447.5">
                  <path
                    d="M515.47,7.24h-407A101.39,101.39,0,0,0,7.24,108.51V339A101.4,101.4,0,0,0,108.48,440.25h407A101.41,101.41,0,0,0,616.76,339V108.51A101.4,101.4,0,0,0,515.47,7.24ZM591.36,339a76,76,0,0,1-75.89,75.89h-407A76,76,0,0,1,32.63,339V108.51a76,76,0,0,1,75.84-75.87h407a76,76,0,0,1,75.89,75.87Z"
                  />
                  <path
                    d="M515.48,447.5h-407A108.64,108.64,0,0,1,0,339V108.51A108.62,108.62,0,0,1,108.48,0h407A108.64,108.64,0,0,1,624,108.51V339A108.66,108.66,0,0,1,515.48,447.5Zm-407-433a94.11,94.11,0,0,0-94,94V339a94.12,94.12,0,0,0,94,94h407a94.15,94.15,0,0,0,94-94V108.51a94.12,94.12,0,0,0-94-94Zm407,407.56h-407A83.2,83.2,0,0,1,25.4,338.92V108.51a83.2,83.2,0,0,1,83.08-83.12h407a83.23,83.23,0,0,1,83.11,83.12V339A83.24,83.24,0,0,1,515.48,422.06Zm-407-382.21a68.69,68.69,0,0,0-68.59,68.62V339a68.71,68.71,0,0,0,68.59,68.65h407A68.72,68.72,0,0,0,584.13,339V108.51a68.71,68.71,0,0,0-68.65-68.62Z"
                  />
                  <circle cx="151.32" cy="115.79" r="26.04" />
                  <path d="M498.72,139.94H231.31a24.16,24.16,0,0,1,0-48.31H498.72a24.16,24.16,0,0,1,0,48.31Z" />
                  <circle cx="151.32" cy="223.02" r="26.04" transform="translate(-81.87 358.15) rotate(-85.93)" />
                  <path d="M439,247.9h-.08l-207.66-.72a24.16,24.16,0,0,1,.07-48.31h.08l207.66.72A24.16,24.16,0,0,1,439,247.9Z" />
                  <path d="M303,354.41H231.31a24.16,24.16,0,0,1,0-48.31H303a24.16,24.16,0,0,1,0,48.31Z" />
                </svg>
                <span>{{ comments }}</span>
              </div>

              <!-- 投币 -->
              <div class="counter-item">
                <svg class="svg-icon coins" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 755 752.98">
                  <path
                    d="M643.67,109.3C497.93-36.44,255-36.44,109.3,109.3s-145.73,388.63,0,534.37,388.63,145.73,534.37,0S794.8,255,643.67,109.3ZM465.55,470.94A575.48,575.48,0,0,0,408.77,537a38.21,38.21,0,0,1-61,1.11c-17.48-22.4-38-44.8-60.37-67.2-22.54-22.53-45.08-43.15-67.62-60.7-20.15-15.7-19.34-46.35,1.44-61.21a524.78,524.78,0,0,0,66.18-56.2,709.47,709.47,0,0,0,59.29-68.1,38.2,38.2,0,0,1,61.18.82,574.71,574.71,0,0,0,57.65,67.28,575.56,575.56,0,0,0,65.18,56.11,38.21,38.21,0,0,1,0,61.48,415.61,415.61,0,0,0-65.23,60.52Z"
                    transform="translate(-0.01 0)"
                  />
                </svg>
                <span>{{ coins }}</span>
              </div>

              <!-- 收藏 -->
              <div class="counter-item">
                <svg class="svg-icon collections" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 752.69 721.35">
                  <path
                    d="M376.51,0a53.94,53.94,0,0,0-49.3,30.64l-70.55,143a68.66,68.66,0,0,1-51.72,37.58L47.2,234.09a55,55,0,0,0-30.47,93.73L130.88,439.13a68.65,68.65,0,0,1,19.75,60.79L123.63,657a55,55,0,0,0,79.77,58l141.15-74.22a68.73,68.73,0,0,1,63.92,0L549.57,715a55,55,0,0,0,79.77-58l-27-157.12a68.68,68.68,0,0,1,19.76-60.79L736.3,327.82a55,55,0,0,0-30.48-93.77L548.08,211.17a68.67,68.67,0,0,1-51.71-37.57l-70.56-143A53.94,53.94,0,0,0,376.51,0Z"
                    transform="translate(-0.19 0)"
                  />
                </svg>
                <span>{{ collections }}</span>
              </div>

              <!-- 分享 -->
              <div class="counter-item">
                <svg class="svg-icon share" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 767.75 576.45">
                  <path
                    d="M728.75,167,457.81,10.57c-52-30-117,7.5-117,67.54v60.61C157.21,164.89,81.12,248.49,37.23,334.64a324.63,324.63,0,0,0-29.9,90.2c-1.87,10.45-2.73,7.82-5.86,39.53-3.53,35.87-.42,62.29,6.34,92.58,5.2,23.37,28.45,26.46,38.67,5.37l2.21-4.54c51-115.45,168.75-204.44,292.15-213V391c0,60,65,97.57,117,67.55l271-156.43C780.74,272.07,780.74,197,728.75,167Z"
                    transform="translate(0 -0.01)"
                  />
                </svg>
              </div>

              <!-- 点赞 -->
              <div class="counter-item">
                <svg class="svg-icon zan" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 596.85 626.72">
                  <path
                    d="M54.78,192.55C24.5,192.55,0,221.16,0,256.45v297.9c0,35.29,24.54,63.9,54.82,63.9s54.83-28.61,54.83-63.9V256.45C109.65,221.16,85.1,192.55,54.78,192.55Z"
                    transform="translate(0.04 0)"
                  />
                  <path
                    d="M509.31,213.62H351l39.23-96.08C406.18,78.42,384.18,0,331.11,0c-34.22,0-64.5,17.17-74.81,42.41L165.2,198.66a64.09,64.09,0,0,0-8.72,32.27V559.32c0,37.23,39.15,67.4,87.45,67.4h197c42,0,78.1-23,85.92-54.85l68.41-278.3C605.43,252.06,564.13,213.62,509.31,213.62Z"
                    transform="translate(0.04 0)"
                  />
                </svg>
                <span>{{ zans }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-btns flex ai-center jc-end">
        <button v-if="showEditBtn" class="btn btn-primary" @click="$emit('edit-btn-click')">
          {{ $t('components.btn_edit') }}
        </button>
        <button v-if="showDeleteBtn" class="btn mar-left-20 btn-primary" @click="$emit('delete-btn-click')">
          {{ $t('components.btn_delete') }}
        </button>
      </div>
    </div>

    <!-- ---------------------- 底部: 合集目录 ---------------------- -->
    <div v-if="articles.length" class="page-list-container flex">
      <!-- 左边图片占位 -->
      <div class="left-holder horizontal" :class="{ vertical: mode === 'vertical' }"></div>

      <!-- 中间内容 -->
      <div class="cneter-infos page-index flex-1">
        <draggable v-model="articles" :move="onMove" @change="move">
          <div v-for="(item, key) in articles" :key="key" class="page-item ai-center flex">
            <span class="fs-md">P{{ key + 1 }}</span>
            <div class="index-sort flex ai-center jc-center flex-cols">
              <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 529.5 411.43">
                <path
                  d="M653.9,514,447.4,218.8c-23.1-33.1-72.1-33.1-95.3,0L145.6,514c-27,38.5.6,91.4,47.6,91.4H606.3C653.3,605.5,680.8,552.6,653.9,514Z"
                  transform="translate(-134.98 -193.98)"
                />
              </svg>
              <svg class="svg-icon down" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 529.5 411.43">
                <path
                  d="M653.9,514,447.4,218.8c-23.1-33.1-72.1-33.1-95.3,0L145.6,514c-27,38.5.6,91.4,47.6,91.4H606.3C653.3,605.5,680.8,552.6,653.9,514Z"
                  transform="translate(-134.98 -193.98)"
                />
              </svg>
            </div>
            <p class="text-hide-1 title hover flex-1" :title="title" @click="toPage(`/detail/${item.aid}`)" v-html="item.title"></p>
            <div class="btn-item text-primary flex-inline ai-center hover jc-center" @click="toPage(`/write/${item.aid}`)">
              <span>{{ item.order }}</span>
              {{ $t('components.btn_edit') }}
            </div>
          </div>
        </draggable>
      </div>
    </div>

    <!-- 底部边框线上的按钮 -->
    <div v-if="pageIndexs.length > 3" class="has-more">
      <button v-if="folding" class="btn open-btn flex ai-center jc-center text-primary" @click="openFolding">
        <span class="mar-right-5">{{ $t('components.btn_open') }}</span>
        <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 740.47 440.2">
          <path
            d="M400,518.6,97.8,192.5a38.31,38.31,0,0,0-56.8,0c-14.9,16.1-14.9,42.2,0,58.3L371.6,607.5a38.31,38.31,0,0,0,56.8,0L759,250.8a43.25,43.25,0,0,0,0-58.3,38.42,38.42,0,0,0-56.9,0Z"
            transform="translate(-29.83 -179.9)"
          />
        </svg>
      </button>

      <!-- 底部边框线上的按钮 -->
      <button v-else class="btn open-btn flex ai-center jc-center text-primary" @click="foldUp">
        <span class="mar-right-5">{{ $t('components.btn_close') }}</span>
        <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 740.47 440.2">
          <path
            class="cls-1"
            d="M400,281.4,97.8,607.5a38.31,38.31,0,0,1-56.8,0c-14.9-16.1-14.9-42.2,0-58.3L371.6,192.5a38.31,38.31,0,0,1,56.8,0L759,549.2a43.25,43.25,0,0,1,0,58.3,38.42,38.42,0,0,1-56.9,0Z"
            transform="translate(-29.83 -179.9)"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
export default {
  components: {
    draggable
  },
  props: {
    sid: {
      // 合集id
      type: Number,
      default: 0
    },
    gid: {
      // 帖子所属分区id
      type: Number,
      default: 0
    },
    mode: {
      // 封面模式(横排|竖排) horizontal | vertical
      type: String,
      default: 'horizontal'
    },
    hasIcon: {
      type: Boolean,
      default: false
    },
    isSeries: {
      type: Boolean,
      default: true
    },
    banner: {
      // banner图片地址
      type: String,
      // required: true,
      default: ''
    },
    title: {
      // 标题
      type: [String, Number],
      // required: true,
      default: ''
    },
    date: {
      // 收藏日期
      type: [String, Number],
      default: ''
    },
    author: {
      // 作者
      type: [String, Number],
      default: ''
    },
    views: {
      // 浏览次数
      type: [String, Number],
      default: 0
    },
    comments: {
      // 评论数
      type: [String, Number],
      default: 0
    },
    coins: {
      // 投币数量
      type: [String, Number],
      default: 0
    },
    collections: {
      // 收藏数量
      type: [String, Number],
      default: 0
    },
    shares: {
      // 分享数量
      type: [String, Number],
      default: 0
    },
    zans: {
      // 点赞数量
      type: [String, Number],
      default: 0
    },
    showEditBtn: {
      // 是否显示编辑按钮
      type: Boolean,
      default: false
    },
    showDeleteBtn: {
      // 是否显示删除按钮
      type: Boolean,
      default: true
    },
    pageIndexs: {
      // 底部目录索引
      type: Array,
      default: () => []
    },
    // hasMorePages: {
    //   // 是否显示 "展开" 按钮
    //   type: [Boolean, Number],
    //   default: true
    // },
    noBorder: {
      // 不需要边框
      type: Boolean,
      default: false
    }
  },

  data: () => ({
    // 是否收起
    folding: true,
    moving: false, // 是否正在移动中
    articles: []
  }),

  created() {
    if (this.pageIndexs && this.pageIndexs.length > 0) {
      this.articles = this.pageIndexs.slice(0, 3);
    }
  },

  methods: {
    onMove() {
      return !this.moving;
    },

    move({ moved: { element, newIndex } }) {
      if (this.moving) return;
      const maxIndex = this.articles.length - 1;
      const payload = {
        sid: this.sid,
        aid: element.aid,
        before_order: 0, // 最前面
        after_order: 0 // 最后面
      };

      if (newIndex) {
        // index 不是 0
        payload.before_order = this.articles[newIndex - 1].order;
      }

      if (newIndex !== maxIndex) {
        // index 不是 maxIndex
        payload.after_order = this.articles[newIndex + 1].order;
      }

      if (payload.before_order === 0 && payload.after_order === 0) {
        this.$message.info(this.$t('tips.please_move_p2_first'));
        return;
      }

      this.moving = true;
      this.$axios
        .$post('/api/series/edit-item-order', payload)
        .then(({ data: order }) => this.$store.commit('userPublish/setArticleOrder', { ...payload, order }))
        .finally(() => (this.moving = false));
    },
    openFolding() {
      this.articles = this.pageIndexs;
      this.folding = false;
    },
    foldUp() {
      this.articles = this.pageIndexs.slice(0, 3);
      this.folding = true;
    },
    deleteArticleIndex(aid) {
      // 删除文章 && 刷新
      this.$emit('delete-page-index', aid);
    },
    // 通过id来指定跳的是公会大厅详情页还是detail页面
    toPageById() {
      if (this.gid === 42) {
        this.$emit('cover-click2');
      } else {
        this.$emit('cover-click');
      }
    }
  }
};
</script>

<style lang="scss" scope>
.collection-item-container {
  border-bottom: 1px solid $gray-white;
  &.no-border {
    border: none !important;
  }
  position: relative;
  top: 0;
  left: 0;
  padding-bottom: 20px;
  // margin-bottom: 30px;
  .collection-title {
    line-height: 1.5;
  }
  .open-btn {
    width: 100px;
    background: #fff;
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 50%);
    .svg-icon {
      fill: $primary;
      width: 13px;
    }
  }
  .left-holder {
    height: 100%;
    visibility: hidden;
    &.vertical {
      // 垂直封面
      width: 88px !important;
    }
    &.horizontal {
      // 水平封面
      width: 160px;
    }
  }
  // padding-bottom: 30px;
  .left-banner {
    position: relative;
    overflow: hidden;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    &.vertical {
      // 垂直封面
      width: 88px !important;
      height: 125px !important;
    }
    &.horizontal {
      // 水平封面
      width: 160px;
      height: 100px;
    }
    .lv-icon {
      position: absolute;
      top: 5px;
      right: 5px;
    }
  }
  .cneter-infos {
    padding: 0 15px;
    .btm-infos {
      .counter-item {
        display: inline-flex;
        align-items: center;
        margin-right: 20px;
        .svg-icon {
          margin-right: 5px;
          width: 15px;
          fill: $gray;
          // sizes
          &.coins,
          &.collections,
          &.share,
          &.zan {
            width: 13px;
          }

          // fill colors
          &.coins {
            fill: #fadd52;
          }
          &.collections {
            fill: #b6ed6f;
          }
          &.share {
            fill: #69cff9;
          }
          &.zan {
            fill: #ffb236;
          }
        }
      }
    }
  }

  // 右边按钮
  $btnsboxw: 160px;
  $btnw: 70px;
  .right-btns {
    width: $btnsboxw;
    .btn {
      width: $btnw;
      height: 30px;
      border-radius: 30px;
    }
  }

  // 底部页面的
  .page-list-container {
    .page-index {
      padding-top: 15px;
      .page-item {
        padding: 15px 0;
        .index-sort {
          margin: 0 10px;
          width: 10px;
          &:hover {
            cursor: crosshair;
          }
          .svg-icon {
            width: 8px;
            fill: $gray;
            &.down {
              margin-top: 2px;
              transform: rotate(180deg);
            }
          }
        }
        .title {
          color: $gray;
        }
        .btns {
          width: $btnsboxw;
          .btn-item {
            width: $btnw;
          }
        }
      }
    }
  }
}
</style>

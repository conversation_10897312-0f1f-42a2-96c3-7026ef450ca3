/* eslint-disable */

//  ***** 我的自定义按钮插件js ***** //
(function(window, document, sceditor) {
  'use strict';
  sceditor.plugins.autosave = function() {
    var base = this;
    sceditor.command.set('ruby', {
      tooltip: '上标',
      exec: function(e) {
        console.log(e);
      },
    });

    sceditor.command.set('emoji', {
      tooltip: '插入表情',
      exec: function() {
        console.log('emoji');
      },
    });

    sceditor.command.set('votes', {
      tooltip: '投票',
      exec: function() {
        console.log('votes');
      },
    });

    sceditor.command.set('attch', {
      tooltip: '附件',
      exec: function() {
        console.log('attch');
      },
    });
  };
})(window, document, sceditor);

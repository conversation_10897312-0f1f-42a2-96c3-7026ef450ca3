<template>
  <div class="footer pad-top-30">
    <div class="container">
      <!-- <div class="friendship-link"> -->
      <!-- 友情鏈接： -->
      <!-- <a href="#" class="link">鬥破蒼穹漫畫</a>
        <a href="#" class="link">輕之文庫</a>
        <a href="#" class="link">喵特</a>
        <a href="#" class="link">鬥破蒼穹漫畫</a>
        <a href="#" class="link">輕之文庫</a>
        <a href="#" class="link">喵特</a>
        <a href="#" class="link">喵特</a>
        <a href="#" class="link">鬥破蒼穹漫畫</a>
        <a href="#" class="link">輕之文庫</a>
        <a href="#" class="link">喵特</a>
        <a href="#" class="link">鬥破蒼穹漫畫</a> -->
      <!-- </div> -->
      <div class="footer-bottom mar-top-30">
        {{ $t('components.footer.lk') }} Copyright©{{ year }} https://www.lightnovel.us All Rights Reserved
        <a :href="$i18n.path('agreement')" class="btm-link" target="_blank">{{ $t('components.footer.agreement') }}</a>
        <a :href="$i18n.path('siteinfo')" class="btm-link" target="_blank">{{ $t('components.footer.site_info') }}</a>
        <a :href="$i18n.path('site_rule')" class="btm-link" target="_blank">{{ $t('components.footer.site_rule') }}</a>
      </div>
      <div class="qr-code">
        <img class="" src="../assets/images/qr_code.png" alt="" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    year: new Date().getFullYear()
  })
};
</script>
<style lang="scss" scoped>
.footer {
  position: relative;
  margin-top: 50px;
  height: 170px;
  background-color: #f7f6f9;
  font-size: 13px;
  min-width: 1160px;
  overflow: hidden;
  .container {
    position: relative;
    width: 825px;
    margin: 0 auto;
    .friendship-link {
      margin-top: 35px;
      width: 715px;
      color: #848484;
      word-break: keep-all;
      a.link {
        color: #848484;
        text-decoration: none;
      }
    }
    .footer-bottom {
      width: 715px;
      // margin-top: 30px;
      color: $gray;
      .btm-link {
        color: $gray;
        text-decoration: none;
        &:hover {
          color: $primary;
          text-decoration: underline;
        }
      }
    }

    .qr-code {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      width: 81px;
      height: 81px;
      img {
        width: 100%;
      }
    }
  }
}
</style>

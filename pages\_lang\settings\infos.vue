<template>
  <div>
    <!-- 裁剪头像 -->
    <!-- 裁剪图片 -->
    <el-dialog
      title=""
      width="90%"
      top="10vh"
      :fullscreen="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :visible="showCrop"
      class="crop-dialog"
    >
      <div id="crop-container">
        <div class="crop-box">
          <!-- 裁剪头像 -->
          <vue-cropper
            ref="cropper"
            mode="cover"
            :img="cropOptions.img"
            :output-size="cropOptions.outputSize"
            :output-type="cropOptions.outputType"
            :full="cropOptions.full"
            :high="cropOptions.high"
            :fixed="cropOptions.fixed"
            :can-move="cropOptions.canMove"
            :can-move-box="cropOptions.canMoveBox"
            :fixed-box="cropOptions.fixedBox"
            :original="cropOptions.original"
            :center-box="cropOptions.centerBox"
            :auto-crop="true"
            :fixed-number="cropOptions.fixedNumber"
            @real-time="realTimePreview"
          />

          <!-- 放大缩小 -->
          <div class="scale-btns">
            <button class="btn-img-scale btn btn-primary" @click="scaleImage(1)">
              {{ $t('write.scale_max') }}
            </button>
            <button class="btn-img-scale btn btn-primary border" @click="scaleImage(-1)">
              {{ $t('write.scale_min') }}
            </button>
          </div>
        </div>
        <div class="preview-box" :style="corpPreviews.div">
          <img :src="corpPreviews.url" :style="corpPreviews.img" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <button class="btn btn-primary" @click="cropImage(true)">
          {{ $t('write.btn_confirm') }}
        </button>
        <button class="btn btn-primary border" @click="cropImage(false)">
          {{ $t('write.btn_cancel') }}
        </button>
      </div>
    </el-dialog>

    <!-- 正文内容 -->
    <SettingsTopTabbar />
    <div class="form">
      <!-- 头像 -->
      <div class="form-item">
        <span class="label">{{ $t('settings_infos.avatar') }}</span>
        <span class="tips">{{ $t('settings_infos.support_imgs') }}</span>
      </div>
      <div class="form-item flex avatar-box-wrapper">
        <span class="label">&nbsp;</span>
        <div class="avatar-box">
          <div class="avatar-item  mar-right-30">
            <div class="img-size flex ai-center jc-center avatar-add-btn hover">
              <el-upload
                :action="uploadURL"
                :data="uploadImageJSONData"
                :before-upload="beforeAvatarUpload"
                :on-success="avatarUploadSuccess"
                :on-error="avatarUploadError"
                :show-file-list="false"
                :multiple="false"
                accept="image/*"
              >
                <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 626.7 626.7">
                  <path
                    d="M646.4,86.9H154.1a67.28,67.28,0,0,0-67.2,67.2V646.4a67.28,67.28,0,0,0,67.2,67.2H646.4a67.28,67.28,0,0,0,67.2-67.2V154.1A67.28,67.28,0,0,0,646.4,86.9ZM154.1,134H646.4a20.1,20.1,0,0,1,20.1,20.1V496.6L580.2,330.4c-16.8-32.4-61.9-35.7-83.3-6L415.8,437.3a25.15,25.15,0,0,1-34.1,6.5l-76.9-49.2a78.68,78.68,0,0,0-107,21.4L134,508V154.1A20.1,20.1,0,0,1,154.1,134Z"
                    transform="translate(-86.9 -86.9)"
                  />
                </svg>
              </el-upload>
            </div>
            <p class="fs-sm text-center mar-top-10 tips">{{ $t('settings_infos.choose_image') }}</p>
          </div>
          <div class="avatar-item">
            <img class="img-size" :src="userModel.avatar" />
            <p class="fs-sm text-center mar-top-10 tips">{{ $t('settings_infos.current_avatar') }}</p>
          </div>
        </div>
      </div>

      <!-- 昵称 -->
      <!-- <div class="form-item flex ai-center">
        <span class="label">{{ $t('settings_infos.nickname') }}</span>
        <input v-model="userModel.nickname" type="text" disabled class="input-control" :placeholder="$t('settings_infos.input_nickname')" />
        <span class="tips">{{ 12 - userModel.nickname.length }}</span>
      </div> -->

      <!-- 性别 -->
      <div class="form-item flex ai-center">
        <span class="label">{{ $t('settings_infos.gender') }}</span>
        <span :class="{ selected: userModel.gender === 1 }" class="checkbox hover dis-select tips mar-right-10" @click="userModel.gender = 1">
          {{ $t('settings_infos.gender_nan') }}
        </span>
        <span :class="{ selected: userModel.gender === 2 }" class="checkbox hover dis-select tips" @click="userModel.gender = 2">
          {{ $t('settings_infos.gender_nv') }}
        </span>
      </div>

      <!-- 签名 -->
      <div class="form-item flex ai-center">
        <span class="label">{{ $t('settings_infos.sign') }}</span>
        <input v-model="userModel.sign" type="text" maxlength="30" class="input-control" :placeholder="$t('settings_infos.input_sign')" />
        <span class="tips">{{ 30 - userModel.sign.length }}</span>
      </div>

      <!-- 确定 -->
      <div class="form-item flex ai-center">
        <span class="label"></span>
        <button class="btn btn-submit btn-primary" @click="submit">{{ $t('settings_password.confirm_update') }}</button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import SettingsTopTabbar from '@/components/SettingsTopTabbar';

export default {
  layout: 'settings',
  middleware: 'auth',
  components: {
    SettingsTopTabbar
  },

  data: () => ({
    userModel: {
      avatar: '',
      nickname: '',
      gender: '',
      sign: ''
    },
    loading: false, // 是否正在上传头像
    showCrop: false, // 是否显示裁剪dialog
    corpPreviews: {}, // 裁剪的预览数据
    cropedFile: null, // 裁剪完之后的 File 对象
    cropOptions: {
      img: '', // 要裁剪的图片地址
      outputType: '', // 图片裁剪后类型[jpeg|png]
      outputSize: 0.5, // 图片裁剪后质量
      size: 1, // 图片裁剪质量 0 - 1
      canMove: true, // 是否能移动图片
      canMoveBox: true, // 是否能移动裁剪框
      fixed: true, // 固定缩放比例
      fixedBox: false, // 是否固定裁剪框大小
      original: true, // 裁剪后图片按照原始比例渲染
      fixedNumber: [1, 1], // 缩放比例
      autoCropWidth: 210, // 裁剪框的宽度
      autoCropHeight: 295, // 裁剪框的高度
      centerBox: true, // 裁剪框是否被限制在图片里面
      full: true, // 是否输出原图比例的截图
      high: false // 	是否按照设备的dpr 输出等比例图片
    },
    uploadFormData: { md5: '', security_key: '' } // 上传图片需要的参数
  }),

  computed: {
    ...mapGetters('login', ['loginUser']),

    // 上传头像图片地址
    uploadURL() {
      return `${process.env.VUE_APP_UPLOAD_URL}/upload/avatar`;
    },

    // 上传需要的额外数据
    uploadImageJSONData() {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.uploadFormData.security_key = this.loginUser.security_key;
      const data = this.apiParamsGenerator(this.uploadFormData, true);
      return data;
    }
  },

  watch: {
    loginUser(user) {
      if (user) this.userModel = { ...this.userModel, ...user };
    }
  },

  created() {
    if (this.loginUser) this.userModel = { ...this.userModel, ...this.loginUser };
  },

  methods: {
    // 提交数据
    async submit() {
      const keys = ['avatar', 'gender', 'sign'];
      const data = {};
      for (const key of keys) {
        if (this.loginUser[key] !== this.userModel[key]) {
          data[key] = this.userModel[key];
        }
      }
      const res = await this.$axios.$post('/api/user/set-info', data);
      if (res.code === 0) {
        this.$message.success(this.$t('tips.update_success'));
        this.$store.dispatch('login/refreshUserinfo');
      }
    },

    // 上传头像之前
    beforeAvatarUpload(file) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        this.uploadFilename = file.name;
        this.cropOptions.outputType = file.type.split('/').pop();

        // 是否超过允许的最大大小 10M
        if (file.size > 10485760) {
          this.$message.error(this.$t('write.file_too_max'));
          return reject(file);
        }

        // 将文件对象转为base64 对象 然后将开启图片裁剪 dialog
        const fileBase64 = await this.file2base64(file);
        this.toggleCropDialog(true, fileBase64);

        // 如果 showCrop 为 false 裁剪结束了
        this.$watch('showCrop', async (val) => {
          if (val) return;
          if (!this.cropedUploadConfirm) {
            return reject(file);
          }

          // 显示loading && 压缩图片(如果大于1MB就压缩到1M以下)
          this.loading = true;
          const minFile = await this.imageCompression(this.cropedFile);

          // 获取文件md5 ,组合数据上传文件
          const md5 = await this.getFileMD5(minFile);
          this.uploadFormData.md5 = md5;
          return resolve(minFile);
        });
      });
    },

    // 头像上传成功
    avatarUploadSuccess(res) {
      this.loading = false;
      if (res.code === 0) {
        this.userModel.avatar = res.data.res_url;
      }
    },

    // 头像上传失败
    avatarUploadError() {
      this.loading = false;
    },

    // 裁剪图片: 获得裁剪后的图片 blob 对象
    cropImage(confirm) {
      if (confirm) {
        const minWidth = 100;
        if (this.corpPreviews.w < minWidth) {
          this.$message.error(this.$t('write.min_width') + minWidth);
          return;
        }
      }

      this.$refs.cropper.getCropBlob((blob) => {
        this.toggleCropDialog(false);
        this.cropedFile = new window.File([blob], this.uploadFilename, {
          type: blob.type
        });
        this.cropedUploadConfirm = confirm;
      });
    },
    // 裁剪实时预览
    realTimePreview(preview) {
      this.corpPreviews = preview;
    },
    // 切换裁剪图片的 dialog 隐藏和显示
    toggleCropDialog(show, file) {
      this.cropOptions.img = file || '';
      this.showCrop = show;
    },
    // 放大+缩小裁剪图片的操作
    scaleImage(num) {
      this.$refs.cropper.changeScale(num || 1);
    }
  },

  head() {
    const title = this.$t('settings_infos.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
@import '@/assets/scss/settings.scss';
</style>

<template>
  <div class="medal-wall-container">
    <h4 class="medal-wall-title">{{ $t('medal_center.medal_mission') }}</h4>
    <el-row v-if="missions.length > 0" :gutter="16" justify="space-between">
      <el-col v-for="mission in missions" :key="mission.id" :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="medal-mission-container">
        <el-card shadow="hover" :body-style="{ padding: '12px 10px' }">
          <medal-steps :active="mission.currentStep">
            <medal-step
              v-for="step in mission.steps"
              :id="step.id"
              :key="step.step"
              :name="step.name"
              :image-url="step.imageUrl"
              :description="step.description"
              :status="step.status"
            />
          </medal-steps>
        </el-card>
      </el-col>
    </el-row>
    <Loading v-if="missions.length === 0" />
  </div>
</template>

<script>
import Loading from '@/components/Loading';
import MedalSteps from '@/components/medal/MedalSteps';
import MedalStep from '@/components/medal/MedalStep';

export default {
  name: 'MedalMission',
  components: {
    MedalSteps,
    MedalStep,
    Loading
  },
  data() {
    return {
      missions: []
    };
  },
  mounted() {
    this.getMissions();
  },
  methods: {
    getMissions() {
      this.$axios.$post('/api/medal/list', { type: 1 }).then((res) => {
        this.missions = [];
        if (res.code === 0) {
          res.data.list.forEach((missions, k) => {
            let currentStep = 0;
            const steps = [];
            missions.items.forEach((mission, kk) => {
              if (mission.status === 2) currentStep = kk + 1;
              steps.push({
                id: mission.id,
                step: kk + 1,
                imageUrl: mission.img,
                name: mission.name,
                description: mission.desc,
                status: mission.status
              });
            });

            this.missions.push({ id: `mission-${k}`, currentStep, steps });
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scope>
.medal-wall-container {
  border-radius: 2px;
  background: $white;
  padding: 20px;
  margin: 13px 10px;
  min-height: 45vh;
  .loading-container {
    min-height: 45vh;
  }
}
.medal-wall-title {
  font-size: 18px;
  font-weight: 400;
  color: $dark;
  padding-bottom: 18px;
}
.medal-mission-container {
  margin-bottom: 20px;
}
</style>

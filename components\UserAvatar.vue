<template>
  <div class="user-avatar-container flex jc-center ai-center" :style="getStyle()">
    <img class="avatar" :src="avatarImg" :style="getStyle(false)" />
    <img v-if="borderImg" class="border" :src="borderImg" :style="getStyle()" />
  </div>
</template>

<script>
export default {
  props: {
    avatarImg: {
      type: String,
      required: true,
      default: ''
    },
    borderImg: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 60
    },
    pad: {
      type: Number,
      default: 5
    }
  },
  methods: {
    getStyle(flag = true) {
      const width = flag ? `${this.width}px` : `${this.width - this.pad}px`;
      return {
        width,
        height: width
      };
    }
  }
};
</script>

<style lang="scss" scope>
.user-avatar-container {
  border: 1px solid $white-fa;
  border-radius: 50%;
  position: relative;
  top: 0;
  left: 0;
  .border {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
  }
  .avatar {
    overflow: hidden;
    border-radius: 50%;
  }
}
</style>

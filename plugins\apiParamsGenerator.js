/* eslint-disable */
import Vue from 'vue';
// import { JSEncrypt } from 'js-rsa-lh5';
// import CryptoJS from 'crypto-js';

export default ({ store }) => {
  /**
   * 生成接口需要的参数
   * @param data
   * @param isUpload
   * @returns {{gz: number, d, is_encrypted: number, sign: string, client: string, platform: string}}
   */
  Vue.prototype.apiParamsGenerator = (data = {}, url = '') => {
    const params = {
      is_encrypted: 0,
      platform: 'pc',
      client: 'web',
      sign: '',
      gz: 0,
      d: { ...data }
    };

    // 处理 security_key
    const token = store.state.login.token;
    if (token) {
      params.d.security_key = token.security_key;
    }

    // 调试输出
    if (process.browser && JSON.parse(process.env.APP_DEBUG)) {
      console.log(`====================|请求 ${url} |====================`);
      console.log({ ...params.d });
    }

    // 如果是上传
    if (typeof url === "boolean" && url) {
      params.d = JSON.stringify(params.d);
    }

    return params;

    /*
    // 处理加密
    const key = CryptoJS.enc.Utf8.parse('1E5D7CA693B8420F');
    const iv = CryptoJS.enc.Utf8.parse('5C8EBD7031F9462A');
    const options = {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    };
    params.d = CryptoJS.AES.encrypt(JSON.stringify(params.d), key, options).toString();

    // 生成签名用的私钥
    //     const privateKey = `-----BEGIN RSA PRIVATE KEY-----
    // MIICWwIBAAKBgQCJR711bnP1PUwvxqiBQ82FCproM3wqRteejLXXHFhmI3CB6BJl
    // Xf5tueuvr/2mf9pFljgbmjXeDVeAoeRsuBvKW9WuND5JaJEeUflRojsEaqRnbDEv
    // 4UALe9az8BFqsk+rNBpgGaTrtxwVnUj5dH0JNSvNBtcJrmprbApKLC+TRQIDAQAB
    // AoGAFTkfPAI9RhWEa5cZojutEHDhHZ3RXSiCRCjlIeI9c7OYkf93/Jjem/Bpf0nv
    // e71PWCu5n+F5i3XiPb40NhnzyFzRVBrSz4Wqw28vp1LrAH5xTGajtn/PeuOg5gbd
    // HiycMuc3nvJqSqgYNN7hDYlvY6eoC3x4cUlanwGS1T+q0V8CQQCliWMKHl6YMHiG
    // IGwqutlHF4u7Gn68CTuZOectpX/xnPvmNym9XyC/Op62VnTbec3/3KAJ8/iFH8/U
    // 8rnEzQWzAkEA1E1Fxk9XyEFj0kvoJK4fb/8LEZnh87Zq37MU0XfyghPChquKf5CA
    // Q+7uvr3ACuewJpUBcKDF2IczilhBi7z3JwJABcQ6cgDK1MxjdnCss/RcW1Cjqnnq
    // zkwYCmds+TcZtOX+FG/5gUJOFrtJChsK6RZeKbqazJ32G1UXXVizRfrKZwJAGmk2
    // ifIq8H26BlptGcsTnCI1ZU4ofuItr4Ce0WRDpg0BQTGr/Cyk2E6sIsyGTJUAI/yb
    // fkBx/HrtXYOXO+QZNwJAASMtMEwZQE5wqjHl6VpEngLbGWmbdr4NRibt5weW7ZTA
    // iJDDkn2AZDaWWF6YbflFP3CxEb2cVwNrgCD0S3SIRA==
    // -----END RSA PRIVATE KEY-----`;

    // rsaKey: aes 加密后的 rsa 私钥字符串
    // secret: 通过 sha256(私钥) 生成, 解密 rsaKey 需要的参数
    // 因为前端可以通过断点去调试代码, 为了防止私钥被窃取, 不能直接使用私钥去签名, 而是要每次 aes 解密出私钥
    const rsaKey = `U2FsdGVkX1+MtR8WKTV2RArQMyDbaecrDfGg8lftBWFSMakJoouhmGKxsr0wKjn36IFgvyg8Cmozip8CfgKlVMYVpv/I1LQ05rotyt2kX/tFSzxsl+OuZ8RZ5KZYKncSVjYXSC3FgLDz15PFH6ceJwJecst1idSbHoCgeOCedAP5Tqq2NPiOggwh7KfDND6UWj1paBO7oUqWGuS4U12vDR7rfG+L4FEINM0kTksgAcOUaoFJIJpZnSu/FCEN85RvB3yekrhC2Jg5l4lEfn0Ew1Jjn2RMpw/LM/8qi83uGk14RAM+bLK70ZsgTzoEySjxhCQ0BlTnSuN8L/ETEMO50J98EgybhwFlH/CUpkWX6m8+2oUqpT5eSl2dDbt4PDSVGD023nHaDXlY2BXSPrHGBUJ3+rOPZfhTwbZ14g9OGMbnhiIfgt+x3Up/6Z/QN/lDXWnYqL5JOwTyZNSYENkg1MGRSZKXQw3fVoE9+ZUy19AdzC1x6/cFOT7WO4JgHLEzCG8YgppfNi4fo9BuTQvfbTUbeJGlUwSZB7kPubz519Yg68fhDZEbOX1qHOYjUJo6DDH3WxZrBDCiQ3b2lNkKBxNM58N8Jmvtu7pz6aG/3DlLyoiQhMn5of5rGQaj2V9zQn4A7JBJ9ANNoly0/fNwocigF6gvUoQqbwFNXVzvxrZ7M6TFfyoXugCpN2utC4OFQPKgkXWlFa3MPNLgpy5r6k0pCuCd6M0qstbj8gEqfM67xsBVTh9va0SnJ59WZEVYISNPyldsfGMVHgGu9pH8arZBJ4IiSTeZc7VKjPQsEuQv2J8ChlHcwvwrS97ftAa1VcHKQiHt9jUIHwNBIxrInROmC/PgNsxezlf6gNoRHlNAlvSKnCZWYLna2ueLO0x9n2ovBxpw5fedcmhx4j5GiLwPxPyGoP5l982KN6o2WnEBjSTiPJCYdn47BvotRe6jOser+vLvig+NOUHu+ABGsqO50MzdulLaXQMAEf0auuTC3sZPezjJxScyvRfl+vHx3aZll6ZCU34uUZnntu26VZuOz2aQVPgstiEiu5M2bRlgaITM2PhWlF/R65+KjUQt41dnNdLS4oPKxRn1CkFFQG8MSIVa8lX+Niorb4lu1RD2VNR3tDt9rZl7DsbMmeY3zKOoAlXhFAoriHnaLuYvwSsQkcffZdOC3T6bAdu0Lt8kVrVGmT6GhnL5tZUCh6GT`;
    const secret = 'a1451fc0e3a2431a6857eca9f8fc8e0cddb91d866ab79a1d53dc8ff1fc729233';

    // 处理签名
    const rsa = new JSEncrypt();
    rsa.setPrivateKey(CryptoJS.AES.decrypt(rsaKey, secret).toString(CryptoJS.enc.Utf8));
    const signData = params.is_encrypted ? params.d : JSON.stringify(params.d);
    params.sign = rsa.sign(signData, CryptoJS.SHA256, 'sha256');

    return params;
    */
  };
};

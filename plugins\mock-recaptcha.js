// Mock reCAPTCHA V3 for development/testing
export default ({ app }, inject) => {
  if (process.client) {
    // 检查是否已经加载了真实的 grecaptcha
    if (!window.grecaptcha) {
      console.log('Loading mock reCAPTCHA...');
      
      // 模拟 grecaptcha 对象
      window.grecaptcha = {
        ready(callback) {
          // 立即执行回调，模拟 reCAPTCHA 已准备就绪
          console.log('Mock reCAPTCHA ready');
          setTimeout(callback, 100);
        },
        
        execute(sitekey, options) {
          // 生成模拟 token
          const mockToken = 'mock_token_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
          console.log('Mock reCAPTCHA execute called:', {
            sitekey,
            action: options.action,
            token: mockToken
          });
          
          // 返回 Promise，模拟异步行为
          return Promise.resolve(mockToken);
        }
      };
      
      console.log('Mock reCAPTCHA loaded successfully');
    }
  }
};

<template>
  <div class="wrap">
    <Nav />
    <div class="layout-container">
      <nuxt />
    </div>
    <Footer />
  </div>
</template>

<script>
import Nav from '../components/Nav.vue';
import Footer from '../components/Footer.vue';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  components: { Nav, Footer },
  mounted() {
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  }
};
</script>
<style lang="scss" scoped>
.wrap {
  width: 100%;
  min-width: 1200px;
  margin: 0 auto;
  background-color: white;
}
</style>

<template>
  <div class="msg-comment-reply" @scroll="onMsgContainerScroll">
    <p class="top-title fs-md">{{ $t('msg_index.comment_about_me') }}</p>
    <ul class="messages">
      <li v-for="(item, key) in messages" :key="key" class="message-item flex flex-cols">
        <!-- 消息顶部: 消息内容 -->
        <div class="flex">
          <nuxt-link target="_blank" class="link" :to="$i18n.path(`profile/${item.user_info.uid}`)">
            <user-avatar :avatar-img="item.user_info.avatar" />
          </nuxt-link>
          <div class="msg-conents">
            <!-- 回复按钮 -->
            <span v-if="item.unread === 1" class="text-primary span-reply">{{ $t('msg_index.span_reply') }}</span>
            <button class="btn btn-reply text-primary" @click="showReplyInput(item)">
              {{ $t('msg_index.btn_reply') }}
            </button>
            <!-- 用户名 -->
            <div class="name-wrapper">
              <p class="author-user-container fs-sm">
                <nuxt-link target="_blank" class="gray-text link" :to="$i18n.path(`profile/${item.user_info.uid}`)">
                  {{ item.user_info.nickname }}
                </nuxt-link>
                <span class="lv-icon mar-right-10" :class="[`lv-${item.user_info.level.level}`]">{{ item.user_info.level.name }}</span>
                <el-image
                  v-if="item.user_info.medals.length > 0"
                  :src="item.user_info.medals[0].img"
                  :title="item.user_info.medals[0].name"
                  fit="contain"
                  class="user-medal-image user-medal-imgbox mar-right-10"
                ></el-image>
              </p>
              <p class="text-gray mar-top-10 mar-bottom-10 fs-xs">
                {{ item | getReplytime | date2short }}
              </p>
            </div>

            <div class="to-comment" @click="toComment(item)">
              <!-- 回复内容 -->
              <p>
                {{ item | getReplyTitle }}
              </p>
              <!-- 回复的帖子名称 -->
              <p class="fs-xs text-gray reply-content">
                {{ item | getReplyContent }}
              </p>
            </div>
          </div>
        </div>
        <!-- 消息底部: 消息回复 -->
        <div v-if="currentShowReplyIdx === item" class="flex mar-top-20">
          <user-avatar :avatar-img="loginUser.avatar" />
          <div class="msg-conents">
            <CommentInput btn-box-top-pad="15px" :show-image-btn="false" :islogin="isLogin" @confirm="sendReply($event, item)" />
          </div>
        </div>
      </li>

      <!-- loading -->
      <li v-if="loading" class="message-item">
        <Loading />
      </li>
    </ul>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import UserAvatar from '@/components/UserAvatar.vue';
import CommentInput from '@/components/CommentInput.vue';
import Loading from '@/components/Loading.vue';

export default {
  layout: 'messages',
  middleware: ['auth'],
  components: {
    UserAvatar,
    CommentInput,
    Loading
  },

  filters: {
    getReplytime(msg) {
      return msg.time;
    },
    getReplyTitle(msg) {
      return msg.content;
    },
    getReplyContent(msg) {
      switch (msg.msg_type) {
        case 1:
          return msg.article_info.title;
        case 2:
        case 3:
          return msg.reply_content;
      }
    }
  },

  async fetch() {
    await this.$store.dispatch('emoji/getEmojis');
  },
  async asyncData({ $axios, store, query }) {
    const type = Number(query.type);
    if (type === 0) {
      const resGuild = await $axios.$post('/api/msg/get-guild-reply');
      // console.log(resGuild, 'resGuild');
      if (resGuild.code === 0) {
        return resGuild.data; // {messages, page_info}
      }
    } else {
      const res = await $axios.$post('/api/msg/get-reply-msg');
      // console.log(res, 'res');
      if (res.code === 0) {
        return res.data; // {messages, page_info}
      }
    }
  },

  data: () => ({
    loading: false, // 是否在加载中
    messages: [], // 回复我的消息
    page_info: {}, // 分页信息
    type: '',
    currentShowReplyIdx: 0 // 当前显示回复的消息 index
  }),

  computed: {
    ...mapGetters('login', ['isLogin', 'loginUser'])
  },
  watch: {
    $route() {
      this.type = this.$route.query.type;
    },
    async type(value) {
      let res = {};
      if (value === 0) {
        res = await this.$axios.$post('/api/msg/get-guild-reply');
      } else if (value === 1) {
        res = await this.$axios.$post('/api/msg/get-reply-msg');
      }
      // console.log(res, 'res');
      if (res.code === 0) {
        this.messages = res.data.messages;
        this.page_info = res.data.page_info;
      }
    }
  },
  methods: {
    // 去消息页
    toComment(item) {
      // console.log(item);
      if (Number(this.$route.query.type) === 0) {
        if (item.msg_type === 2) {
          this.toPage(`/themereply/${item.pid}?rid=${item.reply_rid}`);
        } else {
          this.toPage(`/themereply/${item.pid}?rid=${item.rid}`);
        }
      } else if (Number(this.$route.query.type) === 1) {
        if (item.msg_type === 1) {
          this.toPage(`detail/${item.pid}?tid=${item.tid}`);
        } else if (item.msg_type === 2) {
          this.toPage(`detail/${item.pid}?rid=${item.reply_rid}&tid=${item.reply_tid}`);
        } else {
          this.toPage(`detail/${item.pid}?rid=${item.rid}&tid=${item.tid}`);
        }
      }
    },
    // 消息容器框滑动
    onMsgContainerScroll({ target }) {
      // if (this.loading) return;
      if (Math.ceil(target.scrollTop) + target.offsetHeight === target.scrollHeight) {
        if (this.page_info.has_next) {
          if (Number(this.$route.query.type) === 0) {
            this.loading = true;
            this.$axios
              .$post('api/msg/get-guild-reply', { page: this.page_info.next })
              .then((res) => {
                if (res.code === 0) {
                  this.messages = this.messages.concat(res.data.messages);
                  this.page_info = res.data.page_info;
                }
              })
              .finally(() => (this.loading = false));
          } else {
            this.loading = true;
            this.$axios
              .$post('/api/msg/get-reply-msg', { page: this.page_info.next })
              .then((res) => {
                if (res.code === 0) {
                  this.messages = this.messages.concat(res.data.messages);
                  this.page_info = res.data.page_info;
                }
              })
              .finally(() => (this.loading = false));
          }
        }
      }
    },

    // 发送回复 | 发送回复的回复
    sendReply({ content }, message) {
      if (this.requesting) {
        return;
      }

      /* eslint-disable */
      const { tid, reply_rid, reply_uid, pid: aid, uid, rid, msg_type } = message;
      const params = {
        tid,
        content,
        aid
      };
      if (msg_type === 2) {
        params.r_uid = reply_uid;
        params.r_rid = reply_rid;
      }
      if (msg_type === 3) {
        params.r_uid = uid;
        params.r_rid = rid;
      }
      this.requesting = true;
      this.$axios
        .$post('/api/discuss/post-reply', params)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success(this.$t('tips.reply_success'));
            this.showReplyInput(-1);
          } else {
            this.$message.error(this.$t('tips.reply_fail'));
          }
        })
        .catch(() => this.$message.error(this.$t('tips.reply_fail')))
        .finally(() => (this.requesting = false));
      /* eslint-enable  */
    },

    // 显示出当前消息的回复框(如果已经显示就隐藏)
    showReplyInput(index) {
      if (this.currentShowReplyIdx === index) {
        this.currentShowReplyIdx = -1;
      } else {
        this.currentShowReplyIdx = index;
      }
    }
  },

  head() {
    const title = this.$t('messages.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
.to-comment {
  cursor: pointer;
  word-break: break-word;
}
.link {
  line-height: 1.2;
  text-decoration: none;
  color: $dark;
  &.text-gray {
    color: $gray !important;
  }
}
.user-medal-imgbox {
  width: 30px;
  height: 30px;
  vertical-align: bottom;
  overflow: unset;
  img {
    position: absolute;
    object-fit: contain;
    color: black;
    left: -5px;
    top: 4px;
  }
}
.msg-comment-reply {
  overflow-y: auto;
  height: 100%;
  .backtop {
    position: fixed;
    background: #fff;
    padding: 15px;
    width: 50px;
    height: 50px;
    bottom: 280px;
    right: 50px;
    .svg-icon {
      fill: $gray;
    }
  }
  .top-title {
    padding: 15px;
    background: #fff;
    margin-bottom: 10px;
  }
  .messages {
    max-width: 895px;
    background: #fff;
    padding: 0 15px;
    .message-item {
      padding: 20px 0;
      border-bottom: 1px solid $gray-white;
      .msg-conents {
        width: 100%;
        position: relative;
        padding-left: 15px;
        .reply-content {
          box-sizing: border-box;
          word-wrap: break-word;
          padding: 0 15px;
          margin: 10px 0px;
          line-height: 1.5;
          .shwo-more {
            width: 40px;
            text-align: right;
          }
        }
        .btn-reply {
          position: absolute;
          right: 0;
          top: 0;
        }
        .span-reply {
          position: absolute;
          right: 30px;
          top: 0;
          font-size: 13.6px;
          line-height: 18.4px;
        }
      }
    }
  }
}
</style>

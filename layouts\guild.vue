<template>
  <div class="bg-gray-f7">
    <Nav class="container-top" />
    <div class="container">
      <nuxt />
    </div>
    <Footer />
  </div>
</template>

<script>
import Nav from '../components/Nav.vue';
import Footer from '../components/Footer.vue';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  components: { Nav, Footer },
  mounted() {
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  }
};
</script>
<style lang="scss" scoped>
.bg-gray-f7 {
  .container-top {
    margin: 0 auto;
  }
  .container {
    width: 1160px;
    margin: 0 auto;
  }
}
@media screen and (max-width: 1500px) {
  .bg-gray-f7 {
    .container-top {
      margin: auto auto auto 165px !important;
    }
    .container {
      width: 1160px;
      margin: auto auto auto 180px;
    }
  }
}
@media screen and (max-width: 1380px) {
  .bg-gray-f7 {
    .container-top {
      margin: auto auto auto 165px !important;
      width: 1120px;
    }
    .container {
      width: 1090px;
      margin: auto auto auto 180px;
    }
  }
}
@media screen and (max-width: 1300px) {
  .bg-gray-f7 {
    .container-top {
      margin: auto auto auto 165px !important;
      width: 996px;
    }
    .container-top {
      /deep/ .left-nav-btns {
        .nav-language {
          min-width: 0;
        }
        .nav-search {
          margin-left: 5px;
        }
      }
    }
    .container {
      width: 965px;
      margin: auto auto auto 180px;
    }
  }
}
</style>

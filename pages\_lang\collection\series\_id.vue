<template>
  <div class="publish_mgr_container">
    <TitleTabbar :title="$t('collection.my_collections')" all-count="123" book-count="435" @tab-change="tabChange">
      <!-- 顶部更多 -->
      <template v-slot:more>
        <div v-if="showSelectAction" class="btns dis-select right-btns">
          <!-- <span class="fs-sm mar-right-10 hover" @click="selectAll">
            {{ $t('collection.select_all') }}
          </span> -->
          <span class="fs-sm mar-right-10 dis-select hover" @click="deleteFavConfirm(false)">
            {{ $t('collection.cancel_collection') }}
          </span>
          <span class="fs-sm dis-select hover" @click="toggleShowCheckbox(false)">
            {{ $t('collection.back') }}
          </span>
        </div>
        <button v-else class="btn right-btns btn-select-muilt" @click="toggleShowCheckbox(true)">
          {{ $t('collection.batch_manager') }}
        </button>
      </template>

      <!-- 内容区域: 全部 -->
      <template v-slot:articles>
        <div v-if="fav_series_articles.length">
          <div v-for="(item, key) in fav_series_articles" :key="key" class="collect-item">
            <CollectionItem
              :show-delete-btn="true"
              :is-series="false"
              :has-icon="true"
              :banner="item.cover"
              :title="item.name"
              :date="item.last_time | date2short"
              :author="item.group_name"
              :views="item.hits"
              :comments="item.comments"
              :coins="item.coins"
              :collections="item.favorites"
              :shares="item.shares"
              :zans="item.likes"
              @cover-click="toPage(`/series/${item.sid}`)"
              @delete-btn-click="deleteFavConfirm(item.sid)"
            />
            <div
              class="check-box hide"
              :class="[{ show: showSelectAction }, { checked: selectedArticles.includes(item.sid) }]"
              @click.stop="muiltSelect(item.sid)"
            >
              <svg class="check-svg-icon hover" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 605.65 425.92">
                <path
                  d="M310.2,613a35.92,35.92,0,0,0,24.1-9.7L693,244.6a33.73,33.73,0,1,0-47.7-47.7L310.6,531.4,154.7,376A33.73,33.73,0,0,0,107,423.7L286.1,602.8A33.56,33.56,0,0,0,310.2,613Z"
                  transform="translate(-97.18 -187.08)"
                />
              </svg>
            </div>
          </div>

          <div class="pagination">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="articlesLoading"
              :hide-on-single-page="true"
              :current-page="fav_series_articles_paginate.cur"
              :total="fav_series_articles_paginate.count"
              :page-size="fav_series_articles_paginate.size"
              @current-change="articlePageChange"
            />
          </div>
        </div>

        <!-- 如果没有数据显示: 暂无数据 -->
        <Loading v-if="articlesLoading && fav_series_articles.length === 0" />
        <div v-if="!articlesLoading && fav_series_articles.length === 0" class="empty-tips flex ai-center jc-center fs-sm text-gray">
          {{ $t('settings.no_data') }}
        </div>
      </template>

      <!-- 内容区域: 图书 -->
      <template v-slot:books>
        <div v-if="fav_series_books.length">
          <div v-for="(item, key) in fav_series_books" :key="key" class="collect-item">
            <CollectionItem
              :show-edit-btn="false"
              :is-series="false"
              :has-icon="true"
              :banner="item.cover"
              :title="item.name"
              :date="item.last_time | date2short"
              :author="item.group_name"
              :views="item.hits"
              :comments="item.comments"
              :coins="item.coins"
              :collections="item.favorites"
              :shares="item.shares"
              :zans="item.likes"
              mode="vertical"
              @cover-click="toPage(`/series/${item.sid}`)"
              @delete-btn-click="deleteFavConfirm(item.sid)"
            />
            <div
              class="check-box hide"
              :class="[{ show: showSelectAction }, { checked: selectedBooks.includes(item.sid) }]"
              @click.stop="muiltSelect(item.sid)"
            >
              <svg class="check-svg-icon hover" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 605.65 425.92">
                <path
                  d="M310.2,613a35.92,35.92,0,0,0,24.1-9.7L693,244.6a33.73,33.73,0,1,0-47.7-47.7L310.6,531.4,154.7,376A33.73,33.73,0,0,0,107,423.7L286.1,602.8A33.56,33.56,0,0,0,310.2,613Z"
                  transform="translate(-97.18 -187.08)"
                />
              </svg>
            </div>
          </div>
          <div class="pagination">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="booksLoading"
              :hide-on-single-page="true"
              :current-page="fav_series_books_paginate.cur"
              :total="fav_series_books_paginate.count"
              :page-size="fav_series_books_paginate.size"
              @current-change="booksPageChange"
            />
          </div>
        </div>

        <!-- 如果没有数据显示: 暂无数据 -->
        <div v-if="fav_series_books.length === 0" class="empty-tips flex ai-center jc-center fs-sm text-gray">
          {{ $t('settings.no_data') }}
        </div>
      </template>
    </TitleTabbar>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import TitleTabbar from '@/components/TitleTabbar.vue';
import CollectionItem from '@/components/CollectionItem.vue';
import Loading from '@/components/Loading';

export default {
  layout: 'settings',
  middleware: ['auth'],
  components: {
    TitleTabbar,
    CollectionItem,
    Loading
  },

  async fetch({ store, params }) {
    const uid = params.id;
    // await store.dispatch('userFavorite/getFavSeriesArticles', { uid });
    await store.dispatch('userFavorite/getFavSeriesBooks', { uid });
  },

  asyncData({ params }) {
    return {
      uid: params.id
    };
  },

  data: () => ({
    uid: 0, // 用户id
    articlesLoading: false, // 加载文章loading
    booksLoading: false, // 加载图书 loading
    tabbarCurIdx: 0, // 当前选中的 tabbar
    showSelectAction: false, // 是否显示多选
    selectedArticles: [], // 选中的文章
    selectedBooks: [] // 选中图书的
  }),

  computed: {
    ...mapState('userFavorite', ['fav_series_articles', 'fav_series_articles_paginate', 'fav_series_books', 'fav_series_books_paginate'])
  },

  methods: {
    // 文章分页
    articlePageChange(page) {
      this.articlesLoading = true;
      this.$store
        .dispatch('userFavorite/getFavSeriesArticles', {
          page,
          uid: this.uid
        })
        .finally(() => {
          this.articlesLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // 图书分页
    booksPageChange(page) {
      this.booksLoading = true;
      this.$store
        .dispatch('userFavorite/getFavSeriesBooks', {
          // .dispatch('userFavorite/getFavSeriesArticles', {
          page,
          uid: this.uid
        })
        .finally(() => {
          this.booksLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // 删除收藏
    deleteFavConfirm(id = false) {
      let key, dataKey;
      if (this.tabbarCurIdx === 1) {
        key = 'selectedArticles';
        dataKey = 'fav_series_articles';
      } else {
        key = 'selectedBooks';
        dataKey = 'fav_series_books';
      }
      const payload = {
        fid: '',
        _class: 2,
        dataKey,
        idKey: 'sid'
      };

      if (id) {
        // 删除一个
        payload.fid = id;
      } else {
        // 删除多个
        if (this[key].length === 0) {
          this.$message.error(this.$t('collection.please_select'));
          return;
        }
        payload.fid = this[key];
      }

      this.$confirm(this.$t('tips.are_you_sure'), this.$t('components.type_warn'), {
        confirmButtonText: this.$t('components.btn_confirm'),
        cancelButtonText: this.$t('components.btn_cancel'),
        type: 'warning'
      })
        .then(() => {
          this.$store
            .dispatch('userFavorite/deleteFavorite', payload)
            .then(() => {
              this.toggleShowCheckbox(false);
              this.$message.success(this.$t('tips.delete_success'));
            })
            .catch(() => this.$message.success(this.$t('tips.delete_fail')));
        })
        .catch(Function.prototype);
    },

    // 当 tabbar 切换的时候
    tabChange(i) {
      this.tabbarCurIdx = i;
      this.showSelectAction = false;
      this.selectedArticles = []; // 选中的文章("全部"选项卡)
      this.selectedBooks = []; // 选中的图书("图书选项卡")
      if (i === 1 && this.fav_series_articles.length === 0) {
        this.articlePageChange(1);
      }
    },

    // 选择所有
    selectAll() {
      let key, data;
      if (this.tabbarCurIdx === 0) {
        key = 'selectedArticles';
        data = this.fav_series_articles;
      } else {
        key = 'selectedBooks';
        data = this.fav_series_books;
      }
      for (let i = 0, l = data.length; i < l; i++) {
        this[key].push(data[i].sid);
      }
    },

    // 是否显示
    toggleShowCheckbox(flag) {
      this.showSelectAction = flag;
    },

    // 多选
    muiltSelect(id) {
      const k = this.tabbarCurIdx === 0 ? 'selectedArticles' : 'selectedBooks';
      if (this[k].includes(id)) {
        this[k] = this[k].filter((item) => item !== id);
      } else {
        // eslint-disable-next-line no-lonely-if
        if (!this[k].includes(id)) {
          this[k].push(id);
        }
      }
    }
  },

  head() {
    const title = this.$t('collection.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
@import '@/assets/scss/collect.scss';
</style>

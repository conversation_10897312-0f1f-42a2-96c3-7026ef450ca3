<template>
  <div class="nav flex jc-between">
    <!-- ==================== Google Recaptcha ==================== -->
    <!-- ==================== 登录|注册弹窗 ==================== -->
    <el-dialog
      title=""
      :visible="showLoginLayer"
      :append-to-body="true"
      :close-on-click-modal="false"
      width="500px"
      class="element_ui_layer"
      @close="toggleLoginLayer({ show: false })"
    >
      <!-- ====== 登录弹框 ====== -->
      <div v-if="loginLayerIndex === 0" class="layer-box">
        <p class="max-title text-primary">{{ $t('nav.login') }}</p>
        <p class="error-tips text-primary pad-top-15 pad-bottom-5">
          {{ errMsg }}
        </p>
        <!-- 手机号 -->
        <p class="input-item">
          <input v-model="loginModel.username" type="text" class="input-control" maxlength="64" :placeholder="$t('nav.please_input_phone')" />
        </p>

        <!-- 密码  -->
        <p class="input-item mar-top-10 mar-bottom-30">
          <input v-model="loginModel.password" type="password" class="input-control" maxlength="30" :placeholder="$t('nav.please_input_password')" />
        </p>

        <!-- 记住我 -->
        <!-- <p class="input-item mar-top-15 mar-bottom-30 fs-xs">
          <span class="radio-btn hover" :class="{ checked: loginModel.rememberMe }" @click="loginModel.rememberMe = !loginModel.rememberMe"></span>
          <span class="remember-me hover" @click="loginModel.rememberMe = !loginModel.rememberMe">
            {{ $t('nav.remember_me') }}
          </span>
          <span class="mar-left-20 text-gray">
            {{ $t('nav.dont_checked') }}
          </span>
        </p> -->

        <div class="flex jc-between ai-center" style="width: 100%">
          <button class="btn btn-140" @click="login">
            {{ $t('nav.login') }}
          </button>
          <button class="btn btn-140 btn-border" @click="toggleLoginLayer({ show: true, index: 1 })">
            {{ $t('nav.register') }}
          </button>
        </div>

        <!-- 忘记密码 -->
        <div class="btn forget-psd" @click="toPage('/forget_psw')">
          {{ $t('nav.forget_psw') }}
        </div>
      </div>

      <!-- ====== 注册弹框 ====== -->
      <div v-if="loginLayerIndex === 1" class="layer-box">
        <p class="max-title text-primary">{{ $t('nav.register') }}</p>
        <p class="error-tips text-primary pad-top-15 pad-bottom-5">
          {{ errMsg }}
        </p>
        <!-- 邮箱 -->
        <p class="input-item flex">
          <!-- <select v-model="registerModel.nation_code" class="select-area-code">
            <option v-for="(item, key) in areas" :key="key" :value="item.code">{{ item.code }}</option>
          </select> -->
          <input v-model="registerModel.username" type="email" class="input-control  mar-0" :placeholder="$t('nav.please_input_email')" />
        </p>

        <!-- 验证码  -->
        <p class="input-item mar-top-10 input-group flex">
          <input v-model="registerModel.captcha" type="number" :placeholder="$t('nav.please_input_code')" class="input-control flex-1" />
          <span class="input-btn hover flex-inline disabled jc-center ai-center text-white" @click="sendVerifyCode">
            <span v-if="seconds">{{ $t('nav.resend') }}{{ seconds }}</span>
            <span v-else>{{ $t('nav.send_verify_code') }}</span>
          </span>
        </p>

        <!-- 用户协议 -->
        <!-- <p class="input-item mar-top-15 mar-bottom-30 fs-xs">
          <span class="radio-btn hover" :class="{ checked: agreement }" @click="toggleState('agreement')"></span>
          <span class="remember-me hover" @click="toggleState('agreement')">
            {{ $t('nav.readed_agreement') }}
          </span>
          <nuxt-link :to="$i18n.path('agreement')" tag="a" target="_blank" class="mar-left-10 text-primary">
            {{ $t('nav.user_agreement') }}
          </nuxt-link>
        </p> -->

        <!-- reCAPTCHA V3 自动验证，无需显示UI -->
        <!-- <div class="input-item mar-top-15 mar-bottom-30">
          <div ref="google-recaptcha-view-container"></div>
        </div> -->

        <div class="flex jc-center ai-center" style="width: 100%">
          <button class="btn btn-140" @click="nextStep">
            {{ $t('nav.next_step') }}
          </button>
        </div>
        <!-- 已有账号 -->
        <span class="rbtm-tips hover text-primary" @click="toggleLoginLayer({ show: true, index: 0 })">
          {{ $t('nav.has_account') }}
        </span>
      </div>

      <!-- ====== 完善资料弹框 ====== -->
      <div v-if="loginLayerIndex === 2" class="layer-box">
        <p class="max-title text-primary">{{ $t('nav.complete_info') }}</p>
        <p class="error-tips text-primary pad-top-15 pad-bottom-5">
          {{ errMsg }}
        </p>
        <!-- 用户名 -->
        <p class="input-item">
          <input
            v-model="registerModel.nickname"
            type="text"
            class="input-control"
            maxlength="8"
            :placeholder="$t('nav.please_input_username')"
            @blur="checkNickName"
          />
        </p>

        <!-- 密码  -->
        <p class="input-item mar-top-10">
          <input
            v-model="registerModel.password"
            type="password"
            class="input-control"
            maxlength="16"
            :placeholder="$t('nav.please_input_password')"
          />
        </p>

        <!-- 确认密码  -->
        <p class="input-item mar-top-10 mar-bottom-30">
          <input
            v-model="registerModel.confirm_password"
            type="password"
            class="input-control"
            maxlength="16"
            :placeholder="$t('nav.please_confirm_password')"
          />
        </p>

        <div class="flex jc-center ai-center" style="width: 100%">
          <button class="btn btn-140" @click="register">
            {{ $t('nav.register') }}
          </button>
        </div>
      </div>
    </el-dialog>

    <!-- -------------------- 导航 -------------------- -->
    <div class="left-nav-btns flex ai-center">
      <nuxt-link :to="$i18n.path('')" class="nav-logo">
        <!-- <img src="@/assets/images/logo.svg" /> -->
        <img src="@/assets/images/logo_test.svg" />
      </nuxt-link>
      <div class="nav-dl">
        <img src="@/assets/images/app.svg" class="dl-image" />
        <div
          :class="{ themeColor: showAppQrcode }"
          class="nav-dltext nav-btn hover"
          @mouseenter="showAppQrcode = true"
          @mouseleave="showAppQrcode = false"
        >
          {{ $t('nav.download_app') }}
        </div>
        <div v-show="showAppQrcode" class="nav-dlQR">
          <div class="QRcode">
            <img src="@/assets/images/qr_code.png" width="100%" />
          </div>
          <div class="QRtext">{{ $t('nav.scan_code_download') }}</div>
        </div>
      </div>
      <div class="nav-language hover nav-btn">
        <!-- <button class="btn" @click="toggleLanguage">{{ lang }}</button> -->
        <!-- <NuxtLink v-if="$i18n.locale === 'tc'" :to="`/cn` + $route.fullPath" active-class="none" exact>
          {{ $t('language.cn') }}
        </NuxtLink>
        <NuxtLink v-else :to="$route.fullPath.replace(/^\/[^\/]+/, '')" active-class="none" exact>
          {{ $t('language.tc') }}
        </NuxtLink> -->
      </div>
      <div v-clickoutside="closeSearchTags" class="nav-search" :class="{ active: searchInputFocus }">
        <input
          v-model="kw"
          class="nav-search-box"
          type="text"
          :placeholder="$t('nav.search_in_here')"
          :autocomplete="false"
          @focus="focusChange(true)"
          @blur="focusChange(false)"
          @keyup.enter="jumpSearch(kw)"
        />
        <img class="nav-search-icon hover" src="@/assets/images/icon-search.svg" @click="jumpSearch(kw)" />
        <div v-show="firstOpen && showSearchTags" class="nav-hotSearch nav-hot">
          <div class="hotSearch-text flex jc-between">
            <span>{{ $t('nav.hot_tags') }}</span>
            <span class="close-tags hover text-dark" @click="showSearchTags = false">&times;</span>
          </div>
          <div v-if="searchTagsLoading" class="pad-top-20 pad-bottom-20">
            <Loading />
          </div>
          <div v-else class="hotSearch-content">
            <div
              v-for="(item, index) in hotSearchList"
              :key="index"
              :class="{ searchColor: isHotSearch === item.id }"
              class="hotSearch-content-box hover"
              @click="jumpSearch(item.alias)"
            >
              <span class="hotSearch-content-text">
                {{ item.alias }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="rig-navs-wrapper flex ai-center jc-end">
      <div v-if="isLogin" class="login">
        <div v-clickoutside="closeMenus" class="nav-avatar" @click="showMenus = !showMenus">
          <img class="nav-avatar-image hover" :src="loginUser.avatar" />
          <div v-show="showMenus" class="nav-avatar-info">
            <div class="nav-avatar-topinfo">
              <div class="nav-avatar-fans" @click="toFansPage(loginUser.uid, true)">
                <div class="hover nav-avatar-fan">
                  <span>{{ $t('nav.fans') }}</span>
                  <span style="color: #39D7D9; margin-left: 5px;">
                    {{ loginUser.followers }}
                  </span>
                </div>
                <div class="hover nav-avatar-follow" @click="toFansPage(loginUser.uid, false)">
                  <span>{{ $t('nav.follow') }}</span>
                  <span style="color: #39D7D9; margin-left: 5px;">
                    {{ loginUser.following }}
                  </span>
                </div>
              </div>
              <div class="hover nav-avatar-coins">
                <div class="nav-avatar-coin">
                  <img src="@/assets/images/coin.svg" />
                </div>
                <div style="margin-left: 5px;">
                  {{ loginUser.balance.coin }}
                </div>
              </div>
            </div>
            <div class="nav-avatar-bottominfor">
              <div class="nav-avatar-personal">
                <div class="nav-personal-icon">
                  <img src="@/assets/images/my_profile.svg" />
                </div>
                <div style="margin-left: 5px; cursor: pointer;" @click="toPage(`/settings/${loginUser.uid}`)">
                  {{ $t('nav.profile') }}
                </div>
              </div>
              <div class="nav-avatar-release">
                <div class="nav-release-icon">
                  <img src="@/assets/images/my_publishmgr.svg" />
                </div>
                <div class="hover mar-left-5" @click="toPage(`/publish_mgr/article/${loginUser.uid}`)">
                  {{ $t('nav.publish_mgr') }}
                </div>
              </div>

              <!-- 收藏 -->
              <div class="nav-avatar-collections">
                <div class="nav-avatar-collection">
                  <div class="nav-collection-icon">
                    <img src="@/assets/images/collection.svg" />
                  </div>
                  <div class="hover mar-left-5" @click="toPage(`/collection/article/${loginUser.uid}`)">
                    {{ $t('nav.collections') }}
                  </div>
                </div>
                <button class="btn nav-avatar-quit" @click="logout">
                  {{ $t('nav.logout') }}
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="nav-name hover text-hide-1" :title="loginUser.nickname">{{ loginUser.nickname }}</div>
        <div class="lv-icon" :class="[`lv-${loginUser.level.level}`]">
          {{ loginUser.level.name }}
        </div>

        <!-- 消息 -->
        <!-- :to="$i18n.path(`messages/${loginUser.uid}`)" -->
        <div
          target="_blank"
          class="nuxt-link nav-button hover dis-select nav-message-box"
          @click="toMsgPage(1)"
          @mouseenter="showMsgLog()"
          @mouseleave="removeMsgLog()"
        >
          <span class="nav-msg"
            >{{ $t('nav.msg') }}
            <span v-if="unReadMsg.all_count > 0 && unReadMsg.all_count < 99" class="nav-message-mark" v-text="unReadMsg.all_count"></span>
            <span v-if="unReadMsg.all_count >= 99" class="nav-message-mark" v-text="'99+'"></span>
          </span>

          <!-- 信息弹窗 -->
          <div v-show="isMsgLog" class="nav-message-log">
            <span @click.stop="toMsgPage(1)"
              >{{ $t('nav.commits') }} <span v-if="unReadMsg.reply.count > 0 && unReadMsg.reply.count < 99">({{ unReadMsg.reply.count }})</span>
              <span v-else-if="unReadMsg.reply.count >= 99">(99+)</span>
            </span>
            <span @click.stop="toMsgPage(0)"
              >{{ $t('nav.guildhall') }}
              <span v-if="unReadMsg.guild.count > 0 && unReadMsg.guild.count < 99">({{ unReadMsg.guild.count }})</span>
              <span v-else-if="unReadMsg.guild.count >= 99">(99+)</span>
            </span>
            <span @click.stop="toSystemPage"
              >{{ $t('nav.system_news') }}
              <span v-if="unReadMsg.sys_msg.count > 0 && unReadMsg.sys_msg.count < 99">({{ unReadMsg.sys_msg.count }})</span>
              <span v-else-if="unReadMsg.sys_msg.count.count >= 99">(99+)</span>
            </span>
          </div>
        </div>

        <!-- 动态(我关注的) -->
        <div
          target="_blank"
          class="nuxt-link nav-button hover dis-select nav-message-box"
          @click="toPage(`moments/${loginUser.uid}`)"
          @mouseenter="showDynamicList()"
          @mouseleave="removeDynamicLog()"
        >
          <span class="nav-msg" style="line-height: 52px;">
            {{ $t('nav.moment') }}
            <span v-if="unDynamicMsg.article.unread > 0 && unDynamicMsg.article.unread < 99" class="nav-message-mark">{{
              unDynamicMsg.article.unread
            }}</span>
            <span v-if="unDynamicMsg.article.unread >= 99" class="nav-message-mark">99+</span>
          </span>
          <!-- 动态弹窗 -->
          <div v-show="isShowDynamic" class="nav-state-box">
            <div class="state-contain">
              <div v-if="DynamicLoading" class="pad-top-20 pad-bottom-20">
                <Loading />
              </div>
              <div v-if="!DynamicLoading && DynamicMsg.length">
                <div class="nav-state-log">
                  <div
                    v-for="(item, idx) in DynamicMsg"
                    :key="idx"
                    class="nav-state btn flex"
                    @click.stop="toDelOrThemPAge(item.parent_gid, item.aid)"
                  >
                    <div class="img-box">
                      <img :src="item.author.avatar" alt="" />
                    </div>
                    <div class="flex nav-state-end">
                      <div class="flex nav-state-title">
                        <span>{{ item.author.nickname }}</span>
                        <span>{{ item.last_time | date2short }}</span>
                      </div>
                      <div class="nav-state-txt">
                        {{ item.title }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="show-more btn" @click.stop="toPage(`moments/${loginUser.uid}`)">
                  {{ $t('nav.show_more') }}
                </div>
              </div>
              <div v-if="!DynamicLoading && !DynamicMsg.length" class="state-contain-noNews">
                <img src="@/assets/images/no_data.png" alt="" />
                <span>{{ $t('nav.no_news') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="unLogin flex ai-center">
        <div class="hover" @click="toggleLoginLayer({ show: true })">
          {{ $t('nav.login') }}
        </div>
        <span>/</span>
        <div class="hover" @click="toggleLoginLayer({ show: true, index: 1 })">
          {{ $t('nav.register') }}
        </div>
      </div>

      <!-- 历史记录 -->
      <nuxt-link :to="$i18n.path(`history/article/${loginUser.uid}`)" target="_blank" class="nav-history hover dis-select nuxt-link">
        {{ $t('nav.history') }}
      </nuxt-link>

      <!-- 旧版 -->
      <a href="https://obsolete.lightnovel.fun" target="_blank" class="nav-history hover dis-select nuxt-link">
        {{ $t('nav.old_version') }}
      </a>

      <!-- 发帖 -->
      <div class="nav-button-publish hover" @click="toPage('/write')">
        {{ $t('nav.write') }}
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import { search } from '@/api';
// import UserAvatar from '@/components/UserAvatar';
import Loading from '@/components/Loading';
import storage from '@/plugins/mindLocalStorage.js';
export default {
  components: {
    Loading
  },

  data: () => ({
    areas: [
      { area: '中国大陆', code: 86 },
      { area: '中国台湾', code: 886 },
      { area: '中国香港', code: 852 },
      { area: '中国澳门', code: 853 },
      { area: '日本', code: 81 },
      { area: '韩国', code: 82 },
      { area: '新加坡', code: 65 },
      { area: '美国', code: 1 },
      { area: '巴西', code: 55 },
      { area: '俄罗斯', code: 7 },
      { area: '乌克兰', code: 380 },
      { area: '荷兰', code: 31 },
      { area: '英国', code: 44 }
    ],
    loginModel: {
      // 登录信息
      username: '',
      password: ''
    },
    registerModel: {
      // 注册信息
      nation_code: 86, // 手机区号
      username: '', // 手机号
      nickname: '', // 用户昵称
      password: '', // 用户密码
      confirm_password: '', // 确认密码
      captcha: '' // 手机验证码
    },
    canChangeLang: false, // 是否能够切换语言
    allowName: false, // nickname是否可以使用
    kw: '',
    errMsg: '',
    notRobot: false, // 是否进行了机器人验证
    GRecaptchaResponse: null, // google recaptcha 返回的结果
    agreement: true, // "用户协议"
    seconds: 0, // 发送验证码多少秒后才可以发送
    searchInputFocus: false, // 搜索框是否有焦点
    searchTagsLoading: false, // 获取搜索tags
    // ------------------------------------
    showMenus: false, // 头像菜单栏
    isMessage: true, // 消息提示标志
    showAppQrcode: false, // 下载二维码
    isEnter: false, // 是否变色
    firstOpen: true, // 是否是第一次打开
    showSearchTags: false, // 热搜框
    isHotSearch: '',
    hotSearchList: [],
    isMsgLog: false, // 是否显示信息悬浮窗
    DynamicMsg: [], // 模拟动态数据
    DynamicLoading: false, // 动态弹窗加载
    isShowDynamic: false, // 是否显示动态悬浮窗
    unReadMsgTime: null, // 信息定时器
    unDynamicMsgTime: null, // 动态定时器
    newMsg: null // 跨页面通讯需要
  }),

  computed: {
    ...mapState('login', ['unloginBack', 'showLoginLayer', 'loginLayerIndex']),
    ...mapGetters('login', ['isLogin', 'loginUser']),
    ...mapGetters('msg', ['unReadMsg', 'unDynamicMsg']),
    lang() {
      if (this.$i18n.locale === 'tc') {
        return this.$t('language.cn');
      } else {
        return this.$t('language.tc');
      }
    }
  },
  watch: {
    unloginBack(val) {
      if (val) {
        this.$message.error(this.$t('tips.login_first'));
        this.$store.commit('login/setUnloginBack', false);
      }
    },
    showLoginLayer(val) {
      // reCAPTCHA V3 不需要初始化DOM
      // if (val && this.loginLayerIndex === 1) {
      //   this.$nextTick(this.initGoogleRecaptchaWrapper);
      // }
    },
    loginLayerIndex(val) {
      // reCAPTCHA V3 不需要初始化DOM
      // if (val === 1 && this.showLoginLayer) {
      //   this.$nextTick(this.initGoogleRecaptchaWrapper);
      // }
    }
  },
  mounted() {
    if (process.browser) {
      this.canChangeLang = true;
      storage.get('vuex') && storage.get('vuex').login && storage.get('vuex').login.isLogin && this.replyNews();
      // 登录状态是异步数据，最稳当的方法就是直接读本地缓存
      window.addEventListener('storage', (e) => {
        // 确保监听的是vuex的本地缓存中的未读消息，两个改变，则对其重新赋值
        if (e.key && e.key === 'vuex') {
          this.newMsg = JSON.parse(e.newValue);
          if (this.newMsg.msg.unReadMsg.all_count !== this.unReadMsg.all_count) {
            this.setUnReadMsg(this.newMsg.msg.unReadMsg);
            // 此处一定要清空，否则会内存泄露
            this.newMsg = null;
          }
        }
      });
      // 监听当前页面是否为浏览状态,来操作定时器
      document.addEventListener('visibilitychange', () => {
        const state = document.visibilityState;
        // hidden 表示当前页面切换为隐藏状态，visible表示当前页面切换为浏览状态
        if (state === 'hidden') {
          // console.log('定时器清除');
          // this.start = false;
          clearInterval(this.unReadMsgTime);
          clearInterval(this.unDynamicMsgTime);
        } else if (state === 'visible') {
          // console.log('定时器开启');
          // this.start = true;
          storage.get('vuex').login && storage.get('vuex').login.isLogin && this.replyNews(false);
        }
      });
    }
  },
  methods: {
    ...mapMutations('login', ['toggleLoginLayer']),
    ...mapMutations('msg', ['setUnReadMsg', 'setDynamicMsg']),
    ...mapActions('msg', ['getUnReadMsgNot', 'getDynamicMsgNot']),
    // reCAPTCHA V3 不需要初始化DOM，已移除
    // initGoogleRecaptchaWrapper() {
    //   const container = this.$refs['google-recaptcha-view-container'];
    //   if (container.childNodes.length) return;
    //   window.grecaptcha.render(container, {
    //     sitekey: '6Lf5l6oZAAAAAEZ4GXl99YATuKXNXDmNr5_MfScl',
    //     callback: (token) => {
    //       this.notRobot = true;
    //       this.GRecaptchaResponse = { action: 'user/register', token };
    //     }
    //   });
    // },

    // 验证 google recaptcha v3
    checkGRecaptcha(callback = null) {
      try {
        window.grecaptcha.ready(() => {
          const sitekey = '6LdzVLgrAAAAANg28qWlEX4xUbz7PVF9cUE1vZq_';
          const action = 'user/register';
          window.grecaptcha
            .execute(sitekey, { action })
            .then((token) => {
              callback && typeof callback === 'function' && callback(action, token);
            })
            .catch((e) => {
              this.$message.error(this.$t('tips.google_recaptcha_check_fail'));
              console.log(e);
            });
        });
      } catch (e) {
        this.$message.error(this.$t('tips.google_recaptcha_check_fail'));
        console.log(e);
      }
    },
    // 去detail详情页还是公会大厅详情页
    toDelOrThemPAge(gid, aid) {
      if (gid === 42) {
        this.toPage(`/themereply/${aid}`);
      } else {
        this.toPage(`/detail/${aid}`);
      }
    },
    // 切换语言
    toggleLanguage() {
      if (this.canChangeLang) {
        this.canChangeLang = false;
        const path = this.$route.fullPath;
        const url = this.$i18n.locale === 'tc' ? `/cn${path}` : path.replace(/^\/[^\\/]+/, '');
        window.location = window.location.origin + url;
      }
    },

    // 搜索框焦点变化
    focusChange(flag) {
      this.searchInputFocus = flag;
      if (flag && this.hotSearchList.length === 0) {
        this.showSearchTags = true;
        this.searchTagsLoading = true;
        this.$axios
          .$post(search.getSearchTag)
          .then((res) => {
            if (res.code === 0) {
              this.hotSearchList = res.data;
            }
          })
          .finally(() => (this.searchTagsLoading = false));
      }
    },

    // 关闭搜索标签
    closeSearchTags() {
      this.firstOpen = false;
      this.showSearchTags = false;
    },

    // 下一步
    nextStep() {
      const { username, captcha } = this.registerModel;
      if (username.trim() && captcha.trim()) {
        this.toggleLoginLayer({ show: true, index: 2 });
      } else {
        this.$message.error(this.$t('tips.please_input_all'));
      }
    },

    // 关闭下拉菜单
    closeMenus() {
      this.showMenus = false;
    },

    // 登录
    login() {
      const { username, password } = this.loginModel;
      if (!username.trim() || !password.trim()) {
        this.$message.error(this.$t('tips.please_input_all_inputs'));
        return;
      }
      this.$store.dispatch('login/login', this.loginModel).then(() => {
        this.toggleLoginLayer({ show: false });
        // 获取登录用户的未阅读消息
        this.replyNews();
      });
    },

    // 退出登录
    logout() {
      this.$store.dispatch('login/logout').then(() => {
        this.toggleLoginLayer({ show: false });
        this.$message.success(this.$t('nav.logout_success'));
        // 退出登录清除定时器
        clearInterval(this.unReadMsgTime);
        clearInterval(this.unDynamicMsgTime);
        // 退出登录删除动态信息
        this.DynamicMsg = [];
        // 退出登录删除本地缓存
        storage.get('last_read') && storage.remove('last_read');
        if (this.$route.name !== 'index') {
          this.toPage('/', 'replace');
        }
      });
    },

    // 发送验证码
    async sendVerifyCode() {
      if (!this.seconds) {
        // eslint-disable-next-line camelcase
        const { username: email } = this.registerModel;
        const reg = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
        if (!email || !reg.test(email)) {
          this.$message.error(this.$t('tips._err_email'));
          return;
        }

        // 使用 reCAPTCHA V3 进行验证
        this.checkGRecaptcha(async (action, token) => {
          this.startTimer();
          const res = await this.$axios.$post('/api/captcha/email', {
            email,
            type: 100,
            recaptcha: { action, token }
          });
          if (res.code === 0) {
            this.$message.success(this.$t('tips.send_success'));
          } else {
            this.$message.error(this.$t('tips.send_fail'));
          }
        });

      }
    },

    // 切换显示|隐藏二维码
    toggleShowAppQrcode(flag) {
      this.showAppQrcode = flag;
    },

    // 开始倒计时
    startTimer() {
      this.seconds = 60;
      this.timer = setInterval(() => {
        this.seconds--;
        this.seconds === 0 && clearInterval(this.timer);
      }, 1000);
    },

    // 检查昵称是否存在
    checkNickName() {
      const { nickname } = this.registerModel;
      if (this.reqing || !nickname.trim()) {
        return;
      }
      this.reqing = true;
      this.$axios
        .$post('/api/user/check-nickname', { nickname })
        .then((res) => res.code === 0 && (this.allowName = true))
        .catch(() => this.$message.error(this.$t('nav.nickname_exists')))
        .finally(() => (this.reqing = false));
    },

    // 注册
    register() {
      /* eslint-disable */
      const { username, captcha, nickname, password, confirm_password } = this.registerModel;
      const emailReg = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
      if (!username || !emailReg.test(username)) {
        this.$message.error(this.$t('tips._err_email'));
        return;
      }
      if (!nickname) {
        this.$message.error(this.$t('nav.please_input_name'));
        return;
      }
      if (!this.allowName) {
        this.$message.error(this.$t('nickname_exists'));
        return;
      }
      const reg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[\da-zA-Z~!@#$%\^&*()_+`\-={}\[\];':",./<>?\\|]{8,16}$/;
      if (!reg.test(password)) {
        this.$message.error(this.$t('nav.password_fmt_err'));
        return;
      }
      if (password !== confirm_password) {
        this.$message.error(this.$t('nav.password_confirm_err'));
        return;
      }
      this.$axios
        .$post('/api/user/register', {
          username,
          captcha,
          nickname,
          type: 2, // 1:手机 2:邮箱
          password
        })
        .then((res) => {
          if (res.code === 0) {
            this.toggleLoginLayer({ show: false });
            this.$store.commit('login/login', res.data);
          } else {
            this.$message.error(this.$t('nav.reg_fail'));
          }
        })
        .catch(() => this.$message.error(this.$t('nav.reg_fail')));
    },

    // 设置属性的值
    setState(key, val) {
      if (!key.includes('.')) {
        this[key] = val;
        return;
      }
      if (key) {
        const keys = key.split('.');
      }
      const lastKey = keys.pop();
      const value = keys.reduce((prev, curr) => this[curr], this);
      value[lastKey] = val;
    },

    // 切换属性的值
    toggleState(key, val) {
      if (typeof val === 'undefined') {
        this[key] = !this[key];
      } else {
        this[key] = val;
      }
    },

    // 跳转搜索页
    jumpSearch(keywords) {
      this.showSearchTags = false;
      this.toPage('/search', 'push', { keywords });
    },

    avatarSelect() {
      this.showMenus = true;
    },
    avatarOut() {
      this.showMenus = false;
    },
    downloadSelect() {
      this.showAppQrcode = true;
    },
    downloadOut() {
      this.showAppQrcode = false;
    },
    doSearch() {
      this.showSearchTags = true;
    },
    searchSelect() {
      this.showSearchTags = false;
    },
    hotSearchSelect(index) {
      this.isHotSearch = index;
    },
    hotSearchOut() {
      this.isHotSearch = '';
    },
    loginOut() {
      this.isLogin = false;
    },
    // 触摸信息显示浮窗
    showMsgLog() {
      this.isMsgLog = true;
    },
    removeMsgLog() {
      this.isMsgLog = false;
    },
    toMsgPage(type) {
      if (type === 0) {
        this.toPage(`messages/${this.loginUser.uid}?type=${type}`);
      } else if (type === 1) {
        this.toPage(`messages/${this.loginUser.uid}?type=${type}`);
      }
    },
    toSystemPage() {
      this.toPage(`messages/system_notification`);
    },
    // 轮询方法
    pollingUnread(url, delay, method) {
      if (this.start) {
        this.$axios.post(url, null, { progress: false }).then((res) => {
          // 控制轮询条件
          if (res.data.code === 0) {
            method(res.data.data);
            setTimeout(() => {
              this.pollingUnread(url, delay, method);
            }, delay);
          }
        });
      } else {
        return false;
      }
    },
    // 轮询方法
    pollingDynamic(url, delay, method) {
      if (this.start) {
        this.$axios.post(url, null, { progress: false }).then((res) => {
          // 控制轮询条件
          if (res.code === 0) {
            method(res.data);
            setTimeout(() => {
              this.pollingDynamic(url, delay, method);
            }, delay);
          }
        });
      } else {
        return false;
      }
    },
    replyNews(type = true) {
      if (type) {
        this.getUnReadMsgNot();
        this.getDynamicMsgNot();
      }
      // 180000
      // 300000
      // 每个新开页面都判断是否登录，登录成功就执行获取未阅读消息
      this.unReadMsgTime = setInterval(() => {
        setTimeout(() => {
          this.getUnReadMsgNot();
        }, 0);
      }, 180000);
      this.unDynamicMsgTime = setInterval(() => {
        setTimeout(() => {
          this.getDynamicMsgNot();
        }, 0);
      }, 300000);

      // this.pollingUnread('/api/notify/unread', 180000, this.setUnReadMsg);
      // this.pollingDynamic('/api/notify/dynamic', 300000, this.setDynamicMsg);
    },
    // 弹窗出现时请求数据
    showDynamicList() {
      this.isShowDynamic = true;
      if (!this.DynamicMsg.length) {
        this.DynamicLoading = true;
        this.$axios.post('api/recom/get-follows', null, { progress: false }).then((res) => {
          if (res.data.code === 0) {
            this.DynamicMsg = res.data.data;
            // this.setDynamicMsg({ unread: 0 });
            this.getDynamicMsgNot();
            this.DynamicLoading = false;
          }
        });
      }
    },
    removeDynamicLog() {
      this.isShowDynamic = false;
    }
  }
};
</script>

<style lang="scss">
// 隐藏 google recaptcha
.grecaptcha-badge {
  visibility: hidden;
}

// 清除 element-ui 的样式, 不能使用 scoped
.element_ui_layer {
  .el-dialog__header {
    padding: 0 !important;
    .el-dialog__headerbtn {
      z-index: 2;
    }
  }
  .el-dialog__body {
    padding: 0 !important;
  }
}
</style>

<style lang="scss" scoped>
// ------------ 登录和注册弹框 -------------------
.element_ui_layer {
  .forget-psd {
    position: absolute;
    font-size: 15px;
    font-family: HYQiHei;
    color: #39d7da;
    line-height: 15px;
    right: 110px;
    bottom: 14px;
  }
  .max-title {
    font-size: 36px;
  }
  .btn-140 {
    width: 140px;
    height: 40px;
    border-radius: 40px;
    background: $primary;
    color: #fff;
    &.btn-border {
      border: 1px solid $primary;
      color: $primary;
      background: #fff;
    }
  }

  // 登录弹框
  .layer-box {
    position: relative;
    padding: 40px 100px;
    .rbtm-tips {
      position: absolute;
      right: 15px;
      bottom: 15px;
    }
    .input-item {
      &.input-group {
        .input-control {
          border-right: none;
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          margin-right: 0;
        }
        .input-btn {
          background: $primary;
          height: 40px;
          width: 100px;
          border-top-right-radius: 5px;
          border-bottom-right-radius: 5px;
        }
      }
      .select-area-code {
        width: 80px;
        border: 1px solid $gray-white;
        margin-right: 10px;
      }
      .mar-0 {
        margin: 0;
      }
      .input-control {
        height: 40px;
      }
      .radio-btn {
        vertical-align: bottom;
        display: inline-block;
        background: $gray-white;
        width: 12px;
        height: 12px;
        border-radius: 100%;
        margin-right: 10px;
        &.checked {
          background: $primary !important;
        }
      }
    }
  }
}
// -------------- 导航栏样式 ------------
.nav {
  display: flex;
  align-items: center;
  width: 1190px;
  height: 50px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 0px 15px 0px rgba(1, 1, 1, 0.1);
  border-radius: 0px 0px 20px 20px;
  .nav-btn {
    min-width: 80px;
  }
  .themeColor {
    color: #39d7d9 !important;
  }
  .nav-logo {
    height: 30px;
    width: 30px;
  }
  .nav-dl {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 15px;
    margin-left: 30px;
    .dl-image {
      width: 10px;
      height: 13px;
    }
    .nav-dltext {
      // display: flex;
      // align-items: center;
      height: 15px;
      font-size: 15px;
      color: rgba(66, 66, 66, 1);
      margin-left: 4px;
    }
    .nav-dlQR {
      position: absolute;
      z-index: 3;
      top: 32px;
      left: -30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 130px;
      height: 160px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(243, 243, 243, 1);
      border-radius: 5px;
      padding-top: 11px;
      .QRcode {
        width: 114px;
        height: 111px;
      }
      .QRtext {
        padding-top: 9px;
        font-size: 15px;
        color: #39d7d9;
      }
    }
  }
  .nav-language {
    // display: flex;
    // align-items: center;
    // height: 15px;
    font-size: 15px;
    // margin-left: 30px;
    a {
      color: rgba(66, 66, 66, 1);
      text-decoration: none;
      &:hover {
        color: $primary;
      }
    }
  }
  .nav-search {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    position: relative;
    width: 245px;
    height: 30px;
    margin-left: 50px;
    padding: 0 15px 0 15px;
    border: 1px solid rgba(243, 243, 243, 1);
    border-radius: 15px;
    &.active {
      border-color: $primary;
    }
    .nav-search-box {
      width: 100%;
      height: 17px;
      border: none;
      outline: none;
      &:focus {
        outline: none;
        box-shadow: none;
      }
    }
    .nav-search-icon {
      width: 15px;
      height: 17px;
    }
    .nav-hot {
      position: absolute;
      z-index: 3;
      top: 135%;
      left: 0;
      width: 100%;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(243, 243, 243, 1);
      border-radius: 5px;
      padding: 15px 12px 0 12px;
      .hotSearch-text {
        font-size: 15px;
        color: #b2b2b2;
        .close-tags {
          width: 15px;
          height: 15px;
        }
      }
      .hotSearch-content {
        display: flex;
        flex-wrap: wrap;
        padding-top: 14px;
        .searchColor {
          border-color: #39d7d9 !important;
        }
        .hotSearch-content-box {
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          border: 1px solid #eee;
          border-radius: 5px;
          padding: 5px 10px;
          margin: 0 10px 10px 0;
          .hotSearch-content-text {
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: keep-all;
            &:hover {
              color: $primary;
            }
          }
        }
      }
    }
  }
  .left-nav-btns {
    padding-left: 15px;
  }
  .rig-navs-wrapper {
    padding-right: 15px;
  }
  .login {
    display: flex;
    align-items: center;
    .nav-avatar {
      height: 35px;
      width: 35px;
      // margin-left: 100px;
      position: relative;
      .nav-avatar-image {
        border-radius: 50%;
        overflow: hidden;
        width: 100%;
        height: 100%;
        object-fit: cover;
        overflow: hidden;
      }
      .nav-avatar-info {
        width: 168px;
        height: 170px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(244, 243, 243, 1);
        border-radius: 5px;
        position: absolute;
        z-index: 3;
        top: 42px;
        left: -66px;
        .nav-avatar-topinfo {
          box-sizing: border-box;
          padding: 20px 20px 0 20px;
          height: 70px;
          border-bottom: 1px solid rgba(243, 243, 243, 1);
          .nav-avatar-fans {
            display: flex;
            justify-content: space-between;
            box-sizing: border-box;
            padding-bottom: 15px;
            width: 100%;
            height: 27px;
            font-size: 13px;
            .nav-avatar-fan {
              display: flex;
            }
            .nav-avatar-follow {
              display: flex;
            }
          }
          .nav-avatar-coins {
            display: flex;
            font-size: 13px;
            .nav-avatar-coin {
              width: 12px;
              height: 12px;
            }
          }
        }
        .nav-avatar-bottominfor {
          box-sizing: border-box;
          padding: 10px 20px 0 20px;
          font-size: 13px;
          .nav-avatar-personal {
            display: flex;
            margin-bottom: 17px;
            height: 12px;
            .nav-personal-icon {
              width: 12px;
            }
          }
          .nav-avatar-release {
            display: flex;
            margin-bottom: 17px;
            height: 12px;
            .nav-release-icon {
              width: 12px;
            }
          }
          .nav-avatar-collections {
            display: flex;
            justify-content: space-between;
            margin-bottom: 17px;
            height: 12px;
            .nav-avatar-collection {
              display: flex;
              .nav-collection-icon {
                width: 12px;
              }
            }
            .nav-avatar-quit {
              color: #39d7d9;
            }
          }
        }
      }
    }
    .nav-name {
      align-items: center;
      max-width: 100px;
      min-height: 30px;
      line-height: 30px;
      margin: 0 10px;
      text-overflow: ellipsis;
      font-size: 15px;
    }
    .nav-level {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 35px;
      height: 15px;
      font-size: 13px;
      margin-left: 5px;
      background: rgba(166, 144, 227, 1);
      border-radius: 5px;
      color: white;
      font-family: Source Han Sans CN;
      font-weight: 400;
    }
    .nav-button {
      display: flex;
      align-items: center;
      position: relative;
      height: 15px;
      margin-left: 30px;
      .nav-msg {
        position: relative;
        .nav-message-mark {
          width: 20px;
          height: 20px;
          background: #39d7d9;
          text-align: center;
          border-radius: 50%;
          color: white;
          position: absolute;
          font-size: 12px;
          line-height: 19px;
          left: 32px;
          top: 4px;
        }
      }
    }
  }
  // 消息悬浮窗
  .nav-message-box {
    position: relative;
    span {
      line-height: 52px;
    }
    .nav-message-log {
      position: absolute;
      z-index: 999;
      box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
      left: -40px;
      top: 33px;
      width: 130px;
      height: 110px;
      background: #ffffff;
      border: none;
      border-radius: 5px;
      display: flex;
      flex-direction: column;
      font-size: 13px;
      font-family: Source Han Sans CN;
      color: #424242;
      padding-top: 10px;
      padding-bottom: 10px;
      span {
        line-height: 30px;
        padding-left: 20px;
        span {
          padding-left: 0px;
          color: #39d7d9;
          margin-left: 5px;
        }
        &:hover {
          background-color: #f4f4f4;
        }
      }
    }
  }
  // 动态悬浮窗
  .nav-state-box {
    position: relative;
    span {
      line-height: 52px;
    }
    .state-contain {
      position: absolute;
      z-index: 999;
      left: -140px;
      top: 22px;
      width: 350px;
      background: #ffffff;
      border: 1px solid #f4f3f3;
      box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
      border-radius: 5px;
      font-size: 13px;
      color: #424242;
      .nav-state-log {
        overflow-y: scroll;
        height: 410px;
        padding-top: 20px;
        .nav-state {
          align-items: center;
          margin-bottom: 20px;
          padding: 0 20px;
          .img-box {
            width: 35px;
            height: 35px;
            background: #fcfdf9;
            img {
              width: 35px;
              height: 35px;
              border-radius: 50%;
            }
          }
          .nav-state-end {
            margin-left: 10px;
            flex-direction: column;
            font-family: Source Han Sans CN;
            color: #424242;
            font-size: 13px;
            .nav-state-title {
              span {
                line-height: 22px;
                &:last-of-type {
                  margin-left: 10px;
                  color: #b2b2b2;
                }
              }
            }
            .nav-state-txt {
              line-height: 22px;
            }
          }
          &:last-of-type {
            margin-bottom: 0px;
          }
          &:hover {
            background-color: #f4f4f4;
          }
        }
      }
      .show-more {
        background: #fafafa;
        text-align: center;
        height: 35px;
        line-height: 35px;
      }
      .state-contain-noNews {
        padding: 47px;
        text-align: center;
        span {
          color: #d6d6d6;
        }
      }
    }
  }
  .unLogin {
    display: flex;
    align-items: center;
    height: 15px;
    font-size: 15px;
    color: #424242;
  }
  .nav-history {
    display: flex;
    align-items: center;
    position: relative;
    height: 15px;
    font-size: 15px;
    margin-left: 30px;
  }
  .nav-button-publish {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 30px;
    font-size: 15px;
    color: #fff;
    width: 70px;
    height: 30px;
    background: rgba(57, 215, 217, 1);
    border-radius: 15px;
  }
}
</style>

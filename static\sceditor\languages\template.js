/**
 * <AUTHOR> Name> <Your e-mail/Website if you would like>
 * @license [MIT](http://www.opensource.org/licenses/mit-license.php)
 */
(function () {
	'use strict';

	// Replace <code> with the language code, e.g. no, fr, en, ect.
	sceditor.locale['<code>'] = {

		// Original string is on the left, place the translation between
		// the quotes on the right
		'Bold': '',
		'Italic': '',
		'Underline': '',
		'Strikethrough': '',
		'Subscript': '',
		'Superscript': '',
		'Align left': '',
		'Center': '',
		'Align right': '',
		'Justify': '',
		'Font Name': '',
		'Font Size': '',
		'Font Color': '',
		'Remove Formatting': '',
		'Cut': '',
		'Your browser does not allow the cut command. Please use the keyboard shortcut Ctrl/Cmd-X': '',
		'Copy': '',
		'Your browser does not allow the copy command. Please use the keyboard shortcut Ctrl/Cmd-C': '',
		'Paste': '',
		'Your browser does not allow the paste command. Please use the keyboard shortcut Ctrl/Cmd-V': '',
		'Paste your text inside the following box:': '',
		'Paste Text': '',
		'Bullet list': '',
		'Numbered list': '',
		'Undo': '',
		'Redo': '',
		'Rows:': '',
		'Cols:': '',
		'Insert a table': '',
		'Insert a horizontal rule': '',
		'Code': '',
		'Width (optional):': '',
		'Height (optional):': '',
		'Insert an image': '',
		'E-mail:': '',
		'Insert an email': '',
		'URL:': '',
		'Insert a link': '',
		'Unlink': '',
		'More': '',
		'Insert an emoticon': '',
		'Video URL:': '',
		'Insert': '',
		'Insert a YouTube video': '',
		'Insert current date': '',
		'Insert current time': '',
		'Print': '',
		'View source': '',
		'Description (optional):': '',
		'Enter the image URL:': '',
		'Enter the e-mail address:': '',
		'Enter the displayed text:': '',
		'Enter URL:': '',
		'Enter the YouTube video URL or ID:': '',
		'Insert a Quote': '',
		'Invalid YouTube video': '',
		'Drop files here': '',

		// month format, replace - with the date format separator and order in the
		// order used
		dateFormat: 'day-month-year'
	};
})();

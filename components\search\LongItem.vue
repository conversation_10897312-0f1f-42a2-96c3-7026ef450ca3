<template>
  <div class="long-item">
    <a :href="$i18n.path(`detail/${item.aid}`)" target="_blank">
      <div
        class="cover hover"
        :class="{
          horizontal: item.cover_type === 0,
          vertical: item.cover_type === 1
        }"
        :style="{ 'background-image': `url(${item.cover})` }"
      ></div>
    </a>
    <div class="info">
      <!-- eslint-disable vue/no-v-html -->
      <a :href="$i18n.path(`detail/${item.aid}`)" class="title" target="_blank" :title="item.title" v-html="highLight(item.title)"></a>
      <div class="detail">
        <a :href="$i18n.path(`profile/${item.uid}`)" target="_blank" class="user-info">
          <img :src="item.avatar" class="user-avatar" alt="" />
          <span class="user-name">{{ item.author }}</span>
        </a>
        <div class="extra-info">{{ item.time }}</div>
        <div class="extra-info">{{ item.gid }} - {{ item.parent_gid }}</div>
      </div>
      <div class="card-info">
        <div class="card-count">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 730.49 426.86">
            <path
              d="M400,577.54c-94.2,0-170.85-76.64-170.85-170.85S305.82,235.84,400,235.84s170.85,76.64,170.85,170.85S494.22,577.54,400,577.54Zm0-303.06c-72.89,0-132.2,59.31-132.2,132.21s59.31,132.2,132.2,132.2,132.21-59.31,132.21-132.2S472.92,274.48,400,274.48Z"
              transform="translate(-34.78 -193.26)"
            />
            <path
              d="M408.76,620.11c-199.46,0-318-118.77-359.76-169.84h0a62.65,62.65,0,0,1-1.08-78.12c41.42-53.8,158.53-178.88,351.35-178.88,194.14,0,311.45,126.31,352.89,180.62a62.78,62.78,0,0,1,1.34,75C716.65,500.36,609.31,620.11,408.76,620.11ZM78.93,425.81c38.23,46.81,146.92,155.66,329.83,155.66,182.61,0,279.94-108.46,313.3-155.1a24.33,24.33,0,0,0-.62-29.06c-38-49.73-145.3-165.41-322.17-165.41-175.64,0-282.79,114.55-320.72,163.8a24.16,24.16,0,0,0,.38,30.11Z"
              transform="translate(-34.78 -193.26)"
            />
          </svg>
          {{ item.hits }}
        </div>
        <div class="card-count">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 624 447.5">
            <path
              d="M602.88,190.18h-407A101.39,101.39,0,0,0,94.65,291.45V521.9A101.4,101.4,0,0,0,195.89,623.19h407A101.41,101.41,0,0,0,704.17,521.9V291.45A101.4,101.4,0,0,0,602.88,190.18ZM678.77,521.9a76,76,0,0,1-75.89,75.89h-407a76,76,0,0,1-75.84-75.89V291.45a75.94,75.94,0,0,1,75.84-75.87h407a76,76,0,0,1,75.89,75.87Z"
              transform="translate(-87.41 -182.94)"
            />
            <path
              d="M602.89,630.44h-407A108.64,108.64,0,0,1,87.41,521.9V291.45A108.62,108.62,0,0,1,195.89,182.94h407A108.64,108.64,0,0,1,711.41,291.45V521.9A108.66,108.66,0,0,1,602.89,630.44Zm-407-433a94.11,94.11,0,0,0-94,94V521.9a94.12,94.12,0,0,0,94,94h407a94.14,94.14,0,0,0,94-94V291.45a94.13,94.13,0,0,0-94-94ZM602.89,605h-407a83.2,83.2,0,0,1-83.08-83.14V291.45a83.2,83.2,0,0,1,83.08-83.12h407A83.23,83.23,0,0,1,686,291.45V521.9A83.24,83.24,0,0,1,602.89,605Zm-407-382.21a68.69,68.69,0,0,0-68.59,68.62V521.9a68.71,68.71,0,0,0,68.59,68.65h407a68.73,68.73,0,0,0,68.65-68.65V291.45a68.71,68.71,0,0,0-68.65-68.62Z"
              transform="translate(-87.41 -182.94)"
            />
            <circle cx="151.32" cy="115.79" r="26.04" />
            <path d="M586.13,322.88H318.72a24.16,24.16,0,0,1,0-48.31H586.13a24.16,24.16,0,0,1,0,48.31Z" transform="translate(-87.41 -182.94)" />
            <circle cx="151.32" cy="223.02" r="26.04" />
            <path
              d="M526.39,430.84h-.08l-207.66-.72a24.16,24.16,0,0,1,.07-48.31h.08l207.66.72a24.16,24.16,0,0,1-.07,48.31Z"
              transform="translate(-87.41 -182.94)"
            />
            <path d="M390.41,537.35H318.72a24.16,24.16,0,0,1,0-48.31h71.69a24.16,24.16,0,0,1,0,48.31Z" transform="translate(-87.41 -182.94)" />
          </svg>
          {{ item.comments }}
        </div>
        <!-- <div class="data-icon">
          <img src="~@/assets/images/coin.svg" class="icon-coin" alt="" />
          563
        </div>
        <div class="data-icon">
          <img
            src="~@/assets/images/collection.svg"
            class="icon-collection"
            alt=""
          />
          563
        </div>
        <div class="data-icon">
          <span class="icon-forward">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 767.75 576.45">
              <title>Forward</title>
              <path
                d="M728.75,167,457.81,10.57c-52-30-117,7.5-117,67.54v60.61C157.21,164.89,81.12,248.49,37.23,334.64a324.63,324.63,0,0,0-29.9,90.2c-1.87,10.45-2.73,7.82-5.86,39.53-3.53,35.87-.42,62.29,6.34,92.58,5.2,23.37,28.45,26.46,38.67,5.37l2.21-4.54c51-115.45,168.75-204.44,292.15-213V391c0,60,65,97.57,117,67.55l271-156.43C780.74,272.07,780.74,197,728.75,167Z"
                transform="translate(0 -0.01)"
              />
            </svg>
          </span>
          563
        </div>
        <div class="data-icon">
          <span class="icon-fabulous">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 596.85 626.72">
              <title>Fabulous</title>
              <path
                d="M54.78,192.55C24.5,192.55,0,221.16,0,256.45v297.9c0,35.29,24.54,63.9,54.82,63.9s54.83-28.61,54.83-63.9V256.45C109.65,221.16,85.1,192.55,54.78,192.55Z"
                transform="translate(0.04 0)"
              />
              <path
                d="M509.31,213.62H351l39.23-96.08C406.18,78.42,384.18,0,331.11,0c-34.22,0-64.5,17.17-74.81,42.41L165.2,198.66a64.09,64.09,0,0,0-8.72,32.27V559.32c0,37.23,39.15,67.4,87.45,67.4h197c42,0,78.1-23,85.92-54.85l68.41-278.3C605.43,252.06,564.13,213.62,509.31,213.62Z"
                transform="translate(0.04 0)"
              />
            </svg>
          </span>
          563
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    kw: {
      type: String,
      required: true
    },
    item: {
      type: Object,
      required: true
    }
  },
  methods: {
    highLight(title) {
      const regexpStr = new RegExp(this.kw, 'ig');
      let matchStr = title.match(regexpStr);
      if (matchStr && matchStr.length > 0) {
        matchStr = title.match(regexpStr)[0];
      }
      title = title.replace(regexpStr, `<span style="color: #39d7d9; ">${matchStr}</span>`);
      return title;
    }
  }
};
</script>
<style lang="scss" scoped>
.long-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 30px 0;
  border-bottom: 1px solid #eee;
  .cover {
    border-radius: 5px;
    overflow: hidden;
    background-position: top center;
    background-repeat: no-repeat;
    background-size: cover;
    &.vertical {
      // 垂直封面
      min-width: 88px;
      height: 125px;
    }
    &.horizontal {
      // 水平封面
      min-width: 160px;
      height: 100px;
    }
  }
  .info {
    position: relative;
    margin-left: 15px;
    width: calc(100% - 105px);
    height: 125px;
    overflow: hidden;
    .title {
      display: block;
      color: $dark;
      font-size: 18px;
      text-decoration: none;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-decoration: none;
      .high-light {
        display: block;
        color: $primary;
      }
    }
    .detail {
      position: absolute;
      bottom: 30px;
      left: 0;
      font-size: 13px;
      color: $gray;
      height: 20px;
      display: flex;
      align-items: center;
      .user-info {
        text-decoration: none;
        color: $gray;
        display: flex;
        align-items: center;
        .user-avatar {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }
      .extra-info {
        margin-left: 20px;
      }
    }
    .card-info {
      position: absolute;
      left: 0;
      bottom: 0;
      display: flex;
      color: $gray;
      font-size: 13px;
      line-height: 11px;
      .card-count {
        margin-right: 20px;
        svg {
          width: 14px;
          height: 10px;
          fill: $gray;
        }
      }
      .data-icon {
        margin-right: 20px;
        .icon-coin {
          width: 12px;
          height: 12px;
        }
        .icon-collection {
          width: 12px;
          height: 12px;
        }
        .icon-forward {
          svg {
            width: 16px;
            height: 12px;
            fill: #69cff9;
          }
        }
        .icon-fabulous {
          svg {
            width: 13px;
            height: 14px;
            fill: #ffb236;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="bg-gray-f7">
    <Nav />
    <div class="layout-container">
      <nuxt />
    </div>
    <Footer />
  </div>
</template>

<script>
import Nav from '../components/Nav.vue';
import Footer from '../components/Footer.vue';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  components: { Nav, Footer },
  mounted() {
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  }
};
</script>

<template>
  <div class="module-container flex jc-between">
    <div class="module-left">
      <div class="flex jc-between ai-center">
        <div class="flex jc-between">
          <!-- <div class="type active">
            热门
          </div>
          <div class="type">
            时间
          </div> -->
        </div>
        <div class="flex jc-between">
          <div class="icon icon-photo" :class="{ active: cate_view_index === 0 }" @click="setViewIndex({ key: 'cate_view_index', val: 0 })">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 758.4 549">
              <circle cx="53.6" cy="53.6" r="53.6" />
              <path d="M729,228.8H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
              <circle cx="53.6" cy="274.5" r="53.6" />
              <circle cx="53.6" cy="495.4" r="53.6" />
              <path d="M729,449.7H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
              <path d="M729,670.6H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
            </svg>
          </div>
          <div class="icon icon-text" :class="{ active: cate_view_index === 1 }" @click="setViewIndex({ key: 'cate_view_index', val: 1 })">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 551.5 547">
              <path
                class="cls-1"
                d="M303.8,126.9H181.2a57,57,0,0,0-57,57V306.6a57,57,0,0,0,57,57H303.9a57,57,0,0,0,57-57V183.9A57.12,57.12,0,0,0,303.8,126.9Z"
                transform="translate(-124.2 -126.9)"
              />
              <path
                class="cls-1"
                d="M618.7,126.9H496a57,57,0,0,0-57,57V306.6a57,57,0,0,0,57,57H618.7a57,57,0,0,0,57-57V183.9A57,57,0,0,0,618.7,126.9Z"
                transform="translate(-124.2 -126.9)"
              />
              <path
                class="cls-1"
                d="M303.8,437.2H181.2a57,57,0,0,0-57,57V616.9a57,57,0,0,0,57,57H303.9a57,57,0,0,0,57-57V494.2A57.12,57.12,0,0,0,303.8,437.2Z"
                transform="translate(-124.2 -126.9)"
              />
              <path
                class="cls-1"
                d="M618.7,437.2H496a57,57,0,0,0-57,57V616.9a57,57,0,0,0,57,57H618.7a57,57,0,0,0,57-57V494.2A57,57,0,0,0,618.7,437.2Z"
                transform="translate(-124.2 -126.9)"
              />
            </svg>
          </div>
        </div>
      </div>

      <PartitionCard v-if="coverType == 0 && cate_view_index === 1" :items="dataList" />

      <PartitionOther v-if="coverType == 1 && cate_view_index === 1" :items="dataList" />

      <PartitiontText v-if="cate_view_index === 0" :items="dataList" />

      <div class="module-pages">
        <el-pagination
          class="my-pagination"
          layout="prev, pager, next, jumper"
          :prev-text="$t('paginate.prev')"
          :next-text="$t('paginate.next')"
          :disabled="loading"
          :hide-on-single-page="true"
          :current-page.sync="dataListPaginate.cur"
          :total="dataListPaginate.count"
          :page-size="dataListPaginate.size"
          @current-change="onPageChange"
        />
      </div>
    </div>
    <div class="module-right">
      <Rank v-if="coverType == 0" :ranks="ranksList" />
      <RankOther v-else :ranks="ranksList" />

      <!-- 广告插入 -->
      <div v-if="ad.length" class="poster">
        <div v-for="(item, index) in ad" :key="index" class="poster-box">
          <el-image :src="item.pic_url" lazy fit="cover" @click="doRecomAction(item)"></el-image>
        </div>
      </div>

      <div class="qr-code">
        <div class="QRcode">
          <img src="@/assets/images/qr_code.png" width="100%" />
        </div>
        <div class="QRtext">{{ $t('nav.scan_code_download') }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex';
import { category, recom } from '@/api';
import Rank from '@/components/Rank';
import RankOther from '@/components/RankOther';
import PartitiontText from '@/components/PartitiontText';
import PartitionOther from '@/components/PartitionOther';
import PartitionCard from '@/components/PartitionCard';

export default {
  components: {
    Rank,
    RankOther,
    PartitiontText,
    PartitionOther,
    PartitionCard
  },

  validate({ query, params }) {
    if (typeof query.type === 'undefined' || typeof params.category === 'undefined') {
      return false;
    }
    return true;
  },

  async asyncData({ params, $axios, query }) {
    const data = {
      coverType: parseInt(query.type),
      parent_gid: parseInt(params.category),
      gid: parseInt(params.subCategory)
    };

    const res = await $axios.$post(category.getArticleByCate, {
      parent_gid: data.parent_gid,
      gid: data.gid,
      page: 1
    });

    if (res.code === 0) {
      data.dataList = res.data.list;
      data.dataListPaginate = res.data.page_info;
    }

    const resRanks = await $axios.$post(recom.getRanks, {
      parent_gid: data.parent_gid,
      gid: data.gid
    });

    if (resRanks.code === 0) {
      data.ranksList = resRanks.data;
    }

    const ad = await $axios.$post('/api/recom/get-recommends', {
      class: 5
    });
    data.ad = ad.code === 0 && ad.data.length > 0 ? ad.data[0].items : [];
    return data;
  },

  data: () => ({
    paginate: {},
    parent_gid: '',
    gid: '',
    dataList: [],
    dataListPaginate: {},
    ranksList: [],
    coverType: '',
    loading: false,
    ad: []
  }),

  computed: {
    ...mapState('viewTab', ['cate_view_index'])
  },

  methods: {
    ...mapMutations('viewTab', ['setViewIndex']),
    onPageChange(page) {
      this.loading = true;
      this.$axios
        .$post(category.getArticleByCate, {
          parent_gid: this.parent_gid,
          gid: this.gid,
          page
        })
        .then((res) => {
          if (res.code === 0) {
            this.dataList = res.data.list;
            this.dataListPaginate = res.data.page_info;
            this.backtop();
          }
        })
        .finally(() => (this.loading = false));
    }
  }
};
</script>
<style lang="scss" scoped>
.module-container {
  margin-top: 10px;
  min-height: calc(100vh - 250px);

  .module-left {
    position: relative;
    width: 880px;
    .type {
      font-size: $ml;
      cursor: pointer;
      margin-right: $maxlg;
      color: $dark;
      &:hover,
      &.active {
        color: $primary;
      }
    }
    .icon {
      cursor: pointer;
      fill: #d7d7d7;
      &.icon-photo {
        width: 23px;
        height: 17px;
        svg {
          width: 100%;
          height: 100%;
        }
      }
      &.icon-text {
        margin-left: 20px;
        width: 17px;
        height: 17px;
        svg {
          width: 100%;
          height: 100%;
        }
      }
      &:hover,
      &.active {
        fill: $primary;
      }
    }

    .module-list {
      padding-bottom: 80px;
    }

    .module-pages {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .module-right {
    .poster {
      margin-top: 25px;
      .poster-box {
        cursor: pointer;
        width: 260px;
        height: 145px;
        margin-bottom: 10px;
        .el-image {
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
      }
    }
    .qr-code {
      position: relative;
      margin-top: 40px;
      width: 260px;
      height: 180px;
      border: 1px solid #eee;
      .QRcode {
        position: absolute;
        top: 25px;
        left: 50%;
        transform: translateX(-50%);
        width: 114px;
        height: 109px;
      }
      .QRtext {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 20px;
        font-size: 15px;
        color: $dark;
      }
    }
  }
}
</style>

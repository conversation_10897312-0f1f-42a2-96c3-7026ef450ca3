<template>
  <div class="profile-page flex jc-between">
    <!-- 侧边栏 -->
    <ProfileSide
      :is-me="isMe"
      :uid="user.uid"
      :level="user.level.level"
      :level-name="user.level.name"
      :username="user.nickname"
      :avatar="user.avatar"
      :fans="user.followers"
      :follows="user.following"
      :articles="user.articles"
      :btn-text="btnText"
      :medals="user.medals"
      @btn-click="clickHandler"
    />

    <!-- 右边: 右边信息 -->
    <div class="infomations">
      <!-- tabbar 标题 -->
      <TitleTabbar :title="$t('profile_home.my_articles')" :show-more="false" @tab-change="changeTabbar">
        <!-- 左边帖子 -->
        <template v-slot:articles>
          <div v-if="articles.length">
            <div v-for="(item, key) in articles" :key="key" class="tabbar-article-item flex-inline">
              <CardItem
                :img="item.cover"
                :title="item.title"
                :comments="item.comments"
                :views="item.hits"
                :has-icon="item.sid"
                :gid="item.parent_gid"
                @click="toPage(`/detail/${item.aid}`)"
                @click1="toPage(`/themereply/${item.aid}`)"
              />
            </div>
          </div>
          <Loading v-if="booksLoading && articles.length === 0" />
          <el-pagination
            class="my-pagination"
            layout="prev, pager, next, jumper"
            :prev-text="$t('paginate.prev')"
            :next-text="$t('paginate.next')"
            :disabled="articlesLoading"
            :hide-on-single-page="true"
            :current-page="articles_paginate.cur"
            :total="articles_paginate.count"
            :page-size="articles_paginate.size"
            @current-change="articlePageChange"
          />
        </template>

        <!-- 右边图书 -->
        <template v-slot:books>
          <div>
            <div v-for="(item, key) in books" :key="key" class="tabbar-article-item flex-inline jc-center">
              <CardItem
                :img="item.cover"
                mode="horizontal"
                :title="item.title"
                :has-icon="item.sid"
                :gid="item.parent_gid"
                @click="toPage(`/detail/${item.aid}`)"
                @click1="toPage(`/themereply/${item.aid}`)"
              >
                <div class="book-info flex jc-between">
                  <span class="flex-1 text-hide-1">{{ item.group_name }}</span>
                  <span class="flex-1 text-right">{{ item.time | date2short }}</span>
                </div>
              </CardItem>
            </div>
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="booksLoading"
              :hide-on-single-page="true"
              :current-page="books_paginate.cur"
              :total="books_paginate.count"
              :page-size="books_paginate.size"
              @current-change="booksPageChange"
            />
          </div>
        </template>
      </TitleTabbar>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import CardItem from '@/components/CardItem.vue';
import TitleTabbar from '@/components/TitleTabbar.vue';
import ProfileSide from '@/components/ProfileSide.vue';
import Loading from '@/components/Loading.vue';

export default {
  layout: 'profile',
  components: {
    CardItem,
    TitleTabbar,
    ProfileSide,
    Loading
  },

  async fetch({ params, store }) {
    const uid = Number(params.id);
    await store.dispatch('profile/getProfileData', { page: 1, type: 1, uid });
    await store.dispatch('profile/getUserinfo', { uid });
  },

  asyncData({ params }) {
    return {
      uid: params.id
    };
  },

  data: () => ({
    uid: 0, // 用户 uid
    articlesLoading: false, // 文章是否正在加载中
    booksLoading: false // 图书是否正在加载中
  }),

  computed: {
    ...mapGetters('login', ['isLogin', 'loginUser']),
    ...mapState('profile', ['user', 'articles', 'books', 'articles_paginate', 'books_paginate']),

    isMe() {
      if (!this.isLogin) {
        return false;
      }
      return this.uid === this.loginUser.uid;
    },

    btnText() {
      // 已经关注显示: "取消关注" 未关注显示: "关注"
      if (this.user.followed) {
        return this.$t('components.btn_unfollow');
      } else {
        return this.$t('components.btn_follow');
      }
    }
  },

  methods: {
    // 文章分页
    articlePageChange(page) {
      this.articlesLoading = true;
      this.$store
        .dispatch('profile/getProfileData', {
          page,
          type: 0,
          uid: this.uid
        })
        .finally(() => {
          this.articlesLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // 图书分页
    booksPageChange(page) {
      this.booksLoading = true;
      this.$store
        .dispatch('profile/getProfileData', {
          page,
          type: 1,
          uid: this.uid
        })
        .finally(() => {
          this.booksLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    changeTabbar(index) {
      // 如果没有数据就去获取, 有数据就不获取
      if (index === 1 && this.articles.length === 0) {
        this.articlePageChange(1);
      }
    },

    clickHandler() {
      if (this.isMe) {
        return this.toPage(`/settings/${this.loginUser.uid}`);
      }
      const act = this.user.followed ? 1 : 0;
      this.toggleFollowState(this.user.uid, act, (res) => {
        res && (this.user.followed = !this.user.followed);
      });
    }
  },

  head() {
    const title = this.user.nickname + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
.profile-page {
  margin-top: 30px;
  .infomations {
    width: 910px;
    padding: 30px 15px;
    background: #fff;
  }
  .book-info {
    width: 100%;
    line-height: 1.2;
  }
}
</style>

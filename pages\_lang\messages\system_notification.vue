<template>
  <div v-infinite-scroll="loadMore" class="msg-system-notification">
    <p class="top-title fs-md">{{ $t('msg_system.system_notification') }}</p>
    <ul class="messages">
      <li v-for="(item, index) in notifications" :key="index" class="message-item flex flex-cols">
        <!-- 消息顶部: 消息内容 -->
        <p class="msg-conents text-hide-3">{{ item.msg }}</p>
        <p class="fs-xs text-gray mar-top-10">
          <span class="mar-right-15">{{ item.title }}</span>
          <span>{{ item.time | date2short }}</span>
        </p>
      </li>

      <!-- 加载中 -->
      <li v-show="loading" class="message-item">
        <Loading style="max-height: 100px; padding-top: 30px;" />
      </li>

      <!-- 没有更多 -->
      <li v-if="noMore" class="message-item">
        <p class="text-gray flex jc-center">{{ this.$t('fans.no_more') }} ╮(╯▽╰)╭</p>
      </li>
    </ul>
  </div>
</template>

<script>
import Loading from '@/components/Loading.vue';
export default {
  layout: 'messages',
  components: { Loading },
  middleware: ['auth'],
  async asyncData({ $axios }) {
    const res = await $axios.$post('/api/sys-msg/get-sys-msg', { page: 1 });
    if (res.code === 0) {
      return { notifications: res.data };
    }
  },

  data: () => ({
    page: 1, // 当前页数
    loading: false, // 是否正在加载
    noMore: false, // 是否有更多
    notifications: [], // 系统通知
    currentShowReplyIdx: 0 // 当前显示回复的消息 index
  }),
  methods: {
    // 加载更多
    loadMore() {
      if (this.loading || this.noMore) return;
      this.page += 1;
      this.loading = true;
      this.$axios
        .$post('/api/sys-msg/get-sys-msg', { page: this.page })
        .then((res) => {
          this.loading = false;
          if (res.code === 0) {
            this.notifications = this.notifications.concat(res.data);
            if (res.data.length === 0) {
              --this.page;
              this.noMore = true;
            }
          }
        })
        .finally(() => (this.loading = false));
    },

    // 显示出当前消息的回复框(如果已经显示就隐藏)
    showReplyInput(index) {
      if (this.currentShowReplyIdx === index) {
        this.currentShowReplyIdx = -1;
      } else {
        this.currentShowReplyIdx = index;
      }
    }
  },

  head() {
    const title = this.$t('system_notification.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
.msg-system-notification {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  .top-title {
    padding: 15px;
    background: #fff;
    margin-bottom: 10px;
  }
  .messages {
    background: #fff;
    padding: 0 15px;
    .message-item {
      padding: 20px 0;
      border-bottom: 1px solid $gray-white;
      .msg-conents {
        width: 100%;
        position: relative;
        line-height: 24px;
        a {
          color: $link-color;
          text-decoration: none;
        }
      }
    }
  }
}
</style>

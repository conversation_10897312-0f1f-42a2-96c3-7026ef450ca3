<template>
  <div class="medal-steps">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'MedalSteps',
  props: {
    active: {
      type: Number,
      default: 0
    },
    finishStatus: {
      type: String,
      default: 'finish'
    },
    processStatus: {
      type: String,
      default: 'process'
    }
  },
  data() {
    return {
      steps: [],
      stepOffset: 0
    };
  },
  watch: {
    active(newVal, oldVal) {
      this.$emit('change', newVal, oldVal);
    },
    steps(steps) {
      steps.forEach((child, index) => {
        child.index = index;
      });
    }
  }
};
</script>

<style lang="scss" scope>
.medal-steps {
  display: flex;
  .medal-action {
    margin-top: -14px;
  }
  .medal-img-container {
    border-radius: 5px;
    border: 1px solid #f3f3f3;
  }
}
</style>

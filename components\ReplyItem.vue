<template>
  <div class="reply-item-container flex">
    <img class="avatar hover" :src="avatar" @click="$emit('click')" />
    <div class="content-box flex flex-cols">
      <p class="author-reply-to">
        <span class="fs-sm">
          <span>{{ author }}</span
          ><span class="lv-icon mar-left-10" :class="[`lv-${lv}`]"> {{ lvName }} </span
          ><span v-if="floorMaster" class="lv-icon lv-0 mar-left-10">{{ $t('detail.floor_master') }}</span
          ><el-image
            v-if="typeof medal.img !== undefined && medal.img"
            :src="medal.img"
            :title="medal.name"
            fit="contain"
            lazy
            class="user-medal-image mar-left-10"
          ></el-image>
        </span>
        <span v-if="replyName" class="fs-sm">
          {{ $t('components.btn_reply') }}
          <span class="to-name text-link">@{{ replyName }}</span>
        </span>
        <span class="fs-sm">:</span>

        <!-- eslint-disable-next-line vue/no-v-html -->
        <span class="content fs-sm" v-html="content"></span>
      </p>
      <p class="pad-top-5 fs-sm">
        <span class="text-gray mar-right-20">{{ date }}</span>
        <span class="text-gray btn mar-right-20" @click="reply">
          {{ $t('components.btn_reply') }}
        </span>
        <span v-if="showDelete" class="text-gray btn" @click="del">
          {{ $t('components.btn_delete') }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    floorMaster: {
      // 是否是楼主
      type: Boolean,
      default: false
    },
    avatar: {
      // 作者头像
      type: String,
      required: true
    },
    date: {
      // 回复发布日期
      type: String,
      required: true
    },
    author: {
      // 作者名称
      type: [String, Number],
      required: true
    },
    replyName: {
      // 被回复的人的名称
      type: [String, Number],
      default: ''
    },
    lv: {
      // 作者等级
      type: [String, Number],
      required: true
    },
    lvName: {
      // 等级名称
      type: String,
      required: true
    },
    content: {
      // 回复内容
      type: [String, Number],
      required: true
    },
    showDelete: {
      // 是否显示删除
      type: Boolean,
      default: false
    },
    medal: {
      // 勋章
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    reply() {
      this.$emit('reply');
    },
    del() {
      // 删除
      this.$emit('del');
    }
  }
};
</script>

<style lang="scss" scope>
.reply-item-container {
  .avatar {
    width: 30px;
    height: 30px;
    border-radius: 30px;
  }

  .content-box {
    padding-left: 15px;
    .user-medal-image {
      width: 30px;
      height: 30px;
      vertical-align: middle;
    }
    .content {
      line-height: 1.5;
      img {
        vertical-align: text-top !important;
      }
    }
  }
}
</style>

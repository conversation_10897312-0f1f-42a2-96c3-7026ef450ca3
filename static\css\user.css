/* ==UserStyle==
@name			LK_Dark Mode
<AUTHOR>
@version		1.3.1
@preprocessor	default
==/UserStyle== */

body,
html,
.footer,
.el-dialog,
.input-control,
.lv-icon.lv-0.border,
.wrap,
.search-input,
.bg-gray-f7,
.comment-content-input,
.btn.img-btn,
.btn.emoji-btn,
.content-item-reply,
.access-permission-denied-container,
.new-collection-dialog .item .select,
.new-collection-dialog .item .flex .input-control,
.el-input__inner,
.detail-contents .left-contents #comments .top-tips,
.comment-item-container .content .quote,
.coins-container,
.__nuxt-error-page,
.user-info-bar-container .line,
.el-message-box {
  background-color: #1e1e1e !important;
}

.nav,
.fixed-download,
.index-menu,
.nav-search-box,
.nav-avatar-info,
.nav-hot,
.sidebar-menu,
.top-title,
.messages,
.nav-message-mark,
.sidebar,
.contents,
.articles-container,
.sidebar-menus,
.top-tips,
.select,
.flex .input-control,
.profile-page .infomations,
.sidebar .line {
  background-color: #282828 !important;
}

.el-pager li,
.btn-next,
.btn-prev,
.collection-infos .top-title,
.collection-index-item-container,
.detail-contents .right-recommend .author,
.fixed-container .download,
.article font,
.element_ui_layer .btn-140.btn-border,
.el-dialog__wrapper.rate-dialog > div > div.el-dialog__body > div.flex.jc-between.ai-center > textarea,
.el-message-box .el-button,
.article-item-img-container {
  background-color: rgba(0, 0, 0, 0) !important;
}

.show-more-btn,
.el-message-box .el-button.el-button--primary {
  background-color: #39d7d9 !important;
}

.nav-name,
.nav-dltext,
.btn,
.nav-language a,
.container ul li a,
.unLogin,
.nav-history,
.fixed-container,
.nav-button,
.icon-title,
.article-item-title,
.title,
.nav-avatar-topinfo,
.nav-avatar-bottominfor,
.fs-xs,
.container p,
.container h6,
.input-control,
.tag,
.search-input,
.search-menu ul li a,
.hotSearch-content-box,
.close-tags,
.top-title,
.messages,
.link,
.comment-content-input,
.fs-sm,
.content,
.fs15,
.article-content,
.article-title,
.nav-search-box,
.fs-ml,
.item-text,
.menu-item,
.fs-md,
.select,
.item,
.el-pager .number,
.btn-next,
.btn-prev,
.el-input__inner,
.el-pagination__jump,
li.btn-quicknext,
li.btn-quickprev,
.text-dark,
.article span,
.module-title,
.element_ui_layer .btn-140.btn-border,
.el-dialog__title,
.el-message-box__title,
.el-message-box__message,
.el-message-box .el-button {
  color: #ffffff !important;
}

.btn-140,
.input-btn,
.lv-icon,
.btn-close,
.right-btns .btn,
.btn-publish,
.btn.publish-btn,
.author-follow-btn,
.show-more-btn,
.btn-search,
.el-message-box .el-button.el-button--primary,
.unlock {
  color: #1e1e1e !important;
}

.nav-button-publish,
.sidebar .user-avatar-box .btn {
  color: #282828 !important;
}

.access-permission-denied-container .flex-cols img {
  display: none;
}

.index-flow {
  background-color: #282828 !important;
}

.article a,
.lv-icon.lv-0.border {
  color: #39d7d9 !important;
}

.el-pagination .btn-next,
.el-pagination .btn-prev {
  height: 30px;
}

.detail-contents .right-recommend .author-follow-btn,
.detail-contents .right-recommend .author .user-avatar-container,
.detail-contents .right-recommend .author .user-avatar-container .avatar,
.sidebar-buttons .btn-item,
.show-more-btn,
.btn.img-btn,
.btn.emoji-btn,
.btn.publish-btn,
.nav-button-publish,
.tags-menu .tag,
.search-input,
.btn-search,
.btn-rate,
.btn-collect,
.btn-publish,
.collection-item-container .right-btns .btn,
.el-message-box .el-button,
.sidebar .user-avatar-box .btn,
.unlock {
  border-radius: 5px !important;
}

.svg-icon {
  fill: #ffffff !important;
}

.detail-contents .right-recommend .author,
.el-message-box {
  border: none;
}

.nav-hot,
.fixed-download,
.nav-avatar-info {
  border-color: #282828 !important;
}

.nav-btn .btn {
  font-size: 15px;
}

.nav {
  flex-grow: 1;
  justify-content: center;
}

.nav {
  width: 100% !important;
  min-width: 1190px !important;
  border-radius: 0 !important;
}

.nav-logo,
.nav-dl,
.nav-btn,
.nav-search,
.nav-button,
.nav-history,
.nav-button-publish,
.nav-name,
.nav-avatar,
.nav .login .lv-icon {
  margin: 0 15px !important;
}

.nav-btn {
  min-width: 0 !important;
}

.nav-dltext {
  margin: 0 !important;
}

.navbar {
  position: absolute !important;
  width: 100%;
}

/* .index-menu .container ul li {
	margin: auto !important;
} */

.nav .nav-dl .dl-image {
  transform: translateY(1.5px);
}

.icon-search path {
  fill: #282828 !important;
}

.social-share {
  display: none;
}

.author-name,
.author-signature,
.author-infos,
.other-recommends-title,
.other-recommends-article-item {
  display: none;
}

.detail-contents .right-recommend .author-follow-btn,
.detail-contents .right-recommend .other-recommends-article .show-more-btn {
  width: 60px;
}

.detail-contents .right-recommend .other-recommends {
  padding-top: 35px;
}

.detail-contents .right-recommend .other-recommends-article {
  padding-top: 0;
}

.detail-contents .right-recommend {
  width: 60px;
  position: fixed;
  right: 20px;
}

.detail-contents .right-recommend .author .user-avatar-container .avatar {
  width: 54px !important;
  height: 54px !important;
}

.sidebar-buttons {
  right: 25px;
  bottom: 1%;
  z-index: 999;
}

.detail-contents .right-recommend .other-recommends-article .show-more-btn {
  height: 30px;
  line-height: 30px;
}

.detail-contents .left-contents {
  width: 880px;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

.author-follow-btn.bg-gray.btn.btn-gray {
  background-color: rgba(0, 0, 0, 0) !important;
  border-color: #39d7d9 !important;
  color: #39d7d9 !important;
}
.nav-avatar-info,
.nav-avatar-image,
.nav *,
.index-menu *,
.tags-menu *,
.my-pagination * {
  transition: 0.2s ease-out;
  outline: 0 !important;
}

.nav-avatar-info {
  opacity: 0;
  visibility: hidden;
  transform: translateY(0);
  height: auto !important;
  display: block !important;
  z-index: -1 !important;
  border: 0 !important;
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.5);
}

.nav-avatar-topinfo {
  padding: 0 !important;
  height: auto !important;
  border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

.nav-avatar-image {
  position: relative;
  z-index: 4;
}

.nav .login .nav-avatar:hover .nav-avatar-info {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: initial;
  z-index: 3 !important;
}

.nav .login .nav-avatar:hover .nav-avatar-image {
  transform: scale(2) translateY(25%);
  opacity: 1;
  visibility: visible;
  pointer-events: initial;
}

.nav-search {
  border-radius: 0 !important;
  border: 0 !important;
  border-bottom: 1px solid #ffffff !important;
  opacity: 0.4;
  padding: 5px !important;
}

img.nav-search-icon {
  filter: grayscale(100%);
}

.nav-search:hover,
.nav-search:focus-within,
.nav-search-box:focus {
  opacity: 1;
}

.el-pager .number.active,
.el-pager li:hover,
.el-pagination button:hover,
.tag.nuxt-link-active,
.tags-menu .tag:hover,
.el-message-box .el-button:hover,
.publish_mgr_container .right-btns.btn-select-muilt:hover {
  color: #39d7d9 !important;
  border-color: #39d7d9 !important;
  background-color: rgba(0, 0, 0, 0) !important;
}

.container ul li a.active,
.container ul li a:hover,
.module-title .title:hover,
.nav-btn .btn:hover,
.nav .unLogin:hover,
.nav-button:hover,
.nav-history:hover {
  color: #39d7d9 !important;
}

.nav-hot {
  display: none !important;
}

.nav-avatar-bottominfor {
  padding: 0 !important;
  height: 160px;
}

.nav-avatar-bottominfor div {
  margin: 0 !important;
}

.nav-avatar-personal:hover,
.nav-avatar-release:hover,
.nav-avatar-collection:hover,
.nav-avatar-quit:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.nav-avatar-quit {
  font-size: 13px;
}

.nav-avatar-collections {
  display: block !important;
  margin: 0 !important;
  height: 80px !important;
  width: 100% !important;
}

.nav-avatar-personal,
.nav-avatar-release,
.nav-avatar-collection,
.nav-avatar-quit {
  margin: 0 !important;
  height: 40px !important;
  width: 100% !important;
  padding: 0 !important;
  justify-content: center !important;
  align-items: center !important;
}

.nav-personal-icon,
.nav-release-icon,
.nav-collection-icon {
  position: absolute !important;
  left: 20px !important;
  filter: grayscale(100%);
}

.nav-avatar-personal div:nth-child(2),
.nav-avatar-release div:nth-child(2),
.nav-avatar-collection div:nth-child(2),
.nav-avatar-quit {
  height: 40px !important;
  line-height: 40px !important;
  width: 100% !important;
  text-align: center !important;
  z-index: 1;
}

.nav-avatar-fans {
  display: flex;
  padding: 0 !important;
  margin-top: 50px;
  margin-bottom: 10px;
  height: 40px !important;
  line-height: 20px;
}

.nav-avatar-fan,
.nav-avatar-follow {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  align-self: stretch;
}

.nav-avatar-fan span:nth-child(2),
.nav-avatar-follow span:nth-child(2) {
  margin-left: 0 !important;
  color: #ffffff !important;
}

.nav-avatar-fan:hover span,
.nav-avatar-follow:hover span {
  color: #39d7d9 !important;
}

.nav-avatar-coins {
  display: none !important;
}

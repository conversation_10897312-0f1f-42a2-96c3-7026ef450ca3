<template>
  <div>
    <div class="wrapper">
      <div class="bgImg" :style="background" @click="doRecomAction(topbar)"></div>
      <Nav />
    </div>
    <!-- <IndexMenu /> -->
    <div class="container">
      <nuxt />
    </div>
    <Footer />
    <DialogMobileTips v-if="isShowMobileDialog" />
  </div>
</template>
<script>
import { mapState } from 'vuex';
import Nav from '@/components/Nav';
// import IndexMenu from '@/components/IndexMenu';
import Footer from '@/components/Footer';
import DialogMobileTips from '@/components/DialogMobileTips';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  components: {
    Nav,
    // IndexMenu,
    Footer,
    DialogMobileTips
  },
  data: () => ({
    isShowMobileDialog: false
  }),
  computed: {
    ...mapState('home', ['topbar']),
    background() {
      return this.topbar && this.topbar.pic_url ? `background-image: url(${this.topbar.pic_url})` : '';
    }
  },
  mounted() {
    if (process.browser) {
      this.isShowMobileDialog = this.isMobile(navigator.userAgent) && !sessionStorage.getItem('close_mobile_tips');
    }
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  }
};
</script>
<style lang="scss">
body {
  margin: 0;
}

.dialog-mobile-tips-fixed {
  position: fixed;
}
</style>
<style lang="scss" scoped>
html {
  font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  word-spacing: 1px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

.wrapper {
  // max-width: 1920px;
  width: 100%;
  min-width: 1160px;
  height: 230px;
  margin: 0 auto;
  // background-image: url('../assets/images/wrapper.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top center;
  position: relative;
  z-index: 1;
  .bgImg {
    position: absolute;
    /*background-image: url(https://res.lightnovel.us/temp/pc/topbar.jpg);*/
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    width: 100%;
    height: 230px;
    cursor: pointer;
  }
  .nav {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 1190px;
    height: 50px;
    margin: 0 auto;
    background: white;
    box-shadow: 0px 0px 15px 0px rgba(1, 1, 1, 0.1);
    border-radius: 0px 0px 20px 20px;
  }
}
.container {
  // max-width: 1920px;
  width: 100%;
  min-width: 1160px;
  margin: 0 auto;
  padding: 0 !important;
  min-height: calc(100vh - 500px);
}
</style>

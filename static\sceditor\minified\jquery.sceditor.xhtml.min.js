/* SCEditor v2.1.3 | (C) 2017, <PERSON> | sceditor.com/license */

!function(o){"use strict";function e(e,t){return typeof t===e}o=o&&o.hasOwnProperty("default")?o.default:o;var ye=e.bind(null,"string"),xe=e.bind(null,"undefined"),we=e.bind(null,"function"),i=e.bind(null,"number");function t(e){return!Object.keys(e).length}function Ce(e,t){for(var n=e===!!e,o=n?2:1,i=n?t:e,r=!!n&&e;o<arguments.length;o++){var a=arguments[o];for(var l in a){var c=a[l];if(!xe(c)){var s=null!==c&&"object"==typeof c&&Object.getPrototypeOf(c)===Object.prototype,u=Array.isArray(c);i[l]=r&&(s||u)?Ce(!0,i[l]||(u?[]:{}),c):c}}}return i}function Ee(e,t){var n=e.indexOf(t);-1<n&&e.splice(n,1)}function Se(t,n){if(Array.isArray(t)||"length"in t&&i(t.length))for(var e=0;e<t.length;e++)n(e,t[e]);else Object.keys(t).forEach(function(e){n(e,t[e])})}var r={},ke=1,Te=3;function a(e){return e=parseFloat(e),isFinite(e)?e:0}function De(e,t,n){var o=(n||document).createElement(e);return Se(t||{},function(e,t){"style"===e?o.style.cssText=t:e in o?o[e]=t:o.setAttribute(e,t)}),o}function Ne(e,t){for(var n=e||{};(n=n.parentNode)&&!/(9|11)/.test(n.nodeType);)if(!t||je(n,t))return n}function Me(e,t){return je(e,t)?e:Ne(e,t)}function Re(e){e.parentNode&&e.parentNode.removeChild(e)}function He(e,t){e.appendChild(t)}function Fe(e,t){return e.querySelectorAll(t)}var _e=!0;function ze(n,e,o,i,r){e.split(" ").forEach(function(e){var t;ye(o)?(t=i["_sce-event-"+e+o]||function(e){for(var t=e.target;t&&t!==n;){if(je(t,o))return void i.call(t,e);t=t.parentNode}},i["_sce-event-"+e+o]=t):(t=o,r=i),n.addEventListener(e,t,r||!1)})}function Ae(n,e,o,i,r){e.split(" ").forEach(function(e){var t;ye(o)?t=i["_sce-event-"+e+o]:(t=o,r=i),n.removeEventListener(e,t,r||!1)})}function Oe(e,t,n){if(arguments.length<3)return e.getAttribute(t);null==n?l(e,t):e.setAttribute(t,n)}function l(e,t){e.removeAttribute(t)}function Be(e){Pe(e,"display","none")}function Le(e){Pe(e,"display","")}function Ie(e){Ge(e)?Be(e):Le(e)}function Pe(n,e,t){if(arguments.length<3){if(ye(e))return 1===n.nodeType?getComputedStyle(n)[e]:null;Se(e,function(e,t){Pe(n,e,t)})}else{var o=(t||0===t)&&!isNaN(t);n.style[e]=o?t+"px":t}}function Ve(e,t,n){var o=arguments.length,i={};if(e.nodeType===ke){if(1===o)return Se(e.attributes,function(e,t){/^data\-/i.test(t.name)&&(i[t.name.substr(5)]=t.value)}),i;if(2===o)return Oe(e,"data-"+t);Oe(e,"data-"+t,String(n))}}function je(e,t){var n=!1;return e&&e.nodeType===ke&&(n=(e.matches||e.msMatchesSelector||e.webkitMatchesSelector).call(e,t)),n}function We(e,t){return t.parentNode.insertBefore(e,t)}function c(e){return e.className.trim().split(/\s+/)}function qe(e,t){return je(e,"."+t)}function Ue(e,t){var n=c(e);n.indexOf(t)<0&&n.push(t),e.className=n.join(" ")}function $e(e,t){var n=c(e);Ee(n,t),e.className=n.join(" ")}function Ye(e,t,n){(n=xe(n)?!qe(e,t):n)?Ue(e,t):$e(e,t)}function Ke(e,t){if(xe(t)){var n=getComputedStyle(e),o=a(n.paddingLeft)+a(n.paddingRight),i=a(n.borderLeftWidth)+a(n.borderRightWidth);return e.offsetWidth-o-i}Pe(e,"width",t)}function Xe(e,t){if(xe(t)){var n=getComputedStyle(e),o=a(n.paddingTop)+a(n.paddingBottom),i=a(n.borderTopWidth)+a(n.borderBottomWidth);return e.offsetHeight-o-i}Pe(e,"height",t)}function Qe(e,t,n){var o;we(window.CustomEvent)?o=new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}):(o=e.ownerDocument.createEvent("CustomEvent")).initCustomEvent(t,!0,!0,n),e.dispatchEvent(o)}function Ge(e){return!!e.getClientRects().length}function Je(e,t,n,o,i){for(e=i?e.lastChild:e.firstChild;e;){var r=i?e.previousSibling:e.nextSibling;if(!n&&!1===t(e)||!o&&!1===Je(e,t,n,o,i)||n&&!1===t(e))return!1;e=r}}function Ze(e,t,n,o){Je(e,t,n,o,!0)}function et(e,t){var n=(t=t||document).createDocumentFragment(),o=De("div",{},t);for(o.innerHTML=e;o.firstChild;)He(n,o.firstChild);return n}function tt(e){return e&&(!je(e,"p,div")||e.className||Oe(e,"style")||!t(Ve(e)))}function nt(e,t){var n=De(t,{},e.ownerDocument);for(Se(e.attributes,function(e,t){try{Oe(n,t.name,t.value)}catch(e){}});e.firstChild;)He(n,e.firstChild);return e.parentNode.replaceChild(n,e),n}var s="|body|hr|p|div|h1|h2|h3|h4|h5|h6|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|blockquote|center|";function ot(e){return!!/11?|9/.test(e.nodeType)&&"|iframe|area|base|basefont|br|col|frame|hr|img|input|wbr|isindex|link|meta|param|command|embed|keygen|source|track|object|".indexOf("|"+e.nodeName.toLowerCase()+"|")<0}function it(e,t){var n,o=(e||{}).nodeType||Te;return o!==ke?o===Te:"code"===(n=e.tagName.toLowerCase())?!t:s.indexOf("|"+n+"|")<0}function u(e,t){t.style.cssText=e.style.cssText+t.style.cssText}function rt(e){Je(e,function(e){var t,n=!it(e,!0);if(n&&it(e.parentNode,!0)){var o=function(e){for(;it(e.parentNode,!0);)e=e.parentNode;return e}(e),i=f(o,e),r=e;u(o,r),We(i,o),We(r,o)}if(n&&je(e,"ul,ol")&&je(e.parentNode,"ul,ol")){var a=("li",(t=e.previousElementSibling)?je(t,"li")?t:null:t);a||We(a=De("li"),e),He(a,e)}})}function d(e,t){return e?(t?e.previousSibling:e.nextSibling)||d(e.parentNode,t):null}function at(e){var t,n,o,i,r,a,l,c=Pe(e,"whiteSpace"),s=/line$/i.test(c),u=e.firstChild;if(!/pre(\-wrap)?$/i.test(c))for(;u;){if(a=u.nextSibling,t=u.nodeValue,(n=u.nodeType)===ke&&u.firstChild&&at(u),n===Te){for(o=d(u),i=d(u,!0),l=!1;qe(i,"sceditor-ignore");)i=d(i,!0);if(it(u)&&i){for(r=i;r.lastChild;)for(r=r.lastChild;qe(r,"sceditor-ignore");)r=d(r,!0);l=r.nodeType===Te?/[\t\n\r ]$/.test(r.nodeValue):!it(r)}t=t.replace(/\u200B/g,""),i&&it(i)&&!l||(t=t.replace(s?/^[\t ]+/:/^[\t\n\r ]+/,"")),o&&it(o)||(t=t.replace(s?/[\t ]+$/:/[\t\n\r ]+$/,"")),t.length?u.nodeValue=t.replace(s?/[\t ]+/g:/[\t\n\r ]+/g," "):Re(u)}u=a}}function f(e,t){var n=e.ownerDocument.createRange();return n.setStartBefore(e),n.setEndAfter(t),n.extractContents()}function lt(e){for(var t=0,n=0;e;)t+=e.offsetLeft,n+=e.offsetTop,e=e.offsetParent;return{left:t,top:n}}function p(e,t){var n,o,i=e.style;if(r[t]||(r[t]=t.replace(/^-ms-/,"ms-").replace(/-(\w)/g,function(e,t){return t.toUpperCase()})),o=i[t=r[t]],"textAlign"===t){if(n=i.direction,o=o||Pe(e,t),Pe(e.parentNode,t)===o||"block"!==Pe(e,"display")||je(e,"hr,th"))return"";if(/right/i.test(o)&&"rtl"===n||/left/i.test(o)&&"ltr"===n)return""}return o}var n,g,m,ct={toolbar:"bold,italic,underline,strike,subscript,superscript|left,center,right,justify|font,size,color,removeformat|cut,copy,pastetext|bulletlist,orderedlist,indent,outdent|table|code,quote|horizontalrule,image,email,link,unlink|emoticon,youtube,date,time|ltr,rtl|print,maximize,source",toolbarExclude:null,style:"jquery.sceditor.default.css",fonts:"Arial,Arial Black,Comic Sans MS,Courier New,Georgia,Impact,Sans-serif,Serif,Times New Roman,Trebuchet MS,Verdana",colors:"#000000,#44B8FF,#1E92F7,#0074D9,#005DC2,#00369B,#b3d5f4|#444444,#C3FFFF,#9DF9FF,#7FDBFF,#68C4E8,#419DC1,#d9f4ff|#666666,#72FF84,#4CEA5E,#2ECC40,#17B529,#008E02,#c0f0c6|#888888,#FFFF44,#FFFA1E,#FFDC00,#E8C500,#C19E00,#fff5b3|#aaaaaa,#FFC95F,#FFA339,#FF851B,#E86E04,#C14700,#ffdbbb|#cccccc,#FF857A,#FF5F54,#FF4136,#E82A1F,#C10300,#ffc6c3|#eeeeee,#FF56FF,#FF30DC,#F012BE,#D900A7,#B20080,#fbb8ec|#ffffff,#F551FF,#CF2BE7,#B10DC9,#9A00B2,#9A00B2,#e8b6ef",locale:Oe(document.documentElement,"lang")||"en",charset:"utf-8",emoticonsCompat:!1,emoticonsEnabled:!0,emoticonsRoot:"",emoticons:{dropdown:{":)":"emoticons/smile.png",":angel:":"emoticons/angel.png",":angry:":"emoticons/angry.png","8-)":"emoticons/cool.png",":'(":"emoticons/cwy.png",":ermm:":"emoticons/ermm.png",":D":"emoticons/grin.png","<3":"emoticons/heart.png",":(":"emoticons/sad.png",":O":"emoticons/shocked.png",":P":"emoticons/tongue.png",";)":"emoticons/wink.png"},more:{":alien:":"emoticons/alien.png",":blink:":"emoticons/blink.png",":blush:":"emoticons/blush.png",":cheerful:":"emoticons/cheerful.png",":devil:":"emoticons/devil.png",":dizzy:":"emoticons/dizzy.png",":getlost:":"emoticons/getlost.png",":happy:":"emoticons/happy.png",":kissing:":"emoticons/kissing.png",":ninja:":"emoticons/ninja.png",":pinch:":"emoticons/pinch.png",":pouty:":"emoticons/pouty.png",":sick:":"emoticons/sick.png",":sideways:":"emoticons/sideways.png",":silly:":"emoticons/silly.png",":sleeping:":"emoticons/sleeping.png",":unsure:":"emoticons/unsure.png",":woot:":"emoticons/w00t.png",":wassat:":"emoticons/wassat.png"},hidden:{":whistling:":"emoticons/whistling.png",":love:":"emoticons/wub.png"}},width:null,height:null,resizeEnabled:!0,resizeMinWidth:null,resizeMinHeight:null,resizeMaxHeight:null,resizeMaxWidth:null,resizeHeight:!0,resizeWidth:!0,dateFormat:"year-month-day",toolbarContainer:null,enablePasteFiltering:!1,disablePasting:!1,readOnly:!1,rtl:!1,autofocus:!1,autofocusEnd:!0,autoExpand:!1,autoUpdate:!1,spellcheck:!0,runWithoutWysiwygSupport:!1,startInSourceMode:!1,id:null,plugins:"",zIndex:null,bbcodeTrim:!1,disableBlockRemove:!1,parserOptions:{},dropDownCss:{}},h=navigator.userAgent,v=function(){for(var e=3,t=document,n=t.createElement("div"),o=n.getElementsByTagName("i");n.innerHTML="\x3c!--[if gt IE "+ ++e+"]><i></i><![endif]--\x3e",o[0];);return t.documentMode&&t.all&&window.atob&&(e=10),4===e&&t.documentMode&&(e=11),4<e?e:void 0}(),st="-ms-ime-align"in document.documentElement.style,ut=/iPhone|iPod|iPad| wosbrowser\//i.test(h),dt=((m=document.createElement("div")).contentEditable=!0,"contentEditable"in document.documentElement&&"true"===m.contentEditable&&(g=/Opera Mobi|Opera Mini/i.test(h),/Android/i.test(h)&&(g=!0,/Safari/.test(h)&&(g=!(n=/Safari\/(\d+)/.exec(h))||!n[1]||n[1]<534)),/ Silk\//i.test(h)&&(g=!(n=/AppleWebKit\/(\d+)/.exec(h))||!n[1]||n[1]<534),ut&&(g=/OS [0-4](_\d)+ like Mac/i.test(h)),/Firefox/i.test(h)&&(g=!1),/OneBrowser/i.test(h)&&(g=!1),"UCWEB"===navigator.vendor&&(g=!1),v<=9&&(g=!0),!g)),b=/^(https?|s?ftp|mailto|spotify|skype|ssh|teamspeak|tel):|(\/\/)|data:image\/(png|bmp|gif|p?jpe?g);/i;function ft(e){return e.replace(/([\-.*+?^=!:${}()|\[\]\/\\])/g,"\\$1")}function pt(e,t){if(!e)return e;var n={"&":"&amp;","<":"&lt;",">":"&gt;","  ":"&nbsp; ","\r\n":"<br />","\r":"<br />","\n":"<br />"};return!1!==t&&(n['"']="&#34;",n["'"]="&#39;",n["`"]="&#96;"),e.replace(/ {2}|\r\n|[&<>\r\n'"`]/g,function(e){return n[e]||e})}var y={html:'<!DOCTYPE html><html{attrs}><head><style>.ie * {min-height: auto !important} .ie table td {height:15px} @supports (-ms-ime-align:auto) { * { min-height: auto !important; } }</style><meta http-equiv="Content-Type" content="text/html;charset={charset}" /><link rel="stylesheet" type="text/css" href="{style}" /></head><body contenteditable="true" {spellcheck}><p></p></body></html>',toolbarButton:'<a class="sceditor-button sceditor-button-{name}" data-sceditor-command="{name}" unselectable="on"><div unselectable="on">{dispName}</div></a>',emoticon:'<img src="{url}" data-sceditor-emoticon="{key}" alt="{key}" title="{tooltip}" />',fontOpt:'<a class="sceditor-font-option" href="#" data-font="{font}"><font face="{font}">{font}</font></a>',sizeOpt:'<a class="sceditor-fontsize-option" data-size="{size}" href="#"><font size="{size}">{size}</font></a>',pastetext:'<div><label for="txt">{label}</label> <textarea cols="20" rows="7" id="txt"></textarea></div><div><input type="button" class="button" value="{insert}" /></div>',table:'<div><label for="rows">{rows}</label><input type="text" id="rows" value="2" /></div><div><label for="cols">{cols}</label><input type="text" id="cols" value="2" /></div><div><input type="button" class="button" value="{insert}" /></div>',image:'<div><label for="link">{url}</label> <input type="text" id="image" dir="ltr" placeholder="https://" /></div><div><label for="width">{width}</label> <input type="text" id="width" size="2" dir="ltr" /></div><div><label for="height">{height}</label> <input type="text" id="height" size="2" dir="ltr" /></div><div><input type="button" class="button" value="{insert}" /></div>',email:'<div><label for="email">{label}</label> <input type="text" id="email" dir="ltr" /></div><div><label for="des">{desc}</label> <input type="text" id="des" /></div><div><input type="button" class="button" value="{insert}" /></div>',link:'<div><label for="link">{url}</label> <input type="text" id="link" dir="ltr" placeholder="https://" /></div><div><label for="des">{desc}</label> <input type="text" id="des" /></div><div><input type="button" class="button" value="{ins}" /></div>',youtubeMenu:'<div><label for="link">{label}</label> <input type="text" id="link" dir="ltr" placeholder="https://" /></div><div><input type="button" class="button" value="{insert}" /></div>',youtube:'<iframe width="560" height="315" frameborder="0" allowfullscreen src="https://www.youtube.com/embed/{id}?wmode=opaque&start={time}" data-youtube-id="{id}"></iframe>'};function gt(e,t,n){var o=y[e];return Object.keys(t).forEach(function(e){o=o.replace(new RegExp(ft("{"+e+"}"),"g"),t[e])}),n&&(o=et(o)),o}var x=v&&v<11;function w(e){if("mozHidden"in document)for(var t,n=e.getBody();n;){if((t=n).firstChild)t=t.firstChild;else{for(;t&&!t.nextSibling;)t=t.parentNode;t&&(t=t.nextSibling)}3===n.nodeType&&/[\n\r\t]+/.test(n.nodeValue)&&(/^pre/.test(Pe(n.parentNode,"whiteSpace"))||Re(n)),n=t}}var mt={bold:{exec:"bold",tooltip:"Bold",shortcut:"Ctrl+B"},italic:{exec:"italic",tooltip:"Italic",shortcut:"Ctrl+I"},underline:{exec:"underline",tooltip:"Underline",shortcut:"Ctrl+U"},strike:{exec:"strikethrough",tooltip:"Strikethrough"},subscript:{exec:"subscript",tooltip:"Subscript"},superscript:{exec:"superscript",tooltip:"Superscript"},left:{state:function(e){if(e&&3===e.nodeType&&(e=e.parentNode),e){var t="ltr"===Pe(e,"direction"),n=Pe(e,"textAlign");return"left"===n||n===(t?"start":"end")}},exec:"justifyleft",tooltip:"Align left"},center:{exec:"justifycenter",tooltip:"Center"},right:{state:function(e){if(e&&3===e.nodeType&&(e=e.parentNode),e){var t="ltr"===Pe(e,"direction"),n=Pe(e,"textAlign");return"right"===n||n===(t?"end":"start")}},exec:"justifyright",tooltip:"Align right"},justify:{exec:"justifyfull",tooltip:"Justify"},font:{_dropDown:function(t,e,n){var o=De("div");ze(o,"click","a",function(e){n(Ve(this,"font")),t.closeDropDown(!0),e.preventDefault()}),t.opts.fonts.split(",").forEach(function(e){He(o,gt("fontOpt",{font:e},!0))}),t.createDropDown(e,"font-picker",o)},exec:function(e){var t=this;mt.font._dropDown(t,e,function(e){t.execCommand("fontname",e)})},tooltip:"Font Name"},size:{_dropDown:function(t,e,n){var o=De("div");ze(o,"click","a",function(e){n(Ve(this,"size")),t.closeDropDown(!0),e.preventDefault()});for(var i=1;i<=7;i++)He(o,gt("sizeOpt",{size:i},!0));t.createDropDown(e,"fontsize-picker",o)},exec:function(e){var t=this;mt.size._dropDown(t,e,function(e){t.execCommand("fontsize",e)})},tooltip:"Font Size"},color:{_dropDown:function(t,e,n){var o=De("div"),i="",r=mt.color;r._htmlCache||(t.opts.colors.split("|").forEach(function(e){i+='<div class="sceditor-color-column">',e.split(",").forEach(function(e){i+='<a href="#" class="sceditor-color-option" style="background-color: '+e+'" data-color="'+e+'"></a>'}),i+="</div>"}),r._htmlCache=i),He(o,et(r._htmlCache)),ze(o,"click","a",function(e){n(Ve(this,"color")),t.closeDropDown(!0),e.preventDefault()}),t.createDropDown(e,"color-picker",o)},exec:function(e){var t=this;mt.color._dropDown(t,e,function(e){t.execCommand("forecolor",e)})},tooltip:"Font Color"},removeformat:{exec:"removeformat",tooltip:"Remove Formatting"},cut:{exec:"cut",tooltip:"Cut",errorMessage:"Your browser does not allow the cut command. Please use the keyboard shortcut Ctrl/Cmd-X"},copy:{exec:"copy",tooltip:"Copy",errorMessage:"Your browser does not allow the copy command. Please use the keyboard shortcut Ctrl/Cmd-C"},paste:{exec:"paste",tooltip:"Paste",errorMessage:"Your browser does not allow the paste command. Please use the keyboard shortcut Ctrl/Cmd-V"},pastetext:{exec:function(e){var t,n=De("div"),o=this;He(n,gt("pastetext",{label:o._("Paste your text inside the following box:"),insert:o._("Insert")},!0)),ze(n,"click",".button",function(e){(t=Fe(n,"#txt")[0].value)&&o.wysiwygEditorInsertText(t),o.closeDropDown(!0),e.preventDefault()}),o.createDropDown(e,"pastetext",n)},tooltip:"Paste Text"},bulletlist:{exec:function(){w(this),this.execCommand("insertunorderedlist")},tooltip:"Bullet list"},orderedlist:{exec:function(){w(this),this.execCommand("insertorderedlist")},tooltip:"Numbered list"},indent:{state:function(e,t){var n,o,i;return je(t,"li")?0:je(t,"ul,ol,menu")&&(o=(n=this.getRangeHelper().selectedRange()).startContainer.parentNode,i=n.endContainer.parentNode,o!==o.parentNode.firstElementChild||je(i,"li")&&i!==i.parentNode.lastElementChild)?0:-1},exec:function(){var e=this.getRangeHelper().getFirstBlockParent();this.focus(),Me(e,"ul,ol,menu")&&this.execCommand("indent")},tooltip:"Add indent"},outdent:{state:function(e,t){return Me(t,"ul,ol,menu")?0:-1},exec:function(){Me(this.getRangeHelper().getFirstBlockParent(),"ul,ol,menu")&&this.execCommand("outdent")},tooltip:"Remove one indent"},table:{exec:function(e){var i=this,r=De("div");He(r,gt("table",{rows:i._("Rows:"),cols:i._("Cols:"),insert:i._("Insert")},!0)),ze(r,"click",".button",function(e){var t=Number(Fe(r,"#rows")[0].value),n=Number(Fe(r,"#cols")[0].value),o="<table>";0<t&&0<n&&(o+=Array(t+1).join("<tr>"+Array(n+1).join("<td>"+(x?"":"<br />")+"</td>")+"</tr>"),o+="</table>",i.wysiwygEditorInsertHtml(o),i.closeDropDown(!0),e.preventDefault())}),i.createDropDown(e,"inserttable",r)},tooltip:"Insert a table"},horizontalrule:{exec:"inserthorizontalrule",tooltip:"Insert a horizontal rule"},code:{exec:function(){this.wysiwygEditorInsertHtml("<code>",(x?"":"<br />")+"</code>")},tooltip:"Code"},image:{_dropDown:function(t,e,n,o){var i=De("div");He(i,gt("image",{url:t._("URL:"),width:t._("Width (optional):"),height:t._("Height (optional):"),insert:t._("Insert")},!0));var r=Fe(i,"#image")[0];r.value=n,ze(i,"click",".button",function(e){r.value&&o(r.value,Fe(i,"#width")[0].value,Fe(i,"#height")[0].value),t.closeDropDown(!0),e.preventDefault()}),t.createDropDown(e,"insertimage",i)},exec:function(e){var i=this;mt.image._dropDown(i,e,"",function(e,t,n){var o="";t&&(o+=' width="'+t+'"'),n&&(o+=' height="'+n+'"'),i.wysiwygEditorInsertHtml("<img"+o+' src="'+e+'" />')})},tooltip:"Insert an image"},email:{_dropDown:function(n,e,o){var i=De("div");He(i,gt("email",{label:n._("E-mail:"),desc:n._("Description (optional):"),insert:n._("Insert")},!0)),ze(i,"click",".button",function(e){var t=Fe(i,"#email")[0].value;t&&o(t,Fe(i,"#des")[0].value),n.closeDropDown(!0),e.preventDefault()}),n.createDropDown(e,"insertemail",i)},exec:function(e){var n=this;mt.email._dropDown(n,e,function(e,t){n.focus(),!n.getRangeHelper().selectedHtml()||t?n.wysiwygEditorInsertHtml('<a href="mailto:'+e+'">'+(t||e)+"</a>"):n.execCommand("createlink","mailto:"+e)})},tooltip:"Insert an email"},link:{_dropDown:function(t,e,n){var o=De("div");He(o,gt("link",{url:t._("URL:"),desc:t._("Description (optional):"),ins:t._("Insert")},!0));var i=Fe(o,"#link")[0];function r(e){i.value&&n(i.value,Fe(o,"#des")[0].value),t.closeDropDown(!0),e.preventDefault()}ze(o,"click",".button",r),ze(o,"keypress",function(e){13===e.which&&i.value&&r(e)},_e),t.createDropDown(e,"insertlink",o)},exec:function(e){var n=this;mt.link._dropDown(n,e,function(e,t){n.focus(),t||!n.getRangeHelper().selectedHtml()?(t=t||e,n.wysiwygEditorInsertHtml('<a href="'+e+'">'+t+"</a>")):n.execCommand("createlink",e)})},tooltip:"Insert a link"},unlink:{state:function(){return Me(this.currentNode(),"a")?0:-1},exec:function(){var e=Me(this.currentNode(),"a");if(e){for(;e.firstChild;)We(e.firstChild,e);Re(e)}},tooltip:"Unlink"},quote:{exec:function(e,t,n){var o="<blockquote>",i="</blockquote>";t?(o=o+(n=n?"<cite>"+n+"</cite>":"")+t+i,i=null):""===this.getRangeHelper().selectedHtml()&&(i=(x?"":"<br />")+i),this.wysiwygEditorInsertHtml(o,i)},tooltip:"Insert a Quote"},emoticon:{exec:function(f){var p=this,g=function(e){var t,n,o=p.opts,i=o.emoticonsRoot||"",r=o.emoticonsCompat,a=p.getRangeHelper(),l=r&&" "!==a.getOuterText(!0,1)?" ":"",c=r&&" "!==a.getOuterText(!1,1)?" ":"",s=De("div"),u=De("div"),d=Ce({},o.emoticons.dropdown,e?o.emoticons.more:{});return He(s,u),n=Math.sqrt(Object.keys(d).length),ze(s,"click","img",function(e){p.insert(l+Oe(this,"alt")+c,null,!1).closeDropDown(!0),e.preventDefault()}),Se(d,function(e,t){He(u,De("img",{src:i+(t.url||t),alt:e,title:t.tooltip||e})),u.children.length>=n&&(u=De("div"),He(s,u))}),!e&&o.emoticons.more&&(He(t=De("a",{className:"sceditor-more"}),document.createTextNode(p._("More"))),ze(t,"click",function(e){p.createDropDown(f,"more-emoticons",g(!0)),e.preventDefault()}),He(s,t)),s};p.createDropDown(f,"emoticons",g(!1))},txtExec:function(e){mt.emoticon.exec.call(this,e)},tooltip:"Insert an emoticon"},youtube:{_dropDown:function(r,e,a){var l=De("div");He(l,gt("youtubeMenu",{label:r._("Video URL:"),insert:r._("Insert")},!0)),ze(l,"click",".button",function(e){var t=Fe(l,"#link")[0].value,n=t.match(/(?:v=|v\/|embed\/|youtu.be\/)(.{11})/),o=t.match(/[&|?](?:star)?t=((\d+[hms]?){1,3})/),i=0;o&&Se(o[1].split(/[hms]/),function(e,t){""!==t&&(i=60*i+Number(t))}),n&&/^[a-zA-Z0-9_\-]{11}$/.test(n[1])&&a(n[1],i),r.closeDropDown(!0),e.preventDefault()}),r.createDropDown(e,"insertlink",l)},exec:function(e){var n=this;mt.youtube._dropDown(n,e,function(e,t){n.wysiwygEditorInsertHtml(gt("youtube",{id:e,time:t}))})},tooltip:"Insert a YouTube video"},date:{_date:function(e){var t=new Date,n=t.getYear(),o=t.getMonth()+1,i=t.getDate();return n<2e3&&(n=1900+n),o<10&&(o="0"+o),i<10&&(i="0"+i),e.opts.dateFormat.replace(/year/i,n).replace(/month/i,o).replace(/day/i,i)},exec:function(){this.insertText(mt.date._date(this))},txtExec:function(){this.insertText(mt.date._date(this))},tooltip:"Insert current date"},time:{_time:function(){var e=new Date,t=e.getHours(),n=e.getMinutes(),o=e.getSeconds();return t<10&&(t="0"+t),n<10&&(n="0"+n),o<10&&(o="0"+o),t+":"+n+":"+o},exec:function(){this.insertText(mt.time._time())},txtExec:function(){this.insertText(mt.time._time())},tooltip:"Insert current time"},ltr:{state:function(e,t){return t&&"ltr"===t.style.direction},exec:function(){var e=this.getRangeHelper(),t=e.getFirstBlockParent();this.focus(),(t&&!je(t,"body")||(this.execCommand("formatBlock","p"),(t=e.getFirstBlockParent())&&!je(t,"body")))&&Pe(t,"direction","ltr"===Pe(t,"direction")?"":"ltr")},tooltip:"Left-to-Right"},rtl:{state:function(e,t){return t&&"rtl"===t.style.direction},exec:function(){var e=this.getRangeHelper(),t=e.getFirstBlockParent();this.focus(),(t&&!je(t,"body")||(this.execCommand("formatBlock","p"),(t=e.getFirstBlockParent())&&!je(t,"body")))&&Pe(t,"direction","rtl"===Pe(t,"direction")?"":"rtl")},tooltip:"Right-to-Left"},print:{exec:"print",tooltip:"Print"},maximize:{state:function(){return this.maximize()},exec:function(){this.maximize(!this.maximize())},txtExec:function(){this.maximize(!this.maximize())},tooltip:"Maximize",shortcut:"Ctrl+Shift+M"},source:{state:function(){return this.sourceMode()},exec:function(){this.toggleSourceMode()},txtExec:function(){this.toggleSourceMode()},tooltip:"View source",shortcut:"Ctrl+Shift+S"},ignore:{}},C={};function ht(r){var i=this,a=[],l=function(e){return"signal"+e.charAt(0).toUpperCase()+e.slice(1)},e=function(e,t){e=[].slice.call(e);var n,o,i=l(e.shift());for(n=0;n<a.length;n++)if(i in a[n]&&(o=a[n][i].apply(r,e),t))return o};i.call=function(){e(arguments,!1)},i.callOnlyFirst=function(){return e(arguments,!0)},i.hasHandler=function(e){var t=a.length;for(e=l(e);t--;)if(e in a[t])return!0;return!1},i.exists=function(e){return e in C&&"function"==typeof(e=C[e])&&"object"==typeof e.prototype},i.isRegistered=function(e){if(i.exists(e))for(var t=a.length;t--;)if(a[t]instanceof C[e])return!0;return!1},i.register=function(e){return!(!i.exists(e)||i.isRegistered(e)||(e=new C[e],a.push(e),"init"in e&&e.init.call(r),0))},i.deregister=function(e){var t,n=a.length,o=!1;if(!i.isRegistered(e))return o;for(;n--;)a[n]instanceof C[e]&&(o=!0,"destroy"in(t=a.splice(n,1)[0])&&t.destroy.call(r));return o},i.destroy=function(){for(var e=a.length;e--;)"destroy"in a[e]&&a[e].destroy.call(r);a=[],r=null}}ht.plugins=C;var E=v&&v<11,S=function(e,t,n){var o,i,r,a,l,c="",s=e.startContainer,u=e.startOffset;for(s&&3!==s.nodeType&&(s=s.childNodes[u],u=0),r=a=u;n>c.length&&s&&3===s.nodeType;)o=s.nodeValue,i=n-c.length,l&&(a=o.length,r=0),l=s,t?(u=r=Math.max(a-i,0),c=o.substr(r,a-r)+c,s=l.previousSibling):(u=r+(a=Math.min(i,o.length)),c+=o.substr(r,a),s=l.nextSibling);return{node:l||s,offset:u,text:c}};function vt(r,e){var a,l,c=e||r.contentDocument||r.document,s="sceditor-start-marker",u="sceditor-end-marker",b=this;b.insertHTML=function(e,t){var n,o;if(!b.selectedRange())return!1;for(t&&(e+=b.selectedHtml()+t),o=De("p",{},c),n=c.createDocumentFragment(),o.innerHTML=e;o.firstChild;)He(n,o.firstChild);b.insertNode(n)},l=function(e,t,n){var o,i=c.createDocumentFragment();if("string"==typeof e?(t&&(e+=b.selectedHtml()+t),i=et(e)):(He(i,e),t&&(He(i,b.selectedRange().extractContents()),He(i,t))),o=i.lastChild){for(;!it(o.lastChild,!0);)o=o.lastChild;if(ot(o)?o.lastChild||He(o,document.createTextNode("​")):o=i,b.removeMarkers(),He(o,a(s)),He(o,a(u)),n){var r=De("div");return He(r,i),r.innerHTML}return i}},b.insertNode=function(e,t){var n=l(e,t),o=b.selectedRange(),i=o.commonAncestorContainer;if(!n)return!1;o.deleteContents(),i&&3!==i.nodeType&&!ot(i)?We(n,i):o.insertNode(n),b.restoreRange()},b.cloneSelected=function(){var e=b.selectedRange();if(e)return e.cloneRange()},b.selectedRange=function(){var e,t,n=r.getSelection();if(n){if(n.rangeCount<=0){for(t=c.body;t.firstChild;)t=t.firstChild;(e=c.createRange()).setStartBefore(t),n.addRange(e)}return 0<n.rangeCount&&(e=n.getRangeAt(0)),e}},b.hasSelection=function(){var e=r.getSelection();return e&&0<e.rangeCount},b.selectedHtml=function(){var e,t=b.selectedRange();return t?(He(e=De("p",{},c),t.cloneContents()),e.innerHTML):""},b.parentNode=function(){var e=b.selectedRange();if(e)return e.commonAncestorContainer},b.getFirstBlockParent=function(e){var t=function(e){return it(e,!0)&&(e=e?e.parentNode:null)?t(e):e};return t(e||b.parentNode())},b.insertNodeAt=function(e,t){var n=b.selectedRange(),o=b.cloneSelected();if(!o)return!1;o.collapse(e),o.insertNode(t),b.selectRange(n)},a=function(e){b.removeMarker(e);var t=De("span",{id:e,className:"sceditor-selection sceditor-ignore",style:"display:none;line-height:0"},c);return t.innerHTML=" ",t},b.insertMarkers=function(){var e=b.selectedRange(),t=a(s);b.removeMarkers(),b.insertNodeAt(!0,t),e&&e.collapsed?t.parentNode.insertBefore(a(u),t.nextSibling):b.insertNodeAt(!1,a(u))},b.getMarker=function(e){return c.getElementById(e)},b.removeMarker=function(e){var t=b.getMarker(e);t&&Re(t)},b.removeMarkers=function(){b.removeMarker(s),b.removeMarker(u)},b.saveRange=function(){b.insertMarkers()},b.selectRange=function(e){var t,n=r.getSelection(),o=e.endContainer;if(!E&&e.collapsed&&o&&!it(o,!0)){for(t=o.lastChild;t&&je(t,".sceditor-ignore");)t=t.previousSibling;if(je(t,"br")){var i=c.createRange();i.setEndAfter(t),i.collapse(!1),b.compare(e,i)&&(e.setStartBefore(t),e.collapse(!0))}}n&&(b.clear(),n.addRange(e))},b.restoreRange=function(){var e,t=b.selectedRange(),n=b.getMarker(s),o=b.getMarker(u);if(!n||!o||!t)return!1;e=n.nextSibling===o,(t=c.createRange()).setStartBefore(n),t.setEndAfter(o),e&&t.collapse(!0),b.selectRange(t),b.removeMarkers()},b.selectOuterText=function(e,t){var n,o,i=b.cloneSelected();if(!i)return!1;i.collapse(!1),n=S(i,!0,e),o=S(i,!1,t),i.setStart(n.node,n.offset),i.setEnd(o.node,o.offset),b.selectRange(i)},b.getOuterText=function(e,t){var n=b.cloneSelected();return n?(n.collapse(!e),S(n,e,t).text):""},b.replaceKeyword=function(e,t,n,o,i,r){n||e.sort(function(e,t){return e[0].length-t[0].length});var a,l,c,s,u,d,f,p,g="(^|[\\s    ])",m=e.length,h=i?1:0,v=o||e[m-1][0].length;for(i&&v++,r=r||"",u=(a=b.getOuterText(!0,v)).length,a+=r,t&&(a+=b.getOuterText(!1,v));m--;)if(p=(f=e[m][0]).length,s=Math.max(0,u-p-h),c=-1,i?(l=a.substr(s).match(new RegExp(g+ft(f)+g)))&&(c=l.index+s+l[1].length):c=a.indexOf(f,s),-1<c&&c<=u&&u<=c+p+h)return d=u-c,b.selectOuterText(d,p-d-(/^\S/.test(r)?1:0)),b.insertHTML(e[m][1]),!0;return!1},b.compare=function(e,t){return t||(t=b.selectedRange()),e&&t?0===e.compareBoundaryPoints(Range.END_TO_END,t)&&0===e.compareBoundaryPoints(Range.START_TO_START,t):!e&&!t},b.clear=function(){var e=r.getSelection();e&&(e.removeAllRanges?e.removeAllRanges():e.empty&&e.empty())}}var bt=window,yt=document,xt=v,wt=xt&&xt<11,Ct=/^image\/(p?jpe?g|gif|png|bmp)$/i;function Et(l,e){var a,w,u,c,r,g,d,s,f,p,m,h,t,v,i,b,y,x,C,n,o,E,S,k,T,D,N,M,R,H,F,_,z,A,O,B,L,I,P,V,j,W,q,U,$,Y,K,X,Q,G,J,Z,ee,te,ne,oe,ie,re,ae,le,ce,se,ue,de=this,fe={},pe=[],ge=[],me={},he={},ve={};de.commands=Ce(!0,{},e.commands||mt);var be=de.opts=Ce(!0,{},ct,e);de.opts.emoticons=e.emoticons||ct.emoticons,N=function(){l._sceditor=de,be.locale&&"en"!==be.locale&&z(),We(w=De("div",{className:"sceditor-container"}),l),Pe(w,"z-index",be.zIndex),xt&&Ue(w,"ie ie"+xt),n=l.required,l.required=!1;var e=Et.formats[be.format];"init"in(a=e?new e:{})&&a.init.call(de),_(),I(),A(),F(),O(),B(),dt||de.toggleSourceMode(),G();var t=function(){Ae(bt,"load",t),be.autofocus&&ne(),ue(),Z(),i.call("ready"),"onReady"in a&&a.onReady.call(de)};ze(bt,"load",t),"complete"===yt.readyState&&t()},_=function(){var e=be.plugins;e=e?e.toString().split(","):[],i=new ht(de),e.forEach(function(e){i.register(e.trim())})},z=function(){var e;(t=Et.locale[be.locale])||(e=be.locale.split("-"),t=Et.locale[e[0]]),t&&t.dateFormat&&(be.dateFormat=t.dateFormat)},F=function(){s=De("textarea"),c=De("iframe",{frameborder:0,allowfullscreen:!0}),be.startInSourceMode?(Ue(w,"sourceMode"),Be(c)):(Ue(w,"wysiwygMode"),Be(s)),be.spellcheck||Oe(w,"spellcheck","false"),"https:"===bt.location.protocol&&Oe(c,"src","javascript:false"),He(w,c),He(w,s),de.dimensions(be.width||Ke(l),be.height||Xe(l));var e=xt?"ie ie"+xt:"";e+=ut?" ios":"",(d=c.contentDocument).open(),d.write(gt("html",{attrs:' class="'+e+'"',spellcheck:be.spellcheck?"":'spellcheck="false"',charset:be.charset,style:be.style})),d.close(),g=d.body,r=c.contentWindow,de.readOnly(!!be.readOnly),(ut||st||xt)&&(Xe(g,"100%"),xt||ze(g,"touchend",de.focus));var t=Oe(l,"tabindex");Oe(s,"tabindex",t),Oe(c,"tabindex",t),v=new vt(r),Be(l),de.val(l.value);var n=be.placeholder||Oe(l,"placeholder");n&&(s.placeholder=n,Oe(g,"placeholder",n))},O=function(){be.autoUpdate&&(ze(g,"blur",se),ze(s,"blur",se)),null===be.rtl&&(be.rtl="rtl"===Pe(s,"direction")),de.rtl(!!be.rtl),be.autoExpand&&(ze(g,"load",ue,_e),ze(g,"input keyup",ue)),be.resizeEnabled&&L(),Oe(w,"id",be.id),de.emoticons(be.emoticonsEnabled)},B=function(){var e=l.form,t="compositionstart compositionend",n="keydown keyup keypress focus blur contextmenu",o="onselectionchange"in d?"selectionchange":"keyup focus blur contextmenu mouseup touchend click";ze(yt,"click",X),e&&(ze(e,"reset",U),ze(e,"submit",de.updateOriginal,_e)),ze(g,"keypress",q),ze(g,"keydown",j),ze(g,"keydown",W),ze(g,"keyup",Z),ze(g,"blur",le),ze(g,"keyup",ce),ze(g,"paste",P),ze(g,t,Y),ze(g,o,ee),ze(g,n,K),be.emoticonsCompat&&bt.getSelection&&ze(g,"keyup",ie),ze(g,"blur",function(){de.val()||Ue(g,"placeholder")}),ze(g,"focus",function(){$e(g,"placeholder")}),ze(s,"blur",le),ze(s,"keyup",ce),ze(s,"keydown",j),ze(s,t,Y),ze(s,n,K),ze(d,"mousedown",$),ze(d,o,ee),ze(d,"beforedeactivate keyup mouseup",H),ze(d,"keyup",Z),ze(d,"focus",function(){p=null}),ze(w,"selectionchanged",te),ze(w,"selectionchanged",G),ze(w,"selectionchanged valuechanged nodechanged pasteraw paste",K)},A=function(){var r,a=de.commands,l=(be.toolbarExclude||"").split(","),e=be.toolbar.split("|");u=De("div",{className:"sceditor-toolbar",unselectable:"on"}),be.icons in Et.icons&&(D=new Et.icons[be.icons]),Se(e,function(e,t){r=De("div",{className:"sceditor-group"}),Se(t.split(","),function(e,t){var n,o,i=a[t];!i||-1<l.indexOf(t)||(o=i.shortcut,n=gt("toolbarButton",{name:t,dispName:de._(i.name||i.tooltip||t)},!0).firstChild,D&&D.create&&D.create(t)&&(We(D.create(t),n.firstChild),Ue(n,"has-icon")),n._sceTxtMode=!!i.txtExec,n._sceWysiwygMode=!!i.exec,Ye(n,"disabled",!i.exec),ze(n,"click",function(e){qe(n,"disabled")||R(n,i),G(),e.preventDefault()}),ze(n,"mousedown",function(e){de.closeDropDown(),e.preventDefault()}),i.tooltip&&Oe(n,"title",de._(i.tooltip)+(o?" ("+o+")":"")),o&&de.addShortcut(o,t),i.state?ge.push({name:t,state:i.state}):ye(i.exec)&&ge.push({name:t,state:i.exec}),He(r,n),he[t]=n)}),r.firstChild&&He(u,r)}),He(be.toolbarContainer||w,u)},L=function(){var o,i,r,a,t,n,e=De("div",{className:"sceditor-grip"}),l=De("div",{className:"sceditor-resize-cover"}),c="touchmove mousemove",s="touchcancel touchend mouseup",u=0,d=0,f=0,p=0,g=0,m=0,h=Ke(w),v=Xe(w),b=!1,y=de.rtl();if(o=be.resizeMinHeight||v/1.5,i=be.resizeMaxHeight||2.5*v,r=be.resizeMinWidth||h/1.25,a=be.resizeMaxWidth||1.25*h,t=function(e){"touchmove"===e.type?(e=bt.event,f=e.changedTouches[0].pageX,p=e.changedTouches[0].pageY):(f=e.pageX,p=e.pageY);var t=m+(p-d),n=y?g-(f-u):g+(f-u);0<a&&a<n&&(n=a),0<r&&n<r&&(n=r),be.resizeWidth||(n=!1),0<i&&i<t&&(t=i),0<o&&t<o&&(t=o),be.resizeHeight||(t=!1),(n||t)&&de.dimensions(n,t),e.preventDefault()},n=function(e){b&&(b=!1,Be(l),$e(w,"resizing"),Ae(yt,c,t),Ae(yt,s,n),e.preventDefault())},D&&D.create){var x=D.create("grip");x&&(He(e,x),Ue(e,"has-icon"))}He(w,e),He(w,l),Be(l),ze(e,"touchstart mousedown",function(e){"touchstart"===e.type?(e=bt.event,u=e.touches[0].pageX,d=e.touches[0].pageY):(u=e.pageX,d=e.pageY),g=Ke(w),m=Xe(w),b=!0,Ue(w,"resizing"),Le(l),ze(yt,c,t),ze(yt,s,n),e.preventDefault()})},I=function(){var e=be.emoticons,n=be.emoticonsRoot||"";e&&(ve=Ce({},e.more,e.dropdown,e.hidden)),Se(ve,function(e,t){ve[e]=gt("emoticon",{key:e,url:n+(t.url||t),tooltip:t.tooltip||e}),be.emoticonsEnabled&&pe.push(De("img",{src:n+(t.url||t)}))})},ne=function(){var e,t,n=g.firstChild,o=!!be.autofocusEnd;if(Ge(w)){if(de.sourceMode())return t=o?s.value.length:0,void s.setSelectionRange(t,t);if(at(g),o)for((n=g.lastChild)||(n=De("p",{},d),He(g,n));n.lastChild;)n=n.lastChild,!wt&&je(n,"br")&&n.previousSibling&&(n=n.previousSibling);e=d.createRange(),ot(n)?e.selectNodeContents(n):(e.setStartBefore(n),o&&e.setStartAfter(n)),e.collapse(!o),v.selectRange(e),x=e,o&&(g.scrollTop=g.scrollHeight),de.focus()}},de.readOnly=function(e){return"boolean"!=typeof e?!s.readonly:(g.contentEditable=!e,s.readonly=!e,Q(e),de)},de.rtl=function(e){var t=e?"rtl":"ltr";return"boolean"!=typeof e?"rtl"===Oe(s,"dir"):(Oe(g,"dir",t),Oe(s,"dir",t),$e(w,"rtl"),$e(w,"ltr"),Ue(w,t),D&&D.rtl&&D.rtl(e),de)},Q=function(n){var o=de.inSourceMode()?"_sceTxtMode":"_sceWysiwygMode";Se(he,function(e,t){Ye(t,"disabled",n||!t[o])})},de.width=function(e,t){return e||0===e?(de.dimensions(e,null,t),de):Ke(w)},de.dimensions=function(e,t,n){return t=!(!t&&0!==t)&&t,!1===(e=!(!e&&0!==e)&&e)&&!1===t?{width:de.width(),height:de.height()}:(!1!==e&&(!1!==n&&(be.width=e),Ke(w,e)),!1!==t&&(!1!==n&&(be.height=t),Xe(w,t)),de)},de.height=function(e,t){return e||0===e?(de.dimensions(null,e,t),de):Xe(w)},de.maximize=function(e){var t="sceditor-maximize";return xe(e)?qe(w,t):((e=!!e)&&(k=bt.pageYOffset),Ye(yt.documentElement,t,e),Ye(yt.body,t,e),Ye(w,t,e),de.width(e?"100%":be.width,!1),de.height(e?"100%":be.height,!1),e||bt.scrollTo(0,k),ue(),de)},ue=function(){be.autoExpand&&!S&&(S=setTimeout(de.expandToContent,200))},de.expandToContent=function(e){if(!de.maximize()){if(clearTimeout(S),S=!1,!E){var t=be.resizeMinHeight||be.height||Xe(l);E={min:t,max:be.resizeMaxHeight||2*t}}var n=yt.createRange();n.selectNodeContents(g);var o=n.getBoundingClientRect(),i=d.documentElement.clientHeight-1,r=o.bottom-o.top,a=de.height()+1+(r-i);e||-1===E.max||(a=Math.min(a,E.max)),de.height(Math.ceil(Math.max(a,E.min)))}},de.destroy=function(){if(i){i.destroy(),i=p=v=null,f&&Re(f),Ae(yt,"click",X);var e=l.form;e&&(Ae(e,"reset",U),Ae(e,"submit",de.updateOriginal)),Re(s),Re(u),Re(w),delete l._sceditor,Le(l),l.required=n}},de.createDropDown=function(e,t,n,o){var i,r="sceditor-"+t;de.closeDropDown(!0),f&&qe(f,r)||(!1!==o&&Se(Fe(n,":not(input):not(textarea)"),function(e,t){t.nodeType===ke&&Oe(t,"unselectable","on")}),i=Ce({top:e.offsetTop,left:e.offsetLeft,marginTop:e.clientHeight},be.dropDownCss),Pe(f=De("div",{className:"sceditor-dropdown "+r}),i),He(f,n),He(w,f),ze(f,"click focusin",function(e){e.stopPropagation()}),setTimeout(function(){if(f){var e=Fe(f,"input,textarea")[0];e&&e.focus()}}))},X=function(e){3!==e.which&&f&&!e.defaultPrevented&&(se(),de.closeDropDown())},P=function(e){var t,n,o=xt||st,i=g,r=e.clipboardData;if(r&&!o){var a={},l=r.types,c=r.items;e.preventDefault();for(var s=0;s<l.length;s++){if(bt.FileReader&&c&&Ct.test(c[s].type))return t=r.items[s].getAsFile(),n=void 0,(n=new FileReader).onload=function(e){V({html:'<img src="'+e.target.result+'" />'})},void n.readAsDataURL(t);a[l[s]]=r.getData(l[s])}a.text=a["text/plain"],a.html=a["text/html"],V(a)}else if(!T){var u=i.scrollTop;for(v.saveRange(),T=yt.createDocumentFragment();i.firstChild;)He(T,i.firstChild);setTimeout(function(){var e=i.innerHTML;i.innerHTML="",He(i,T),i.scrollTop=u,T=!1,v.restoreRange(),V({html:e})},0)}},V=function(e){var t=De("div",{},d);i.call("pasteRaw",e),Qe(w,"pasteraw",e),e.html?(t.innerHTML=e.html,rt(t)):t.innerHTML=pt(e.text||"");var n={val:t.innerHTML};"fragmentToSource"in a&&(n.val=a.fragmentToSource(n.val,d,b)),i.call("paste",n),Qe(w,"paste",n),"fragmentToHtml"in a&&(n.val=a.fragmentToHtml(n.val,b)),i.call("pasteHtml",n),de.wysiwygEditorInsertHtml(n.val,null,!0)},de.closeDropDown=function(e){f&&(Re(f),f=null),!0===e&&de.focus()},de.wysiwygEditorInsertHtml=function(e,t,n){var o,i,r,a=Xe(c);de.focus(),!n&&Me(y,"code")||(v.insertHTML(e,t),v.saveRange(),M(),Le(o=Fe(g,"#sceditor-end-marker")[0]),i=g.scrollTop,r=lt(o).top+1.5*o.offsetHeight-a,Be(o),(i<r||r+a<i)&&(g.scrollTop=r),ae(!1),v.restoreRange(),Z())},de.wysiwygEditorInsertText=function(e,t){de.wysiwygEditorInsertHtml(pt(e),pt(t))},de.insertText=function(e,t){return de.inSourceMode()?de.sourceEditorInsertText(e,t):de.wysiwygEditorInsertText(e,t),de},de.sourceEditorInsertText=function(e,t){var n,o,i=s.selectionStart,r=s.selectionEnd;n=s.scrollTop,s.focus(),o=s.value,t&&(e+=o.substring(i,r)+t),s.value=o.substring(0,i)+e+o.substring(r,o.length),s.selectionStart=i+e.length-(t?t.length:0),s.selectionEnd=s.selectionStart,s.scrollTop=n,s.focus(),ae()},de.getRangeHelper=function(){return v},de.sourceEditorCaret=function(e){return s.focus(),e?(s.selectionStart=e.start,s.selectionEnd=e.end,this):{start:s.selectionStart,end:s.selectionEnd}},de.val=function(e,t){return ye(e)?(de.inSourceMode()?de.setSourceEditorValue(e):(!1!==t&&"toHtml"in a&&(e=a.toHtml(e)),de.setWysiwygEditorValue(e)),de):de.inSourceMode()?de.getSourceEditorValue(!1):de.getWysiwygEditorValue(t)},de.insert=function(e,t,n,o,i){if(de.inSourceMode())return de.sourceEditorInsertText(e,t),de;if(t){var r=v.selectedHtml();!1!==n&&"fragmentToSource"in a&&(r=a.fragmentToSource(r,d,b)),e+=r+t}return!1!==n&&"fragmentToHtml"in a&&(e=a.fragmentToHtml(e,b)),!1!==n&&!0===i&&(e=e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")),de.wysiwygEditorInsertHtml(e),de},de.getWysiwygEditorValue=function(e){for(var t,n=De("div",{},d),o=g.childNodes,i=0;i<o.length;i++)He(n,o[i].cloneNode(!0));return He(g,n),rt(n),Re(n),t=n.innerHTML,!1!==e&&a.hasOwnProperty("toSource")&&(t=a.toSource(t,d)),t},de.getBody=function(){return g},de.getContentAreaContainer=function(){return c},de.getSourceEditorValue=function(e){var t=s.value;return!1!==e&&"toHtml"in a&&(t=a.toHtml(t)),t},de.setWysiwygEditorValue=function(e){e||(e="<p>"+(xt?"":"<br />")+"</p>"),g.innerHTML=e,M(),Z(),ae(),ue()},de.setSourceEditorValue=function(e){s.value=e,ae()},de.updateOriginal=function(){l.value=de.val()},M=function(){var e,s,u,d,t,f,p;be.emoticonsEnabled&&(e=g,s=ve,u=be.emoticonsCompat,d=e.ownerDocument,t="(^|\\s| | | | |$)",f=[],p={},Ne(e,"code")||(Se(s,function(e){p[e]=new RegExp(t+ft(e)+t),f.push(e)}),f.sort(function(e,t){return t.length-e.length}),function e(t){for(t=t.firstChild;t;){if(t.nodeType!==ke||je(t,"code")||e(t),t.nodeType===Te)for(var n=0;n<f.length;n++){var o=t.nodeValue,i=f[n],r=u?o.search(p[i]):o.indexOf(i);if(-1<r){var a=o.indexOf(i,r),l=et(s[i],d),c=o.substr(a+i.length);l.appendChild(d.createTextNode(c)),t.nodeValue=o.substr(0,a),t.parentNode.insertBefore(l,t.nextSibling)}}t=t.nextSibling}}(e)))},de.inSourceMode=function(){return qe(w,"sourceMode")},de.sourceMode=function(e){var t=de.inSourceMode();return"boolean"!=typeof e?t:((t&&!e||!t&&e)&&de.toggleSourceMode(),de)},de.toggleSourceMode=function(){var e=de.inSourceMode();!dt&&e||(e||(v.saveRange(),v.clear()),de.blur(),e?de.setWysiwygEditorValue(de.getSourceEditorValue()):de.setSourceEditorValue(de.getWysiwygEditorValue()),p=null,Ie(s),Ie(c),Ye(w,"wysiwygMode",e),Ye(w,"sourceMode",!e),Q(),G())},J=function(){return s.focus(),s.value.substring(s.selectionStart,s.selectionEnd)},R=function(e,t){de.inSourceMode()?t.txtExec&&(Array.isArray(t.txtExec)?de.sourceEditorInsertText.apply(de,t.txtExec):t.txtExec.call(de,e,J())):t.exec&&(we(t.exec)?t.exec.call(de,e):de.execCommand(t.exec,t.hasOwnProperty("execParam")?t.execParam:null))},H=function(){xt&&(p=v.selectedRange())},de.execCommand=function(e,t){var n=!1,o=de.commands[e];if(de.focus(),!Me(v.parentNode(),"code")){try{n=d.execCommand(e,!1,t)}catch(e){}!n&&o&&o.errorMessage&&alert(de._(o.errorMessage)),G()}},ee=function(){function e(){if(r.getSelection()&&r.getSelection().rangeCount<=0)x=null;else if(v&&!v.compare(x)){if((x=v.cloneSelected())&&x.collapsed){var e=x.startContainer,t=x.startOffset;for(t&&e.nodeType!==Te&&(e=e.childNodes[t]);e&&e.parentNode!==g;)e=e.parentNode;e&&it(e,!0)&&(v.saveRange(),n=d,Je(g,function(e){it(e,!0)?(o||We(o=De("p",{},n),e),e.nodeType===Te&&""===e.nodeValue||He(o,e)):o=null},!1,!0),v.restoreRange())}Qe(w,"selectionchanged")}var n,o;C=!1}C||(C=!0,"onselectionchange"in d?e():setTimeout(e,100))},te=function(){var e,t=v.parentNode();b!==t&&(e=b,b=t,y=v.getFirstBlockParent(t),Qe(w,"nodechanged",{oldNode:e,newNode:b}))},de.currentNode=function(){return b},de.currentBlockNode=function(){return y},G=function(){var e,t,n="active",o=d,i=de.sourceMode();if(de.readOnly())Se(Fe(u,n),function(e,t){$e(t,n)});else{i||(t=v.parentNode(),e=v.getFirstBlockParent(t));for(var r=0;r<ge.length;r++){var a=0,l=he[ge[r].name],c=ge[r].state,s=i&&!l._sceTxtMode||!i&&!l._sceWysiwygMode;if(ye(c)){if(!i)try{-1<(a=o.queryCommandEnabled(c)?0:-1)&&(a=o.queryCommandState(c)?1:0)}catch(e){}}else s||(a=c.call(de,t,e));Ye(l,"disabled",s||a<0),Ye(l,n,0<a)}D&&D.update&&D.update(i,t,e)}},q=function(e){if(!e.defaultPrevented&&(de.closeDropDown(),13===e.which)&&!je(y,"li,ul,ol")&&tt(y)){p=null;var t=De("br",{},d);if(v.insertNode(t),!wt){var n=t.parentNode,o=n.lastChild;o&&o.nodeType===Te&&""===o.nodeValue&&(Re(o),o=n.lastChild),!it(n,!0)&&o===t&&it(t.previousSibling)&&v.insertHTML("<br>")}e.preventDefault()}},Z=function(){Ze(g,function(e){if(e.nodeType===ke&&!/inline/.test(Pe(e,"display"))&&!je(e,".sceditor-nlf")&&tt(e)){var t=De("p",{},d);return t.className="sceditor-nlf",t.innerHTML=wt?"":"<br />",He(g,t),!1}if(3===e.nodeType&&!/^\s*$/.test(e.nodeValue)||je(e,"br"))return!1})},U=function(){de.val(l.value)},$=function(){de.closeDropDown(),p=null},de._=function(){var n=arguments;return t&&t[n[0]]&&(n[0]=t[n[0]]),n[0].replace(/\{(\d+)\}/g,function(e,t){return void 0!==n[t-0+1]?n[t-0+1]:"{"+t+"}"})},K=function(t){i&&i.call(t.type+"Event",t,de);var e=(t.target===s?"scesrc":"scewys")+t.type;fe[e]&&fe[e].forEach(function(e){e.call(de,t)})},de.bind=function(e,t,n,o){for(var i=(e=e.split(" ")).length;i--;)if(we(t)){var r="scewys"+e[i],a="scesrc"+e[i];n||(fe[r]=fe[r]||[],fe[r].push(t)),o||(fe[a]=fe[a]||[],fe[a].push(t)),"valuechanged"===e[i]&&(ae.hasHandler=!0)}return de},de.unbind=function(e,t,n,o){for(var i=(e=e.split(" ")).length;i--;)we(t)&&(n||Ee(fe["scewys"+e[i]]||[],t),o||Ee(fe["scesrc"+e[i]]||[],t));return de},de.blur=function(e,t,n){return we(e)?de.bind("blur",e,t,n):de.sourceMode()?s.blur():g.blur(),de},de.focus=function(e,t,n){if(we(e))de.bind("focus",e,t,n);else if(de.inSourceMode())s.focus();else{if(Fe(d,":focus").length)return;var o,i=v.selectedRange();x||ne(),!wt&&i&&1===i.endOffset&&i.collapsed&&(o=i.endContainer)&&1===o.childNodes.length&&je(o.firstChild,"br")&&(i.setStartBefore(o.firstChild),i.collapse(!0),v.selectRange(i)),r.focus(),g.focus(),p&&(v.selectRange(p),p=null)}return G(),de},de.keyDown=function(e,t,n){return de.bind("keydown",e,t,n)},de.keyPress=function(e,t,n){return de.bind("keypress",e,t,n)},de.keyUp=function(e,t,n){return de.bind("keyup",e,t,n)},de.nodeChanged=function(e){return de.bind("nodechanged",e,!1,!0)},de.selectionChanged=function(e){return de.bind("selectionchanged",e,!1,!0)},de.valueChanged=function(e,t,n){return de.bind("valuechanged",e,t,n)},oe=function(e){var n=0,o=de.emoticonsCache,t=String.fromCharCode(e.which);Me(y,"code")||(o||(o=[],Se(ve,function(e,t){o[n++]=[e,t]}),o.sort(function(e,t){return e[0].length-t[0].length}),de.emoticonsCache=o,de.longestEmoticonCode=o[o.length-1][0].length),v.replaceKeyword(de.emoticonsCache,!0,!0,de.longestEmoticonCode,be.emoticonsCompat,t)&&(be.emoticonsCompat&&/^\s$/.test(t)||e.preventDefault()))},ie=function(){!function(e,t){var n=/[^\s\xA0\u2002\u2003\u2009\u00a0]+/,o=e&&Fe(e,"img[data-sceditor-emoticon]");if(e&&o.length)for(var i=0;i<o.length;i++){var r=o[i],a=r.parentNode,l=r.previousSibling,c=r.nextSibling;if(l&&n.test(l.nodeValue.slice(-1))||c&&n.test((c.nodeValue||"")[0])){var s=t.cloneSelected(),u=-1,d=s.startContainer,f=l.nodeValue;null===f&&(f=l.innerText||""),f+=Ve(r,"sceditor-emoticon"),d===c&&(u=f.length+s.startOffset),d===e&&e.childNodes[s.startOffset]===c&&(u=f.length),d===l&&(u=s.startOffset),c&&c.nodeType===Te||(c=a.insertBefore(a.ownerDocument.createTextNode(""),c)),c.insertData(0,f),Re(l),Re(r),-1<u&&(s.setStart(c,u),s.collapse(!0),t.selectRange(s))}}}(y,v)},de.emoticons=function(e){return e||!1===e?((be.emoticonsEnabled=e)?(ze(g,"keypress",oe),de.sourceMode()||(v.saveRange(),M(),ae(!1),v.restoreRange())):(Se(Fe(g,"img[data-sceditor-emoticon]"),function(e,t){var n=Ve(t,"sceditor-emoticon"),o=d.createTextNode(n);t.parentNode.replaceChild(o,t)}),Ae(g,"keypress",oe),ae()),de):be.emoticonsEnabled},de.css=function(e){return o||(o=De("style",{id:"inline"},d),He(d.head,o)),ye(e)?(o.styleSheet?o.styleSheet.cssText=e:o.innerHTML=e,de):o.styleSheet?o.styleSheet.cssText:o.innerHTML},j=function(e){var t=[],n={"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+",";":": ","'":'"',",":"<",".":">","/":"?","\\":"|","[":"{","]":"}"},o={109:"-",110:"del",111:"/",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"},i=e.which,r={8:"backspace",9:"tab",13:"enter",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",91:"win",92:"win",93:"select",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scrolllock",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"}[i]||String.fromCharCode(i).toLowerCase();(e.ctrlKey||e.metaKey)&&t.push("ctrl"),e.altKey&&t.push("alt"),e.shiftKey&&(t.push("shift"),o[i]?r=o[i]:n[r]&&(r=n[r])),r&&(i<16||18<i)&&t.push(r),t=t.join("+"),me[t]&&!1===me[t].call(de)&&(e.stopPropagation(),e.preventDefault())},de.addShortcut=function(e,t){return e=e.toLowerCase(),ye(t)?me[e]=function(){return R(he[t],de.commands[t]),!1}:me[e]=t,de},de.removeShortcut=function(e){return delete me[e.toLowerCase()],de},W=function(e){var t,n,o;if(!be.disableBlockRemove&&8===e.which&&(n=v.selectedRange())&&(t=n.startContainer,0===n.startOffset&&(o=re())&&!je(o,"body"))){for(;t!==o;){for(;t.previousSibling;)if((t=t.previousSibling).nodeType!==Te||t.nodeValue)return;if(!(t=t.parentNode))return}de.clearBlockFormatting(o),e.preventDefault()}},re=function(){for(var e=y;!tt(e)||it(e,!0);)if(!(e=e.parentNode)||je(e,"body"))return;return e},de.clearBlockFormatting=function(e){return!(e=e||re())||je(e,"body")||(v.saveRange(),e.className="",p=null,Oe(e,"style",""),je(e,"p,div,td")||nt(e,"p"),v.restoreRange()),de},ae=function(e){if(i&&(i.hasHandler("valuechangedEvent")||ae.hasHandler)){var t,n=de.sourceMode(),o=!n&&v.hasSelection();e=(m=!1)!==e&&!d.getElementById("sceditor-start-marker"),h&&(clearTimeout(h),h=!1),o&&e&&v.saveRange(),(t=n?s.value:g.innerHTML)!==ae.lastVal&&(ae.lastVal=t,Qe(w,"valuechanged",{rawValue:n?de.val():t})),o&&e&&v.removeMarkers()}},le=function(){h&&ae()},ce=function(e){var t=e.which,n=ce.lastChar,o=13===n||32===n,i=8===n||46===n;ce.lastChar=t,m||(13===t||32===t?o?ce.triggerNext=!0:ae():8===t||46===t?i?ce.triggerNext=!0:ae():ce.triggerNext&&(ae(),ce.triggerNext=!1),clearTimeout(h),h=setTimeout(function(){m||ae()},1500))},Y=function(e){(m=/start/i.test(e.type))||ae()},se=function(){de.updateOriginal()},N()}Et.locale={},Et.formats={},Et.icons={},Et.command={get:function(e){return mt[e]||null},set:function(e,t){return!(!e||!t)&&((t=Ce(mt[e]||{},t)).remove=function(){Et.command.remove(e)},mt[e]=t,this)},remove:function(e){return mt[e]&&delete mt[e],this}},window.sceditor={command:Et.command,commands:mt,defaultOptions:ct,ie:v,ios:ut,isWysiwygSupported:dt,regexEscape:ft,escapeEntities:pt,escapeUriScheme:function(e){var t,n=window.location;return e&&/^[^\/]*:/i.test(e)&&!b.test(e)?((t=n.pathname.split("/")).pop(),n.protocol+"//"+n.host+t.join("/")+"/"+e):e},dom:{css:Pe,attr:Oe,removeAttr:l,is:je,closest:Me,width:Ke,height:Xe,traverse:Je,rTraverse:Ze,parseHTML:et,hasStyling:tt,convertElement:nt,blockLevelList:s,canHaveChildren:ot,isInline:it,copyCSS:u,fixNesting:rt,findCommonAncestor:function(e,t){for(;e=e.parentNode;)if((n=e)!==(o=t)&&n.contains&&n.contains(o))return e;var n,o},getSibling:d,removeWhiteSpace:at,extractContents:f,getOffset:lt,getStyle:p,hasStyle:function(e,t,n){var o=p(e,t);return!!o&&(!n||o===n||Array.isArray(n)&&-1<n.indexOf(o))}},locale:Et.locale,icons:Et.icons,utils:{each:Se,isEmptyObject:t,extend:Ce},plugins:ht.plugins,formats:Et.formats,create:function(e,t){t=t||{},Ne(e,".sceditor-container")||(t.runWithoutWysiwygSupport||dt)&&new Et(e,t)},instance:function(e){return e._sceditor}},o.sceditor=window.sceditor,o.fn.sceditor=function(e){var t,n=[];return this.each(function(){t=this._sceditor,"state"===e?n.push(!!t):"instance"===e?n.push(t):t||o.sceditor.create(this,e)}),n.length?1===n.length?n[0]:n:this}}(jQuery),function(w){"use strict";var e=w.ie,C=e&&e<11,E=w.dom,t=w.utils,S=E.css,n=E.attr,k=E.is,T=E.removeAttr,o=E.convertElement,r=t.extend,a=t.each,D=t.isEmptyObject,l=w.command.get,c={bold:{txtExec:["<strong>","</strong>"]},italic:{txtExec:["<em>","</em>"]},underline:{txtExec:['<span style="text-decoration:underline;">',"</span>"]},strike:{txtExec:['<span style="text-decoration:line-through;">',"</span>"]},subscript:{txtExec:["<sub>","</sub>"]},superscript:{txtExec:["<sup>","</sup>"]},left:{txtExec:['<div style="text-align:left;">',"</div>"]},center:{txtExec:['<div style="text-align:center;">',"</div>"]},right:{txtExec:['<div style="text-align:right;">',"</div>"]},justify:{txtExec:['<div style="text-align:justify;">',"</div>"]},font:{txtExec:function(e){var t=this;l("font")._dropDown(t,e,function(e){t.insertText('<span style="font-family:'+e+';">',"</span>")})}},size:{txtExec:function(e){var t=this;l("size")._dropDown(t,e,function(e){t.insertText('<span style="font-size:'+e+';">',"</span>")})}},color:{txtExec:function(e){var t=this;l("color")._dropDown(t,e,function(e){t.insertText('<span style="color:'+e+';">',"</span>")})}},bulletlist:{txtExec:["<ul><li>","</li></ul>"]},orderedlist:{txtExec:["<ol><li>","</li></ol>"]},table:{txtExec:["<table><tr><td>","</td></tr></table>"]},horizontalrule:{txtExec:["<hr />"]},code:{txtExec:["<code>","</code>"]},image:{txtExec:function(e,t){var i=this;l("image")._dropDown(i,e,t,function(e,t,n){var o="";t&&(o+=' width="'+t+'"'),n&&(o+=' height="'+n+'"'),i.insertText("<img"+o+' src="'+e+'" />')})}},email:{txtExec:function(e,n){var o=this;l("email")._dropDown(o,e,function(e,t){o.insertText('<a href="mailto:'+e+'">'+(t||n||e)+"</a>")})}},link:{txtExec:function(e,n){var o=this;l("link")._dropDown(o,e,function(e,t){o.insertText('<a href="'+e+'">'+(t||n||e)+"</a>")})}},quote:{txtExec:["<blockquote>","</blockquote>"]},youtube:{txtExec:function(e){var n=this;l("youtube")._dropDown(n,e,function(e,t){n.insertText('<iframe width="560" height="315" src="https://www.youtube.com/embed/{id}?wmode=opaque&start='+t+'" data-youtube-id="'+e+'" frameborder="0" allowfullscreen></iframe>')})}},rtl:{txtExec:['<div stlye="direction:rtl;">',"</div>"]},ltr:{txtExec:['<div stlye="direction:ltr;">',"</div>"]}};function N(){var i=this,n={},b={};function e(e,t,n){var o,i,m,r,a,l,c,s,u,d,f,p,g,h,v=n.createElement("div");return v.innerHTML=t,S(v,"visibility","hidden"),n.body.appendChild(v),i=v,E.traverse(i,function(e){var t=e.nodeName.toLowerCase();y("*",e),y(t,e)},!0),m=v,E.traverse(m,function(e){var t,n=e.nodeName.toLowerCase(),o=e.parentNode,i=e.nodeType,r=!E.isInline(e),a=e.previousSibling,l=e.nextSibling,c=o===m,s=!a&&!l,u="iframe"!==n&&function e(t,n){var o,i=t.childNodes,r=t.nodeName.toLowerCase(),a=t.nodeValue,l=i.length,c=N.allowedEmptyTags||[];if(n&&"br"===r)return!0;if(k(t,".sceditor-ignore"))return!0;if(-1<c.indexOf(r)||"td"===r||!E.canHaveChildren(t))return!1;if(a&&/\S|\u00A0/.test(a))return!1;for(;l--;)if(!e(i[l],n&&!t.previousSibling&&!t.nextSibling))return!1;return!t.getBoundingClientRect||!t.className&&!t.hasAttributes("style")||(!(o=t.getBoundingClientRect()).width||!o.height)}(e,c&&s&&"br"!==n),d=e.ownerDocument,f=N.allowedTags,p=e.firstChild,g=N.disallowedTags;if(3!==i&&(4===i?n="!cdata":"!"!==n&&8!==i||(n="!comment"),1===i&&k(e,".sceditor-nlf")&&(!p||!C&&1===e.childNodes.length&&/br/i.test(p.nodeName)?u=!0:(e.classList.remove("sceditor-nlf"),e.className||T(e,"class"))),u?t=!0:f&&f.length?t=f.indexOf(n)<0:g&&g.length&&(t=-1<g.indexOf(n)),t)){if(!u){for(r&&a&&E.isInline(a)&&o.insertBefore(d.createTextNode(" "),e);e.firstChild;)o.insertBefore(e.firstChild,l);r&&l&&E.isInline(l)&&o.insertBefore(d.createTextNode(" "),l)}o.removeChild(e)}},!0),r=v,p=(f=N.allowedAttribs)&&!D(f),h=(g=N.disallowedAttribs)&&!D(g),b={},E.traverse(r,function(e){if(e.attributes&&(a=e.nodeName.toLowerCase(),s=e.attributes.length))for(b[a]||(b[a]=p?x(f["*"],f[a]):x(g["*"],g[a]));s--;)l=e.attributes[s],c=l.name,u=b[a][c],d=!1,p?d=null!==u&&(!Array.isArray(u)||u.indexOf(l.value)<0):h&&(d=null===u||Array.isArray(u)&&-1<u.indexOf(l.value)),d&&e.removeAttribute(c)}),e||function(e){var t;E.removeWhiteSpace(e);var n,o=e.firstChild;for(;o;)n=o.nextSibling,E.isInline(o)&&!k(o,".sceditor-ignore")?(t||(t=e.ownerDocument.createElement("p"),o.parentNode.insertBefore(t,o)),t.appendChild(o)):t=null,o=n}(v),o=(new w.XHTMLSerializer).serialize(v,!0),n.body.removeChild(v),o}function y(e,o){n[e]&&n[e].forEach(function(n){n.tags[e]?a(n.tags[e],function(e,t){o.getAttributeNode&&(!(e=o.getAttributeNode(e))||t&&t.indexOf(e.value)<0||n.conv.call(i,o))}):n.conv&&n.conv.call(i,o)})}function x(e,t){var n={};return e&&r(n,e),t&&a(t,function(e,t){Array.isArray(t)?n[e]=(n[e]||[]).concat(t):n[e]||(n[e]=null)}),n}i.init=function(){D(N.converters||{})||a(N.converters,function(e,t){a(t.tags,function(e){n[e]||(n[e]=[]),n[e].push(t)})}),this.commands=r(!0,{},c,this.commands)},i.toSource=e.bind(null,!1),i.fragmentToSource=e.bind(null,!0)}w.XHTMLSerializer=function(){var o={indentStr:"\t"},i=[],d=0;function f(e){var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"," ":"&nbsp;"};return e?e.replace(/[&<>"\xa0]/g,function(e){return t[e]||e}):""}function p(e,t){switch(e.nodeType){case 1:"!"===e.nodeName.toLowerCase()?n(e):function(e,t){var n,o,i,r=e.nodeName.toLowerCase(),a="iframe"===r,l=e.attributes.length,c=e.firstChild,s=t||/pre(?:\-wrap)?$/i.test(S(e,"whiteSpace")),u=!e.firstChild&&!E.canHaveChildren(e)&&!a;if(k(e,".sceditor-ignore"))return;g("<"+r,!t&&m(e));for(;l--;)o=e.attributes[l],i=o.value,g(" "+o.name.toLowerCase()+'="'+f(i)+'"',!1);g(u?" />":">",!1),a||(n=c);for(;n;)d++,p(n,s),n=n.nextSibling,d--;u||g("</"+r+">",!s&&!a&&m(e)&&c&&m(c))}(e,t);break;case 3:!function(e,t){var n=e.nodeValue;t||(n=n.replace(/[\r\n]/," ").replace(/[^\S|\u00A0]+/g," "));n&&g(f(n),!t&&m(e))}(e,t);break;case 4:g("<![CDATA["+f(e.nodeValue)+"]]>");break;case 8:n(e);break;case 9:case 11:!function(e){var t=e.firstChild;for(;t;)p(t),t=t.nextSibling}(e)}}function n(e){g("\x3c!-- "+f(e.nodeValue)+" --\x3e")}function g(e,t){var n=d;if(!1!==t)for(i.length&&i.push("\n");n--;)i.push(o.indentStr);i.push(e)}function m(e){var t=e.previousSibling;return 1!==e.nodeType&&t?!E.isInline(t):!t&&!E.isInline(e.parentNode)||!E.isInline(e)}this.serialize=function(e,t){if(i=[],t)for(e=e.firstChild;e;)p(e),e=e.nextSibling;else p(e);return i.join("")}},N.converters=[{tags:{"*":{width:null}},conv:function(e){S(e,"width",n(e,"width")),T(e,"width")}},{tags:{"*":{height:null}},conv:function(e){S(e,"height",n(e,"height")),T(e,"height")}},{tags:{li:{value:null}},conv:function(e){T(e,"value")}},{tags:{"*":{text:null}},conv:function(e){S(e,"color",n(e,"text")),T(e,"text")}},{tags:{"*":{color:null}},conv:function(e){S(e,"color",n(e,"color")),T(e,"color")}},{tags:{"*":{face:null}},conv:function(e){S(e,"fontFamily",n(e,"face")),T(e,"face")}},{tags:{"*":{align:null}},conv:function(e){S(e,"textAlign",n(e,"align")),T(e,"align")}},{tags:{"*":{border:null}},conv:function(e){S(e,"borderWidth",n(e,"border")),T(e,"border")}},{tags:{applet:{name:null},img:{name:null},layer:{name:null},map:{name:null},object:{name:null},param:{name:null}},conv:function(e){n(e,"id")||n(e,"id",n(e,"name")),T(e,"name")}},{tags:{"*":{vspace:null}},conv:function(e){S(e,"marginTop",n(e,"vspace")-0),S(e,"marginBottom",n(e,"vspace")-0),T(e,"vspace")}},{tags:{"*":{hspace:null}},conv:function(e){S(e,"marginLeft",n(e,"hspace")-0),S(e,"marginRight",n(e,"hspace")-0),T(e,"hspace")}},{tags:{hr:{noshade:null}},conv:function(e){S(e,"borderStyle","solid"),T(e,"noshade")}},{tags:{"*":{nowrap:null}},conv:function(e){S(e,"whiteSpace","nowrap"),T(e,"nowrap")}},{tags:{big:null},conv:function(e){S(o(e,"span"),"fontSize","larger")}},{tags:{small:null},conv:function(e){S(o(e,"span"),"fontSize","smaller")}},{tags:{b:null},conv:function(e){o(e,"strong")}},{tags:{u:null},conv:function(e){S(o(e,"span"),"textDecoration","underline")}},{tags:{s:null,strike:null},conv:function(e){S(o(e,"span"),"textDecoration","line-through")}},{tags:{dir:null},conv:function(e){o(e,"ul")}},{tags:{center:null},conv:function(e){S(o(e,"div"),"textAlign","center")}},{tags:{font:{size:null}},conv:function(e){S(e,"fontSize",S(e,"fontSize")),T(e,"size")}},{tags:{font:null},conv:function(e){o(e,"span")}},{tags:{"*":{type:["_moz"]}},conv:function(e){T(e,"type")}},{tags:{"*":{_moz_dirty:null}},conv:function(e){T(e,"_moz_dirty")}},{tags:{"*":{_moz_editor_bogus_node:null}},conv:function(e){e.parentNode.removeChild(e)}}],N.allowedAttribs={},N.disallowedAttribs={},N.allowedTags=[],N.disallowedTags=[],N.allowedEmptyTags=[],w.formats.xhtml=N}(sceditor);
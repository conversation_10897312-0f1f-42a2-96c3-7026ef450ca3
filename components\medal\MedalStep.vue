<template>
  <div class="medal-step" :style="style">
    <!--步骤条上方的勋章部分-->
    <div class="medal-card-container">
      <medal-card :name="name" :image-url="imageUrl" no-border no-mask>
        <template v-slot:description>
          <slot name="description">{{ description }}</slot>
        </template>
      </medal-card>
    </div>
    <!-- 步骤的数字图标和步骤条直线 -->
    <div class="medal-step-footer" :class="`is-${currentStatus}`">
      <!--步骤条直线-->
      <!--如果是最后一步，margin-right不存在；如果不是，则为0-->
      <div class="medal-step-line" :class="isLast && 'is-last'" :style="isLast ? '' : { marginRight: $parent.stepOffset + 'px' }">
        <i class="medal-step-line-inner" :style="lineStyle"></i>
      </div>
      <!--步骤条的数字图标-->
      <div class="medal-step-button">
        <!--根据当前状态判断按钮样式-->
        <el-button v-if="currentStatus === 'finish'" round size="mini" class="lk-primary-button" type="primary" disabled>{{
          $t('medal_center.got')
        }}</el-button>
        <el-button v-else-if="currentStatus === 'wait'" round size="mini" type="info" disabled>{{ $t('medal_center.unfinished') }}</el-button>
        <el-button v-else round size="mini" class="lk-primary-button" @click="handleMissionComplete">{{
          !waiting ? $t('medal_center.finish') : $t('medal_center.getting')
        }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import MedalCard from './MedalCard.vue';

export default {
  name: 'MedalStep',
  components: { MedalCard },
  props: {
    id: {
      type: Number,
      default: 0
    }, // 任务id
    name: {
      type: String,
      default: ''
    }, // 标题
    imageUrl: {
      type: String,
      default: ''
    }, // 图片链接
    description: {
      type: String,
      default: ''
    }, // 描述信息
    status: {
      type: Number,
      default: 0
    } // 任务状态
  },

  data() {
    return {
      index: -1,
      lineStyle: {}, // 步骤条直线的样式
      currentStatus: '', // 当前步骤的状态: wait（灰色）/ process（primary）/ finish（primary）,
      medalStatus: this.status,
      waiting: false
    };
  },
  computed: {
    // 前一step的状态
    prevStatus() {
      const prevStep = this.$parent.steps[this.index - 1];
      return prevStep ? prevStep.currentStatus : 'wait';
    },
    // 判断是否是最后一步
    isLast() {
      const parent = this.$parent;
      return parent.steps[parent.steps.length - 1] === this;
    },
    //  返回总步骤数
    stepsCount() {
      return this.$parent.steps.length;
    },
    // 构造样式
    style() {
      const style = {};
      const parent = this.$parent;
      const len = parent.steps.length;
      const space = typeof this.space === 'number' ? this.space + 'px' : this.space ? this.space : 100 / (len - (this.isCenter ? 0 : 1)) + '%';
      style.flexBasis = space;
      if (this.isLast) {
        style.maxWidth = 100 / this.stepsCount + '%';
      } else {
        style.marginRight = -this.$parent.stepOffset + 'px';
      }
      return style;
    }
  },
  beforeCreate() {
    this.$parent.steps.push(this);
  },
  mounted() {
    const unwatch = this.$watch('index', (val) => {
      this.$watch('$parent.active', this.updateStatus, { immediate: true });
      unwatch();
    });
  },
  beforeDestroy() {
    const steps = this.$parent.steps;
    const index = steps.indexOf(this);
    if (index >= 0) {
      steps.splice(index, 1);
    }
  },
  methods: {
    updateStatus(val) {
      const prevChild = this.$parent.$children[this.index - 1];
      if (val > this.index && this.medalStatus === 2) {
        this.currentStatus = this.$parent.finishStatus; // 已领取
      } else if (val === this.index && this.medalStatus === 1) {
        this.currentStatus = this.$parent.processStatus; // 待领取
      } else {
        this.currentStatus = 'wait'; // 未达成
      }
      if (prevChild) prevChild.calcProgress(this.currentStatus);
    },
    calcProgress(status) {
      let step = 100;
      const style = {};
      // transitionDelay在过渡效果开始前等待的秒数：
      style.transitionDelay = 150 * this.index + 'ms';
      if (status === this.$parent.processStatus) {
        step = this.currentStatus !== 'error' ? 0 : 0;
      } else if (status === 'wait') {
        step = 0;
        // 为负数的时候过渡的动作会从该时间点开始显示，之前的动作被截断；为正数的时候过渡的动作会延迟触发。
        style.transitionDelay = -150 * this.index + 'ms';
      }
      style.borderWidth = step ? '1px' : 0;
      style.width = step + '%';

      this.lineStyle = style;
    },
    handleMissionComplete() {
      // 提交任务，领取勋章。生产环境替换为对应api
      if (this.waiting) return false;

      this.waiting = true;
      this.$axios.$post('/api/medal/unlock', { id: this.id }).then((res) => {
        if (res.code === 0) {
          this.$parent.active += 1;
          this.medalStatus = 2;
        } else {
          this.waiting = false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scope>
.medal-card-container {
  display: flex;
  justify-content: center;
}
.medal-step-footer {
  position: relative;
  width: 100%;
  text-align: center;
}
.medal-step-footer.is-finish {
  color: #f3f3f3;
  border-color: #f3f3f3;
}
.medal-step-line.is-last {
  display: none;
}
.medal-step-line {
  position: absolute;
  height: 2px;
  top: 11px;
  left: 50%;
  right: -50%;
  background-color: #f3f3f3;
}
.medal-step-line-inner {
  display: block;
  border-width: 1px;
  border-style: solid;
  border-color: inherit;
  transition: 0.15s ease-out;
  box-sizing: border-box;
  width: 0;
  height: 0;
}
.medal-step-button {
  position: relative;
  z-index: 1;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  font-size: 14px;
  box-sizing: border-box;
  background: $white;
  transition: 0.15s ease-out;
  .el-button.is-disabled,
  .el-button.is-disabled:focus,
  .el-button.is-disabled:hover {
    color: #b8b8b8;
    background-color: #f3f3f3;
    border-color: #f3f3f3;
  }
}
/*按钮样式 */
.lk-primary-button {
  background-color: $primary;
  border-color: $primary;
  color: $white;
}
.lk-primary-button:focus,
.lk-primary-button:hover {
  background-color: $primary-hover;
  border-color: $primary-hover;
  color: $white;
}
</style>

/* eslint-disable standard/no-callback-literal */

// 全局工具方法
import Vue from 'vue';
import BMF from 'browser-md5-file';
import imageCompression from 'browser-image-compression';

export default ({ store }) => {
  // 回到顶部
  Vue.prototype.backtop = () => {
    const el = document.documentElement || document.body;
    el.scrollTop = 0;
  };

  // 必须要登录然后才能操作|否则显示登录框
  Vue.prototype.loginNext = function(fn) {
    if (store.state.login.isLogin) {
      typeof fn === 'function' && fn();
    } else {
      store.commit('login/toggleLoginLayer', {
        show: true
      });
    }
  };

  // 跳页面
  Vue.prototype.toPage = function(path, type = '_blank', query = {}) {
    path = String(path);
    if (path.indexOf('/') === 0) {
      path = path.substr(1);
    }
    path = this.$i18n.path(path);
    if (type === '_blank') {
      window.open(path, type);
      return;
    }
    if (Object.keys(query).length === 0) {
      this.$router[type]({ path });
    } else {
      this.$router[type]({ path, query });
    }
  };

  // 计算字符串字节长度
  Vue.prototype.getByteSize = (str) => {
    let count = 0;
    for (let i = 0, l = str.length; i < l; i++) {
      count += str.charCodeAt(i) <= 128 ? 1 : 2;
    }
    return count;
  };

  // 判断是否为空对象|数组
  Vue.prototype.isEmptyObject = (obj) => Object.keys(obj).length === 0;

  // 将文件对象读取为 base64 字符串格式
  Vue.prototype.file2base64 = (file) => {
    if (!(file instanceof window.File) && !(file instanceof window.Blob)) {
      throw new TypeError('the parameter must instance of File or Blob');
    }
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadend = (e) => resolve(e.target.result);
    });
  };

  /**
   * 获取文件 md5 值
   * docs: https://www.npmjs.com/package/browser-md5-file
   */
  Vue.prototype.getFileMD5 = (file) => {
    if (!(file instanceof window.File)) {
      throw new TypeError('the parameter must instance of File');
    }
    return new Promise((resolve, reject) => {
      if (!window.bmf) {
        window.bmf = new BMF();
      }
      window.bmf.md5(file, (err, md5) => (err ? reject(err) : resolve(md5)));
    });
  };

  /**
   * 压缩图片
   * docs: https://www.npmjs.com/package/browser-image-compression
   * file File 对象
   * maxSizeMB 低于这个值不压缩, 高于这个值就压缩到这个值一下
   * maxWidth 压缩后最大宽度
   * maxHeight 压缩后最大高度
   */
  Vue.prototype.imageCompression = async (file, maxSizeMB = 1, maxWidth = 1920, maxHeight = 1080) => {
    if (!(file instanceof window.File)) {
      throw new TypeError('the parameter must instance of File');
    }
    const blob = await imageCompression(file, {
      maxSizeMB,
      maxWidth,
      maxHeight,
      useWebWorker: false,
      fileType: file.type
    });
    return new window.File([blob], blob.name, {
      type: blob.type
    });
  };

  // 切换收藏状态
  Vue.prototype.toggleFavoriteState = function(isFav, payload, cb = null) {
    this.loginNext(() => {
      if (typeof this.lastToggleFavTime === 'undefined') {
        this.lastToggleFavTime = 0;
      }

      const delay = 1000; // 1s
      const now = Date.now();

      if (now - this.lastToggleFavTime > delay) {
        this.lastToggleFavTime = now;
        const data = [];
        if (isFav) {
          // 收藏
          data[0] = this.$t('tips.collection_success');
          data[1] = this.$t('tips.collection_fail');
          data[2] = 'user/addCollection';
        } else {
          // 取消收藏
          data[0] = this.$t('tips.cancel_success');
          data[1] = this.$t('tips.cancel_fail');
          data[2] = 'user/deleteCollection';
        }

        this.$store
          .dispatch(data[2], payload)
          .then(() => {
            this.$message.success(data[0]);
            typeof cb === 'function' && cb(true);
          })
          .catch(() => {
            this.$message.success(data[1]);
            typeof cb === 'function' && cb(false);
          });
      }
    });
  };

  // 跳转到粉丝关注页面
  Vue.prototype.toFansPage = function(uid, fansPage = true) {
    const pagetype = fansPage ? 'fans' : `stars`;
    return this.toPage(`/fans/${uid}?type=${pagetype}`);
  };

  // 切换用户关注状态
  Vue.prototype.toggleFollowState = function(uid, act, cb = null) {
    this.loginNext(() => {
      const data = [];
      if (act === 0) {
        // 关注
        data[0] = this.$t('tips.follow_success');
        data[1] = this.$t('tips.follow_fail');
      } else {
        // 取消关注
        data[0] = this.$t('tips.cancel_success');
        data[1] = this.$t('tips.cancel_fail');
      }

      const loginUser = this.$store.getters['login/loginUser'];
      if (uid === loginUser.uid) {
        this.$message.error(this.$t('tips.cannot_follow_self'));
        return;
      }

      this.$store
        .dispatch('user/followUser', { uid, act })
        .then(() => {
          this.$message.success(data[0]);
          typeof cb === 'function' && cb(true);
        })
        .catch(() => {
          this.$message.error(data[1]);
          typeof cb === 'function' && cb(false);
        });
    });
  };

  // 返回是否移动端
  Vue.prototype.isMobile = function(ua) {
    return !!ua.match(/AppleWebKit.*Mobile.*/);
  };

  // 执行推荐的动作
  Vue.prototype.doRecomAction = function(item, target = '_blank') {
    switch (item.action_type) {
      // 1文章
      case 1:
        this.toPage(`/detail/${item.action_params}`, target);
        break;
      // 2合集
      case 2:
        this.toPage(`/series/${item.action_params}`, target);
        break;
      // 2合集
      case 3: {
        const p = item.action_params.split(',');
        const gid = parseInt(p[0]);
        const type = gid === 3 || gid === 33 ? 1 : 0;
        this.toPage(`/category/${gid}/${p[1]}?type=${type}`, target);
        break;
      }
      // 4转跳url
      case 4:
        window.open(item.action_params, target);
        break;
      default:
        break;
    }
  };
};

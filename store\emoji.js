// 表情模块 ---

// state
export const state = () => ({
  emojis: []
});

// actions
export const actions = {
  async getEmojis({ commit, state }) {
    if (!state.emojis.length) {
      // 如果没有表情证明没有有缓存
      const res = await this.$axios.$post('/api/smiley/get-list');
      res.code === 0 && commit('getEmojis', res.data.list);
    }
  }
};

// mutations
export const mutations = {
  getEmojis(state, emojis) {
    state.emojis = emojis;
  }
};

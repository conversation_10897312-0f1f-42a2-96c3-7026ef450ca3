// eslint-disable-next-line no-undef
CKEDITOR.plugins.add('myfujian', {
  icons: 'myfujian',
  init(editor) {
    editor.addCommand('myfujian', {
      exec() {
        // 在 window 上触发事件
        const myEvent = new CustomEvent('ckeditormyfujianclick');
        if (window.dispatchEvent) {
          window.dispatchEvent(myEvent);
        } else {
          window.fireEvent(myEvent);
        }
      }
    });
    editor.ui.addButton('myfujian', {
      label: '上传附件',
      command: 'myfujian',
      toolbar: 'insert'
    });
  }
});

import Vue from 'vue';
import { Dialog, Rate, Upload, Message, Drawer, MessageBox, Pagination, InfiniteScroll, Button, Row, Col, Card, Image } from 'element-ui';
// import 'element-ui/lib/theme-chalk/index.css';

Vue.use(InfiniteScroll);
Vue.use(Dialog);
Vue.use(Rate);
Vue.use(Upload);
Vue.use(Message.name, Message);
Vue.use(Drawer);
Vue.use(MessageBox.name, MessageBox);
Vue.use(Pagination);
Vue.use(Button);
Vue.use(Row);
Vue.use(Col);
Vue.use(Card);
Vue.use(Image);
Vue.prototype.$message = Message;
Vue.prototype.$msgbox = MessageBox;
Vue.prototype.$alert = MessageBox.alert;
Vue.prototype.$confirm = MessageBox.confirm;
Vue.prototype.$prompt = MessageBox.prompt;

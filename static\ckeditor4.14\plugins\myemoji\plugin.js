CKEDITOR.plugins.add("myemoji", {
  icons: "myemoji",
  init: function (editor) {
    editor.addCommand("myemoji", {
      exec: function () {
        // 在 window 上触发事件
        var myEvent = new CustomEvent("ckeditormyemojiclick");
        if (window.dispatchEvent) {
          window.dispatchEvent(myEvent);
        } else {
          window.fireEvent(myEvent);
        }
      },
    });
    editor.ui.addButton("myemoji", {
      label: "表情",
      command: "myemoji",
      toolbar: "insert",
    });
  },
});

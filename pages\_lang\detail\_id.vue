<template>
  <!-- eslint-disable vue/no-v-html -->
  <div>
    <ul class="sidebar-buttons">
      <li class="btn-item hover">
        <a href="#comments">
          <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 629.84 509.79">
            <path
              class="cls-1"
              d="M85.2,190.88V521a46.31,46.31,0,0,0,46.26,46.26h81.81a10.12,10.12,0,0,1,8.74,5l7.84,70a24.23,24.23,0,0,0,41.94,0l73-70a10.1,10.1,0,0,1,8.73-5H668.78A46.31,46.31,0,0,0,715,521V190.88a46.31,46.31,0,0,0-46.26-46.25H131.46A46.31,46.31,0,0,0,85.2,190.88ZM524.38,352.33a47.19,47.19,0,1,1,47.19,47.19A47.19,47.19,0,0,1,524.38,352.33Zm-171.46,0a47.2,47.2,0,1,1,47.2,47.19A47.2,47.2,0,0,1,352.92,352.33Zm-171.45,0a47.2,47.2,0,1,1,47.2,47.19A47.2,47.2,0,0,1,181.47,352.33Z"
              transform="translate(-85.2 -144.63)"
            />
          </svg>
        </a>
      </li>
      <li class="btn-item hover" @click="changeTC('t')">
        <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M709.529751 704.632789q-10.99786-5.491529-12.063602-17.022261t8.777566-19.242555q16.474588-10.968257 34.044521-10.968257a65.868747 65.868747 0 0 1 25.163342 5.476727q125.091412 62.567909 212.896672 127.296905a29.603931 29.603931 0 0 1 0 46.093321 47.677131 47.677131 0 0 1-58.142121 0q-16.474588-13.173749-34.044521-26.347499a27.235617 27.235617 0 0 0-15.364441-4.440589l-312.780336 8.777565c-5.12148 0-7.697022 2.960393-7.697022 8.792368v159.254349a40.305753 40.305753 0 0 1-12.063602 29.603931 41.045851 41.045851 0 0 1-58.156923 0 40.261347 40.261347 0 0 1-12.078404-29.603931v-156.900836q0-8.747962-8.792368-7.667419l-395.064464 8.777566a34.6366 34.6366 0 0 1-24.704481-9.887713 33.837294 33.837294 0 0 1-2.190691-48.846487 30.240416 30.240416 0 0 1 23.594334-10.361376q62.567909 0 248.036539-2.175889a27.235617 27.235617 0 0 0 15.36444-4.440589l10.953455-5.49153a3.892917 3.892917 0 0 0 2.205492-3.833709 3.937323 3.937323 0 0 0-2.205492-3.848511q-72.426018-46.093321-163.517315-89.995951a22.202949 22.202949 0 0 1-12.566869-18.176814 20.722752 20.722752 0 0 1 8.244695-20.308297q17.540329-14.239491 37.300953-14.269095a56.410291 56.410291 0 0 1 25.252154 6.601677q24.142006 9.858109 44.997975 20.841168a14.195085 14.195085 0 0 0 14.269095-1.095346q31.824226-21.936513 80.108238-60.362416a2.175889 2.175889 0 0 0 1.095346-3.286036c-0.725296-1.480197-1.480197-2.205493-2.190691-2.205493h-52.665394q-25.266955-1.080543-34.044521-30.714079a20.249089 20.249089 0 0 1 2.753166-16.474588c3.300838-5.106678 8.229893-7.68222 14.801965-7.68222q27.41324 2.220295 60.362416 2.190691 20.85597 0 29.603932-9.872911a80.537495 80.537495 0 0 0 12.078404-21.951315 5.003064 5.003064 0 0 0-1.110148-5.476728 7.474993 7.474993 0 0 0-5.476727-2.205492H128.981856a7.785834 7.785834 0 0 0-7.68222 5.491529 33.304423 33.304423 0 0 1-1.095346 7.68222 41.963573 41.963573 0 0 1-15.838103 22.484186 33.926105 33.926105 0 0 1-25.799826 6.054004 27.102399 27.102399 0 0 1-20.85597-12.078404 31.454177 31.454177 0 0 1-5.476727-24.142006q12.0488-48.284012 26.332697-118.534141 2.205493-8.747962-6.586875-8.762764H31.318486a26.421509 26.421509 0 0 1-18.724486-7.149349 23.683145 23.683145 0 0 1-7.667419-18.117606 26.199479 26.199479 0 0 1 26.391905-26.288291h49.364556c5.920786 0 9.147615-2.545938 9.887713-7.697022q12.0488-63.648452 16.459786-95.472679c0-0.710494-0.562475-1.480197-1.643018-2.205493s-2.027869-0.710494-2.738364 0a135.349174 135.349174 0 0 1-9.887713 10.983059 41.179068 41.179068 0 0 1-27.442845 12.078404 43.044116 43.044116 0 0 1-29.603931-8.792368 26.983983 26.983983 0 0 1-12.078404-20.841168 25.163342 25.163342 0 0 1 8.777566-21.951315q63.648452-59.207863 103.1697-130.612545a45.797282 45.797282 0 0 1 23.031859-20.870771 41.238276 41.238276 0 0 1 18.650477-4.44059 57.727666 57.727666 0 0 1 10.983058 1.095345 27.975715 27.975715 0 0 1 19.242556 15.912113q6.009598 12.611275-0.547673 25.785025c-2.960393 5.920786-5.920786 11.353108-8.777566 16.459785-1.480197 1.480197-1.643018 2.738364-0.562474 3.848511a5.299104 5.299104 0 0 0 3.848511 1.643019h308.354549a28.523388 28.523388 0 0 1 20.855969 8.851575 27.872101 27.872101 0 0 1 0 40.601792 28.508586 28.508586 0 0 1-20.855969 8.777566H164.092118q-8.792368 0-13.158947 6.586874-13.188551 15.394044-34.044521 39.506447a4.544203 4.544203 0 0 0 0 4.440589 3.434056 3.434056 0 0 0 3.286036 2.175889h316.095977q26.362301 0 43.90263 18.665279 16.444984 17.510725 16.444984 40.557386 0 4.44059-4.44059 47.188667c0 5.151084 2.545938 7.697022 7.667419 7.697022h48.298814a2.56074 2.56074 0 0 0 2.738363-1.65782 5.017866 5.017866 0 0 0-0.562474-3.83371 55.684995 55.684995 0 0 1-1.080544-80.12304q68.103844-75.697252 107.610291-173.390226a46.774212 46.774212 0 0 1 20.855969-21.936513 42.185602 42.185602 0 0 1 29.603932-4.440589 31.691009 31.691009 0 0 1 22.513789 17.02226 30.299624 30.299624 0 0 1 0.532871 27.990517q-9.872911 24.142006-23.04666 51.570049a2.960393 2.960393 0 0 0-0.532871 4.440589 5.713559 5.713559 0 0 0 4.929054 2.175889h270.002656a31.602197 31.602197 0 0 1 23.046661 9.887713 33.111997 33.111997 0 0 1 0 47.203469 31.631801 31.631801 0 0 1-23.046661 9.872911h-34.044521c-5.920786 0-9.532466 2.575542-10.983058 7.68222q-36.176004 116.313846-109.71217 200.818268c-3.670887 3.670887-3.670887 7.312171 0 10.968257a539.383629 539.383629 0 0 0 173.405028 94.377333q13.158947 4.44059 17.555131 18.665279a37.241746 37.241746 0 0 1 1.095346 8.792367 27.753686 27.753686 0 0 1-5.49153 16.444984 57.07638 57.07638 0 0 1-28.449378 21.403643 51.58485 51.58485 0 0 1-34.044521 0.562474 584.455615 584.455615 0 0 1-177.786409-108.66123q-6.601677-5.476727-12.063602 0-69.184388 55.95143-166.877361 96.582826a45.338421 45.338421 0 0 1-31.824227 1.643019 55.773807 55.773807 0 0 1-27.383636-18.117606 25.296559 25.296559 0 0 1 9.872911-41.697138q97.692973-36.205608 165.782016-91.091296 5.476727-5.491529 1.095345-12.063603-49.453367-60.347614-85.718183-138.294765a9.784099 9.784099 0 0 0-4.943857-2.738363 3.138017 3.138017 0 0 0-3.833709 1.65782 446.560502 446.560502 0 0 1-47.203468 50.474703q-6.572073 5.506331-12.063602 0h-1.095346v4.440589a44.983174 44.983174 0 0 1-45.012777 45.012778h-16.444984c-5.12148 0-8.067071 2.56074-8.777566 7.667418q-1.110147 8.881179-6.586875 53.775541c-0.740098 5.920786 1.820642 8.777566 7.682221 8.777566h41.697137a25.444579 25.444579 0 0 1 18.665279 7.697022 25.163342 25.163342 0 0 1 0 36.205608 25.429777 25.429777 0 0 1-18.665279 7.68222h-50.489505c-5.846776 0-9.502862 2.575542-10.968257 7.682221q-5.521133 26.273489-13.173749 39.447238c-1.480197 3.670887-0.370049 6.216826 3.300838 7.667418a29.204278 29.204278 0 0 1 17.007459 24.689679q1.643018 17.051864-11.515929 26.895172a1358.139558 1358.139558 0 0 1-115.233303 82.313731c-2.205493 0.725296-3.123215 2.027869-2.738364 3.848511a9.221625 9.221625 0 0 0 2.738364 4.929055q46.078519 26.362301 61.457761 36.22041a10.953455 10.953455 0 0 0 13.158948 0q110.837119-64.743798 189.879615-124.010869 17.510725-13.188551 36.205608-13.188551 14.239491 0 29.603932 8.777565a22.883839 22.883839 0 0 1 12.65568 20.308297 23.49072 23.49072 0 0 1-10.361376 21.403643q-113.042612 75.712054-229.430468 139.375308c-0.740098 0-0.932524 0.532871-0.547672 1.643019s0.917722 1.643018 1.643018 1.643018q109.726972-1.095345 325.954086-5.49153a2.545938 2.545938 0 0 0 2.738364-1.643018c0.370049-1.095345-0.192426-2.013067-1.643019-2.738363Q746.860309 723.283265 709.529751 704.632789zM249.691886 857.093035q21.951315-12.033998 42.807285-12.063602a79.057299 79.057299 0 0 1 28.538189 5.491529q12.0488 4.44059 13.158948 17.007459t-8.777566 19.242555q-93.252384 59.281873-216.19751 107.565885a57.994102 57.994102 0 0 1-24.0976 5.521133q-24.156808 0-44.997976-19.760624c-5.920786-5.920786-8.052269-13.173749-6.586875-21.966117s6.586875-14.624342 15.364441-17.555132q120.680426-38.381497 200.788664-83.409076z m1.110147-507.026532c0.710494-5.831974-1.480197-8.762764-6.586874-8.762764h-88.811794q-8.881179 0-9.887714 8.762764-6.572073 34.044521-10.983058 53.790343c-0.725296 5.12148 1.480197 7.667418 6.601677 7.667418h92.186642c5.106678 0 8.022665-2.960393 8.777565-8.777565q8.703556-49.275744 8.703556-52.606186z m-74.631511-125.106214c-5.12148 0-8.422318 2.575542-9.887713 7.68222l-8.777565 49.379358c-1.480197 5.151084 0.725296 7.697022 6.586874 7.697022h86.709915c5.846776 0 9.147615-2.545938 9.872911-7.697022q2.175889-18.635675 5.49153-49.379358c0.725296-5.106678-1.835444-7.68222-7.697023-7.68222z m126.20156 177.78641c-0.725296 5.920786 1.480197 8.777566 6.601676 8.777565h96.568024c5.846776 0 9.132813-2.960393 9.887713-8.777565q4.351778-26.332697 6.572073-52.680196c0.710494-5.831974-1.480197-8.762764-6.572073-8.762764h-95.502282c-5.12148 0-8.052269 2.960393-8.777566 8.762764q-5.476727 33.008383-8.777565 52.754206z m17.555131-120.724832c-0.725296 5.151084 1.480197 7.697022 6.601677 7.697022h94.362531c5.136282 0 8.052269-2.545938 8.792367-7.697022q1.095345-30.714079 3.300839-49.379358c0-5.106678-2.575542-7.68222-7.68222-7.68222h-90.025556c-5.846776 0-9.147615 2.575542-9.872911 7.68222z m535.579524-99.854061a5.432321 5.432321 0 0 0-5.491529-7.68222H678.815673a5.802371 5.802371 0 0 0-4.943857 2.190691 5.683955 5.683955 0 0 0-0.547673 5.491529q31.824226 80.108238 89.98115 150.358368 4.44059 5.491529 9.887713 0a424.816415 424.816415 0 0 0 82.313731-150.284358zM696.444814 890.101419a20.219485 20.219485 0 0 1-13.17375-15.926916c-1.480197-7.667418 1.110147-14.061867 7.682221-19.242555q19.760624-14.239491 40.616593-14.254293a79.930615 79.930615 0 0 1 23.031859 3.286037q118.548943 40.616594 211.816129 92.186642 12.078404 6.572073 13.173749 21.403642t-9.887713 23.594333a51.318415 51.318415 0 0 1-32.357097 12.626077 62.641919 62.641919 0 0 1-33.482046-8.229893q-87.894072-51.570048-207.419945-95.443074z"
          ></path>
        </svg>
      </li>
      <li class="btn-item hover" @click="changeCN('s')">
        <svg class="svg-icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M208.416949 157.27579c-2.180677-5.117916-6.230507-7.699126-12.09015-7.699126H167.740641a16.688857 16.688857 0 0 0-13.217575 6.60137 752.689709 752.689709 0 0 1-64.886277 83.592631q-10.992394 12.104984-29.14987 13.751618a47.470527 47.470527 0 0 1-32.428304-8.262838A31.553066 31.553066 0 0 1 13.758119 217.771043a32.250289 32.250289 0 0 1 8.796882-21.92545A611.568733 611.568733 0 0 0 139.065476 28.586158 50.437435 50.437435 0 0 1 162.177688 5.48878a48.479276 48.479276 0 0 1 19.804111-4.450362 76.753908 76.753908 0 0 1 12.09015 1.097756 33.095858 33.095858 0 0 1 21.984787 18.706354 32.472807 32.472807 0 0 1 0 28.586158l-13.187905 26.390646a5.78547 5.78547 0 0 0 0 4.954737 4.257513 4.257513 0 0 0 4.450362 2.759224h254.085995a34.000765 34.000765 0 0 1 24.195135 9.330925 30.573986 30.573986 0 0 1 9.879803 23.097379 32.309627 32.309627 0 0 1-9.879803 23.661091 32.917844 32.917844 0 0 1-24.195135 9.894638H294.205092a4.450362 4.450362 0 0 0-4.450362 2.210346 6.987068 6.987068 0 0 0 0 5.48878 473.503672 473.503672 0 0 1 26.40548 49.488024 34.119441 34.119441 0 0 1-1.097756 28.052115 33.184865 33.184865 0 0 1-21.450744 18.157476 38.65881 38.65881 0 0 1-29.164705-2.74439 39.059343 39.059343 0 0 1-19.774441-21.450744q-14.270827-31.849757-36.255615-71.443143z m254.041492 347.55843q0 27.532906-19.284902 46.209591a63.254477 63.254477 0 0 1-45.571706 18.632182H155.635656c-5.933816 0-8.796882 2.966908-8.796882 8.796882v405.91751a39.163185 39.163185 0 0 1-11.007228 28.03728 38.451127 38.451127 0 0 1-54.991639 0 39.14835 39.14835 0 0 1-10.992394-28.03728v-637.885207a63.254477 63.254477 0 0 1 18.69152-45.645879q18.69152-19.284902 46.194757-19.284901h262.868043q26.390646 0 45.645879 19.284901t19.284901 45.645879z m-76.991261-159.486136c0-5.844809-2.566375-8.796882-7.684292-8.796882H155.635656c-5.933816 0-8.796882 2.966908-8.796882 8.796882v45.111835c0 5.132751 2.966908 7.684292 8.796882 7.684292h222.176901c5.117916 0 7.684292-2.551541 7.684292-7.684292zM146.838774 505.887472c0 5.933816 2.966908 8.796882 8.796882 8.796882h222.176901c5.117916 0 7.684292-2.966908 7.684292-8.796882v-46.135418c0-5.132751-2.566375-7.713961-7.684292-7.713961H155.635656c-5.933816 0-8.796882 2.58121-8.796882 7.713961z m506.985229 121.03501q27.473568 0 46.194756 19.284901a63.32865 63.32865 0 0 1 18.69152 45.645879v175.996979a63.313815 63.313815 0 0 1-18.69152 45.645878q-18.706355 19.284902-46.194756 19.284902H383.316171a3.886649 3.886649 0 0 0-4.450361 4.450362 35.143025 35.143025 0 0 1-10.458351 25.856603 33.82275 33.82275 0 0 1-24.744012 10.384177h-2.210347a35.94409 35.94409 0 0 1-26.375811-11.007228 37.279198 37.279198 0 0 1-11.007229-27.488402V691.808758a63.269312 63.269312 0 0 1 18.69152-45.645878q18.69152-19.195894 46.209592-19.284902z m-8.811717 61.593009c0-5.844809-2.966908-8.796882-8.796882-8.796883H387.707195c-5.933816 0-8.796882 2.966908-8.796882 8.796883v56.089394c0 5.147585 2.966908 7.713961 8.796882 7.713961h248.567547c5.933816 0 8.796882-2.566375 8.796882-7.713961zM378.910313 871.099005c0 5.933816 2.966908 8.796882 8.796882 8.796882h248.567547c5.933816 0 8.796882-2.966908 8.796882-8.796882v-58.299741c0-5.844809-2.966908-8.796882-8.796882-8.796882H387.707195c-5.933816 0-8.796882 2.966908-8.796882 8.796882zM647.281971 75.878671a3.678966 3.678966 0 0 0-0.548878 4.954736 4.613542 4.613542 0 0 0 3.842145 2.759224h325.573643a34.000765 34.000765 0 0 1 24.195134 9.330926 30.588821 30.588821 0 0 1 9.894638 23.097378 32.324462 32.324462 0 0 1-9.894638 23.661091 32.917844 32.917844 0 0 1-24.195134 9.894638H783.670729a3.500951 3.500951 0 0 0-3.308103 2.210346 4.613542 4.613542 0 0 0 0 4.450362q19.80411 26.390646 36.300119 51.698371a30.425641 30.425641 0 0 1 2.195512 27.488402q-5.518449 14.330165-20.887032 18.69152a40.705977 40.705977 0 0 1-13.217575 2.210346 49.873722 49.873722 0 0 1-17.593764-3.293268 51.56486 51.56486 0 0 1-24.195134-20.901866q-19.774441-36.285284-51.683537-75.952843-4.450362-6.586536-13.20274-6.60137h-71.502481a16.644354 16.644354 0 0 0-13.187906 6.60137 557.867699 557.867699 0 0 1-67.141127 75.893505 47.618872 47.618872 0 0 1-31.89426 14.300496h-3.263599a57.67669 57.67669 0 0 1-30.78167-8.796882q-14.330165-7.669457-14.300496-25.307725a23.972616 23.972616 0 0 1 9.894638-19.789276 554.040388 554.040388 0 0 0 126.479286-162.779404A49.443521 49.443521 0 0 1 605.493072 5.48878 48.627621 48.627621 0 0 1 627.47786 0.029669a29.47623 29.47623 0 0 1 11.007229 2.195512c11.007228 2.195512 18.69152 8.084824 23.097378 17.593764a32.635987 32.635987 0 0 1 0 28.600993q-6.60137 13.173071-14.300496 27.458733z m251.875649 205.695727q26.390646 0 45.645879 19.284901t19.284901 45.645879v582.99741q0 35.172694-9.894638 53.893882t-33.051354 28.497151q-35.202363 12.075315-130.885144 12.104985h-2.195512a48.835305 48.835305 0 0 1-30.232792-10.384178 61.459498 61.459498 0 0 1-20.367823-28.03728c-2.966908-8.099659-1.839483-15.961965 3.308103-23.661091s12.09015-11.185243 20.887032-10.384178h101.201229c8.055155-0.741727 13.751618-2.58121 17.044887-5.503614s4.954736-8.426019 4.954736-16.496008V578.576717c0-5.933816-2.966908-8.796882-8.811717-8.796882H621.974246a63.254477 63.254477 0 0 1-45.645879-18.69152q-19.284902-18.69152-19.284901-46.209591V346.460674q0-26.390646 19.284901-45.645878t45.645879-19.284902z m-14.300496 63.78852c0-5.844809-2.966908-8.796882-8.811717-8.796882H641.778356c-5.132751 0-7.699126 2.966908-7.699126 8.796882v45.111836c0 5.132751 2.566375 7.684292 7.699126 7.684291h234.267051c5.933816 0 8.811717-2.551541 8.811717-7.684291zM634.07923 505.887472c0 5.933816 2.566375 8.796882 7.699126 8.796882h234.267051c5.933816 0 8.811717-2.966908 8.811717-8.796882v-46.135418c0-5.132751-2.966908-7.713961-8.811717-7.713961H641.778356c-5.132751 0-7.699126 2.58121-7.699126 7.713961z"
          ></path>
        </svg>
      </li>
      <li v-backtop class="btn-item hover">
        <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 740.47 440.2">
          <path
            d="M400,281.4,97.8,607.5a38.31,38.31,0,0,1-56.8,0c-14.9-16.1-14.9-42.2,0-58.3L371.6,192.5a38.31,38.31,0,0,1,56.8,0L759,549.2a43.25,43.25,0,0,1,0,58.3,38.42,38.42,0,0,1-56.9,0Z"
            transform="translate(-29.83 -179.9)"
          />
        </svg>
      </li>
    </ul>
    <div v-if="hasAccessPermission" class="detail-contents flex jc-between">
      <div class="left-contents">
        <!-- 文章 -->
        <article class="article">
          <!-- 标题 -->
          <h2 class="article-title" v-html="article.title"></h2>
          <!-- <div class="btn" @click="toZhi">简体</div>
          <div class="btn" @click="toTc">繁体</div> -->
          <!-- 文章信息 -->
          <div class="article-infos">
            <!-- <span class="article-infos-item fs-xs text-gray">小分区</span> -->
            <span class="article-infos-item fs-xs text-gray">{{ article.time | date2short }}</span>
            <span class="article-infos-item fs-xs text-gray">{{ $t('detail.reads') }}: {{ article.hits | num2short }}</span>
            <span class="article-infos-item fs-xs text-gray">{{ $t('detail.comments') }}: {{ article.comments | num2short }}</span>
            <!-- 投币 -->
            <div
              v-clickoutside="closeGiveCoins"
              class="article-infos-item fs-xs text-gray counter-item btn"
              @click="headShowGiveCoins = !headShowGiveCoins"
            >
              <svg :class="{ active: article.already_coin }" class="svg-icon coins" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 755 752.98">
                <path
                  d="M643.67,109.3C497.93-36.44,255-36.44,109.3,109.3s-145.73,388.63,0,534.37,388.63,145.73,534.37,0S794.8,255,643.67,109.3ZM465.55,470.94A575.48,575.48,0,0,0,408.77,537a38.21,38.21,0,0,1-61,1.11c-17.48-22.4-38-44.8-60.37-67.2-22.54-22.53-45.08-43.15-67.62-60.7-20.15-15.7-19.34-46.35,1.44-61.21a524.78,524.78,0,0,0,66.18-56.2,709.47,709.47,0,0,0,59.29-68.1,38.2,38.2,0,0,1,61.18.82,574.71,574.71,0,0,0,57.65,67.28,575.56,575.56,0,0,0,65.18,56.11,38.21,38.21,0,0,1,0,61.48,415.61,415.61,0,0,0-65.23,60.52Z"
                  transform="translate(-0.01 0)"
                />
              </svg>
              <span>{{ article.coins | num2short }}</span>
              <div :class="{ show: headShowGiveCoins }" class="coins-container hide tail-arrow-top">
                <div class="flex ai-center jc-between">
                  <!-- 10 -->
                  <img src="@/assets/images/coin10.png" class="give-coin-item" @click="giveCoins(10)" />
                  <!-- 30 -->
                  <img src="@/assets/images/coin30.png" class="give-coin-item" @click="giveCoins(30)" />
                  <!-- 50 -->
                  <img src="@/assets/images/coin50.png" class="give-coin-item" @click="giveCoins(50)" />
                </div>
                <p class="tips text-center text-gray fs-sm">{{ $t('detail.give_coin_number') }}</p>
              </div>
            </div>

            <!-- 收藏 -->
            <div class="article-infos-item fs-xs text-gray hover counter-item" :class="{ active: article.already_fav }" @click="toggleCollection">
              <svg class="svg-icon collections" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 752.69 721.35">
                <path
                  d="M376.51,0a53.94,53.94,0,0,0-49.3,30.64l-70.55,143a68.66,68.66,0,0,1-51.72,37.58L47.2,234.09a55,55,0,0,0-30.47,93.73L130.88,439.13a68.65,68.65,0,0,1,
                  19.75,60.79L123.63,657a55,55,0,0,0,79.77,58l141.15-74.22a68.73,68.73,0,0,1,63.92,0L549.57,715a55,55,0,0,0,79.77-58l-27-157.12a68.68,68.68,0,0,1,19.76-60.79L736.3,327.82a55,55,0,0,0-30.48-93.77L548.08,211.17a68.67,68.67,0,0,1-51.71-37.57l-70.56-143A53.94,53.94,0,0,0,376.51,0Z"
                  transform="translate(-0.19 0)"
                />
              </svg>
              <span>{{ article.favorites | num2short }}</span>
            </div>
            <!-- 分享 -->
            <div class="article-infos-item fs-xs text-gray counter-item btn" @click="showShare = !showShare">
              <svg class="svg-icon share" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 767.75 576.45">
                <path
                  d="M728.75,167,457.81,10.57c-52-30-117,7.5-117,67.54v60.61C157.21,164.89,81.12,248.49,37.23,334.64a324.63,324.63,0,0,0-29.9,90.2c-1.87,10.45-2.73,7.82-5.86,39.53-3.53,35.87-.42,62.29,6.34,92.58,5.2,23.37,28.45,26.46,38.67,5.37l2.21-4.54c51-115.45,168.75-204.44,292.15-213V391c0,60,65,97.57,117,67.55l271-156.43C780.74,272.07,780.74,197,728.75,167Z"
                  transform="translate(0 -0.01)"
                />
              </svg>
              <div :class="{ show: showShare }" class="other-coins-container hide tail-arrow-top">
                <div class="social-share" data-disabled="google,tencent,diandian" data-source="http://baidu.com"></div>
              </div>
            </div>

            <!-- 点赞 -->
            <div class="article-infos-item fs-xs text-gray counter-item btn" :class="{ active: article.already_like }" @click="zanArticle">
              <svg class="svg-icon zan" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 596.85 626.72">
                <path
                  d="M54.78,192.55C24.5,192.55,0,221.16,0,256.45v297.9c0,35.29,24.54,63.9,54.82,63.9s54.83-28.61,54.83-63.9V256.45C109.65,221.16,85.1,192.55,54.78,192.55Z"
                  transform="translate(0.04 0)"
                />
                <path
                  d="M509.31,213.62H351l39.23-96.08C406.18,78.42,384.18,0,331.11,0c-34.22,0-64.5,17.17-74.81,42.41L165.2,198.66a64.09,64.09,0,0,0-8.72,32.27V559.32c0,37.23,39.15,67.4,87.45,67.4h197c42,0,78.1-23,85.92-54.85l68.41-278.3C605.43,252.06,564.13,213.62,509.31,213.62Z"
                  transform="translate(0.04 0)"
                />
              </svg>
              <span>{{ article.likes | num2short }}</span>
            </div>

            <!-- 回复 -->
            <a href="#comments" class="reply-link text-primary fs-xs">{{ $t('detail.reply') }}</a>
          </div>

          <!-- 文章正文内容 -->
          <div class="article-content">
            <!-- <Loading v-show="isWebTorrent" />
            <div v-show="!isWebTorrent" class="log" :class="classSize"></div> -->
            <article id="article-main-contents" v-html="article.content"></article>

            <!-- 文章锁定 -->
            <div v-if="article.pay_info && article.pay_info.is_paid == 0" class="article-content-lock flex flex-cols jc-center ai-center">
              <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 152.47 170.92">
                <path
                  d="M139.67,15.83H45.92a8.26,8.26,0,0,0-8.25,8.25v43a8.26,8.26,0,0,0,8.25,8.25h4.8A8.26,8.26,0,0,0,59,67.1V45.38a8.26,8.26,0,0,1,8.25-8.25h64.2a8.25,8.25,0,0,1,8.25,8.25V67.1a8.26,8.26,0,0,0,8.25,8.25h4.8A8.26,8.26,0,0,0,161,67.1v-43a8.26,8.26,0,0,0-8.25-8.24ZM167.32,84.7h-136A8.26,8.26,0,0,0,23.1,93V178.5a8.26,8.26,0,0,0,8.25,8.25h136a8.25,8.25,0,0,0,8.25-8.25V93A8.26,8.26,0,0,0,167.32,84.7Zm-46,85.3h-44a3.52,3.52,0,0,1-3-5.3L89,139.25a20.35,20.35,0,1,1,20.8,0l14.7,25.47a3.54,3.54,0,0,1-3.1,5.28Z"
                  transform="translate(-23.1 -15.83)"
                />
              </svg>
              <span class="fs-md mar-left-5 text-primary">{{ $t('detail.content_need_unlock') }}</span>
              <button class="btn fs-sd mar-top-20 btn-primary unlock" @click="unlockContent(article.aid)">
                {{ article.pay_info.price }} {{ $t('detail.qb_unlock') }}
              </button>
            </div>
          </div>

          <div v-if="article.has_poll" class="article-votes">
            <p class="fs-md article-votes-title">{{ article.poll.title }}</p>

            <!-- 投票选项 -->
            <div class="article-votes-options">
              <div v-for="(item, index) in article.poll.options" :key="index" class="vote-options flex">
                <div class="options-item-radio flex ai-end jc-center">
                  <div
                    :class="{ active: selectedVoteIdx[item.oid] }"
                    class="outter-radio flex ai-end jc-center btn"
                    @click="selectVoteOption(item.oid)"
                  >
                    <span class="radio bg-gray"></span>
                  </div>
                </div>
                <div class="options-item-progress flex flex-cols flex-1">
                  <div class="progress-title flex ai-center flex-1">
                    <span class="title-name">{{ item.text }}</span>
                    <span class="text-gray">{{ item.votes }} {{ $t('detail.vote') }}</span>
                  </div>
                  <div class="progress-bar flex-1">
                    <!-- 进度条 -->
                    <p :style="{ width: `${item.percent}%` }" class="progress-bar-progress"></p>
                  </div>
                </div>
                <div class="options-item-counter flex jc-center ai-end">
                  <span>{{ item.percent }}%</span>
                </div>
              </div>
            </div>

            <!-- 投票按钮 -->
            <div class="article-votes-submit flex ai-center jc-center">
              <button class="article-votes-btn btn btn-primary" @click="castVote">{{ $t('detail.btn_vote') }}</button>
            </div>
          </div>

          <!-- 附件 | 连接 -->
          <div class="article-extra-file">
            <ul v-if="article.files">
              <li>
                <a
                  v-for="(item, key) in article.files"
                  :key="key"
                  :href="item.url"
                  :download="item.name"
                  target="_blank"
                  class="fs-mdbtn text-primary article-extra-file-item flex"
                >
                  <span class="text-primary">{{ $t('detail.download_file') }}:</span>
                  <span class="text-link link">{{ item.name }}</span>
                </a>
              </li>
            </ul>
          </div>

          <!-- 收藏 投币 点赞 分享 -->
          <div class="article-actions ai-center flex jc-between">
            <div class="article-actions-left flex jc-between ai-end">
              <!-- 收藏 -->
              <div class="article-actions-left-item hover flex" :class="{ active: article.already_fav }" @click="toggleCollection">
                <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 753.02 721.5">
                  <path
                    d="M406.49,42.18a53.94,53.94,0,0,0-49.3,30.64l-70.55,143a68.67,68.67,0,0,1-51.72,37.58L77.18,276.27A55,55,0,0,0,46.71,370L160.86,481.31a68.66,68.66,0,0,1,19.75,60.79l-27,157.12a55,55,0,0,0,79.77,58L374.53,683a68.73,68.73,0,0,1,63.92,0l141.1,74.18a55,55,0,0,0,79.77-57.95l-27-157.12a68.67,68.67,0,0,1,19.76-60.79L766.28,370a55,55,0,0,0-30.48-93.77L578.06,253.35a68.67,68.67,0,0,1-51.71-37.57l-70.56-143A53.94,53.94,0,0,0,406.49,42.18Z"
                    transform="translate(-29.98 -42.18)"
                  />
                </svg>
                <span class="num fs-ml flex ai-center">{{ article.favorites | num2short }}</span>
              </div>

              <!-- 投币 -->
              <div v-clickoutside="closeGiveCoins" class="article-actions-left-item action-coin btn flex" @click="showGiveCoins = !showGiveCoins">
                <svg :class="{ active: article.already_coin }" class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 755 752.97">
                  <path
                    d="M672.66,135.74C526.92-10,284-10,138.29,135.74s-145.73,388.63,0,534.37,388.63,145.73,534.37,0,151.13-388.63,0-534.37ZM494.54,497.38a575.12,575.12,0,0,0-56.78,66.09,38.2,38.2,0,0,1-61,1.11c-17.48-22.4-38-44.8-60.37-67.2-22.54-22.53-45.08-43.15-67.62-60.7-20.15-15.7-19.34-46.35,1.44-61.21a525.18,525.18,0,0,0,66.18-56.2,708.39,708.39,0,0,0,59.29-68.1,38.18,38.18,0,0,1,61.18.82,574.75,574.75,0,0,0,57.65,67.28,575.15,575.15,0,0,0,65.18,56.11,38.21,38.21,0,0,1,0,61.48,415.9,415.9,0,0,0-65.23,60.52Zm0,0"
                    transform="translate(-28.99 -26.44)"
                  />
                </svg>
                <span class="num fs-ml flex ai-center">{{ article.coins | num2short }}</span>
                <div :class="{ show: showGiveCoins }" class="coins-container hide tail-arrow-top">
                  <div class="flex ai-center jc-between">
                    <!-- 10 -->
                    <img src="@/assets/images/coin10.png" class="give-coin-item" @click="giveCoins(10)" />
                    <!-- 30 -->
                    <img src="@/assets/images/coin30.png" class="give-coin-item" @click="giveCoins(30)" />
                    <!-- 50 -->
                    <img src="@/assets/images/coin50.png" class="give-coin-item" @click="giveCoins(50)" />
                  </div>
                  <p class="tips text-center text-gray fs-sm">{{ $t('detail.give_coin_number') }}</p>
                </div>
              </div>

              <!-- 点赞 -->
              <div class="article-actions-left-item btn flex" :class="{ active: article.already_like }" @click="zanArticle">
                <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 753.02 721.5">
                  <path
                    d="M178,267.55c-30.28,0-54.82,28.61-54.82,63.9v297.9c0,35.29,24.54,63.9,54.82,63.9s54.83-28.61,54.83-63.9V331.45C232.87,296.16,208.32,267.55,178,267.55Z"
                    transform="translate(-123.22 -75)"
                  />
                  <path
                    d="M632.53,288.62H474.17l39.23-96.08c16-39.12-6-117.54-59.07-117.54-34.22,0-64.5,17.17-74.81,42.41l-91.1,156.25a64.09,64.09,0,0,0-8.72,32.27V634.32c0,37.23,39.15,67.4,87.45,67.4h197c42,0,78.1-23,85.92-54.85l68.41-278.3C728.65,327.06,687.35,288.62,632.53,288.62Z"
                    transform="translate(-123.22 -75)"
                  />
                </svg>
                <span class="num fs-ml flex ai-center">{{ article.likes | num2short }}</span>
              </div>
            </div>
            <div class="social-share" data-disabled="google,tencent,diandian" data-source="http://baidu.com"></div>
          </div>
        </article>

        <!-- 评论区域 -->
        <section id="comments">
          <!-- 顶部分页 -->
          <div class="top-tips flex jc-between">
            <p class="fs-md">{{ $t('detail.all_comments') }} {{ article.comments }}</p>
            <div v-if="paginate" class="paginate">
              <el-pagination
                class="my-pagination"
                layout="prev, pager, next, jumper"
                :prev-text="$t('paginate.prev')"
                :next-text="$t('paginate.next')"
                :hide-on-single-page="true"
                :disabled="loadingComments"
                :current-page="paginate.cur"
                :total="paginate.count"
                :page-size="paginate.size"
                @current-change="commentsPageChange($event, true)"
              />
            </div>
          </div>

          <!-- 评论区域 -->
          <div class="comment-list-container">
            <!-- 输入评论 -->
            <div class="comment-list-item flex">
              <div v-if="isLogin" class="flex-1 hover" @click="toPage(`/profile/${loginUser.uid}`)">
                <UserAvatar :avatar-img="loginUser.avatar" />
              </div>
              <div v-else class="flex-1">
                <UserAvatar avatar-img="/images/avatar.png" />
              </div>
              <div class="comment-list-item-content">
                <CommentInput
                  ref="commentinput"
                  btn-box-bottom-pad="30px"
                  btn-box-top-pad="30px"
                  upload-url="/upload/discuss-image"
                  :islogin="isLogin"
                  @confirm="publishComment"
                />
              </div>
            </div>
            <!-- 开始: 热门评论列表  -->
            <Loading v-show="loadingComments" />
            <div v-if="comments.hots.length && !loadingComments">
              <div v-for="(comment, key) in comments.hots" :key="comment.time" class="comment-list-item flex">
                <div class="flex-1">
                  <UserAvatar class="hover" :avatar-img="comment.user_info.avatar" @click.native="toPage(`/profile/${comment.user_info.uid}`)" />
                </div>
                <div class="comment-list-item-content">
                  <!-- 评论内容 -->
                  <div class="content-item-pinglun" :data-id="`reply_${comment.tid}`">
                    <CommentItem
                      :floor-master="comment.user_info.uid === article.author.uid"
                      :username="comment.user_info.nickname"
                      :lv="comment.user_info.level.level"
                      :lv-name="comment.user_info.level.name"
                      :content="comment.content"
                      :date="comment.time | date2short"
                      :zans="comment.likes"
                      :zaned="comment.liked"
                      :show-delete="canDeleteDiscuss"
                      :medal="comment.user_info.medals ? comment.user_info.medals[0] : {}"
                      @reply="showCommentInput(key, comment.user_info.nickname, true, comment)"
                      @zan="zanComment(comment, key)"
                      @del="delDiscuss(comment.tid, 0, key)"
                    />

                    <!-- ------------------ 回复列表 ------------------- -->
                    <div v-if="comment.replies">
                      <!-- 已经展开 -->
                      <div v-if="openedReplyKey[comment.tid]">
                        <ul class="content-item-reply">
                          <li
                            v-for="(reply, index) in openedReplies[comment.tid].list"
                            :key="index"
                            class="mar-bottom-25"
                            :data-id="`reply_${reply.rid}`"
                          >
                            <ReplyItem
                              :floor-master="reply.user_info.uid === article.author.uid"
                              :avatar="reply.user_info.avatar"
                              :date="reply.time | date2short"
                              :author="reply.user_info.nickname"
                              :reply-name="replyRridFormatter(reply.r_user_info)"
                              :lv="reply.user_info.level.level"
                              :lv-name="reply.user_info.level.name"
                              :content="reply.content"
                              :show-delete="canDeleteDiscuss"
                              :medal="reply.user_info.medals ? reply.user_info.medals[0] : {}"
                              @reply="showCommentInput(key, reply.user_info.nickname, false, { reply, comment })"
                              @click="toPage(`/profile/${reply.user_info.uid}`)"
                              @del="delDiscuss(comment.tid, reply.rid, key, index)"
                            />
                          </li>
                        </ul>
                        <el-pagination
                          layout="prev, pager, next, jumper"
                          :prev-text="$t('paginate.prev')"
                          :next-text="$t('paginate.next')"
                          :disabled="repliesLoading"
                          :hide-on-single-page="true"
                          :current-page="openedReplies[comment.tid].page_info.cur"
                          :total="openedReplies[comment.tid].page_info.count"
                          :page-size="openedReplies[comment.tid].page_info.size"
                          @current-change="repliesPageChangeHandler(comment.tid, $event)"
                        />
                      </div>

                      <!-- 未展开 -->
                      <div v-else>
                        <ul class="content-item-reply">
                          <li
                            v-for="(reply, index) in comment.reply_list.slice(0, 3)"
                            :key="index"
                            class="mar-bottom-25"
                            :data-id="`reply_${reply.rid}`"
                          >
                            <ReplyItem
                              :floor-master="reply.user_info.uid === article.author.uid"
                              :avatar="reply.user_info.avatar"
                              :date="reply.time | date2short"
                              :author="reply.user_info.nickname"
                              :reply-name="replyRridFormatter(reply.r_user_info)"
                              :lv="reply.user_info.level.level"
                              :lv-name="reply.user_info.level.name"
                              :content="reply.content"
                              :show-delete="canDeleteDiscuss"
                              :medal="reply.user_info.medals ? reply.user_info.medals[0] : {}"
                              @reply="showCommentInput(key, reply.user_info.nickname, false, { reply, comment })"
                              @click="toPage(`/profile/${reply.user_info.uid}`)"
                              @del="delDiscuss(comment.tid, reply.rid, key, index)"
                            />
                          </li>
                        </ul>

                        <!-- 查看更多 -->
                        <button v-if="comment.replies > 3" class="show-more-replies btn fs-xs text-primary" @click="showMoreReplies(comment.tid)">
                          {{ $t('detail.show_more') }}
                        </button>
                      </div>
                    </div>

                    <!-- ------------------ 回复列表 ------------------- -->
                  </div>
                  <!-- 底部回复输入框 -->
                  <div v-if="showCommentInputIndex === key" class="comment-reply-input">
                    <CommentInput
                      btn-box-bottom-pad="15px"
                      btn-box-top-pad="15px"
                      :show-image-btn="false"
                      :islogin="isLogin"
                      :pleaseholder="showCommentPleaseholder"
                      @confirm="publishReply"
                    />
                  </div>
                  <div :class="key === comments.hots.length - 1 ? 'have-hotline' : 'no-hotline'">
                    <span>{{ $t('detail.last_is_host') }}</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- 开始: 评论列表  -->
            <Loading v-show="loadingComments" />
            <div v-if="comments.list.length && !loadingComments">
              <div v-for="(comment, key) in comments.list" :key="comment.time" class="comment-list-item flex">
                <div class="flex-1">
                  <UserAvatar class="hover" :avatar-img="comment.user_info.avatar" @click.native="toPage(`/profile/${comment.user_info.uid}`)" />
                </div>
                <div class="comment-list-item-content">
                  <!-- 评论内容 -->
                  <div class="content-item-pinglun" :data-id="`reply_${comment.tid}`">
                    <CommentItem
                      :floor-master="comment.user_info.uid === article.author.uid"
                      :username="comment.user_info.nickname"
                      :lv="comment.user_info.level.level"
                      :lv-name="comment.user_info.level.name"
                      :content="comment.content"
                      :date="comment.time | date2short"
                      :zans="comment.likes"
                      :zaned="comment.liked"
                      :show-delete="canDeleteDiscuss"
                      :medal="comment.user_info.medals ? comment.user_info.medals[0] : {}"
                      @reply="showCommentInput(key, comment.user_info.nickname, true, comment)"
                      @zan="zanComment(comment, key)"
                      @del="delDiscuss(comment.tid, 0, key)"
                    />

                    <!-- ------------------ 回复列表 ------------------- -->
                    <div v-if="comment.replies">
                      <!-- 已经展开 -->
                      <div v-if="openedReplyKey[comment.tid]">
                        <ul class="content-item-reply">
                          <li
                            v-for="(reply, index) in openedReplies[comment.tid].list"
                            :key="index"
                            class="mar-bottom-25"
                            :data-id="`reply_${reply.rid}`"
                          >
                            <ReplyItem
                              :floor-master="reply.user_info.uid === article.author.uid"
                              :avatar="reply.user_info.avatar"
                              :date="reply.time | date2short"
                              :author="reply.user_info.nickname"
                              :reply-name="replyRridFormatter(reply.r_user_info)"
                              :lv="reply.user_info.level.level"
                              :lv-name="reply.user_info.level.name"
                              :content="reply.content"
                              :show-delete="canDeleteDiscuss"
                              :medal="reply.user_info.medals ? reply.user_info.medals[0] : {}"
                              @reply="showCommentInput(key, reply.user_info.nickname, false, { reply, comment })"
                              @click="toPage(`/profile/${reply.user_info.uid}`)"
                              @del="delDiscuss(comment.tid, reply.rid, key, index)"
                            />
                          </li>
                        </ul>
                        <el-pagination
                          layout="prev, pager, next, jumper"
                          :prev-text="$t('paginate.prev')"
                          :next-text="$t('paginate.next')"
                          :disabled="repliesLoading"
                          :hide-on-single-page="true"
                          :current-page="openedReplies[comment.tid].page_info.cur"
                          :total="openedReplies[comment.tid].page_info.count"
                          :page-size="openedReplies[comment.tid].page_info.size"
                          @current-change="repliesPageChangeHandler(comment.tid, $event)"
                        />
                      </div>

                      <!-- 未展开 -->
                      <div v-else>
                        <ul class="content-item-reply">
                          <li
                            v-for="(reply, index) in comment.reply_list.slice(0, 3)"
                            :key="index"
                            class="mar-bottom-25"
                            :data-id="`reply_${reply.rid}`"
                          >
                            <ReplyItem
                              :floor-master="reply.user_info.uid === article.author.uid"
                              :avatar="reply.user_info.avatar"
                              :date="reply.time | date2short"
                              :author="reply.user_info.nickname"
                              :reply-name="replyRridFormatter(reply.r_user_info)"
                              :lv="reply.user_info.level.level"
                              :lv-name="reply.user_info.level.name"
                              :content="reply.content"
                              :show-delete="canDeleteDiscuss"
                              :medal="reply.user_info.medals ? reply.user_info.medals[0] : {}"
                              @reply="showCommentInput(key, reply.user_info.nickname, false, { reply, comment })"
                              @click="toPage(`/profile/${reply.user_info.uid}`)"
                              @del="delDiscuss(comment.tid, reply.rid, key, index)"
                            />
                          </li>
                        </ul>

                        <!-- 查看更多 -->
                        <button v-if="comment.replies > 3" class="show-more-replies btn fs-xs text-primary" @click="showMoreReplies(comment.tid)">
                          {{ $t('detail.show_more') }}
                        </button>
                      </div>
                    </div>

                    <!-- ------------------ 回复列表 ------------------- -->
                  </div>
                  <!-- 底部回复输入框 -->
                  <div v-if="showCommentInputIndex === key" class="comment-reply-input">
                    <CommentInput
                      btn-box-bottom-pad="15px"
                      btn-box-top-pad="15px"
                      :show-image-btn="false"
                      :islogin="isLogin"
                      :pleaseholder="showCommentPleaseholder"
                      @confirm="publishReply"
                    />
                  </div>
                  <div class="no-hotline"></div>
                </div>
              </div>
            </div>
            <!-- 结束: 评论列表 -->
          </div>

          <!-- 底部分页 -->
          <div class="top-tips" style="height=200px">
            <div v-if="paginate" class="paginate">
              <el-pagination
                class="my-pagination"
                layout="prev, pager, next, jumper"
                :prev-text="$t('paginate.prev')"
                :next-text="$t('paginate.next')"
                :hide-on-single-page="true"
                :disabled="loadingComments"
                :current-page="paginate.cur"
                :total="paginate.count"
                :page-size="paginate.size"
                @current-change="commentsPageChange($event, true)"
              />
            </div>
          </div>
        </section>
      </div>

      <!-- ===== 右边 ===== -->
      <div class="right-recommend" :class="{ scroll: isscroll }">
        <!-- 作者 -->
        <div class="author flex jc-center flex-cols ai-center">
          <div class="hover" @click="toPage(`/profile/${article.author.uid}`)">
            <UserAvatar :avatar-img="article.author.avatar" />
          </div>
          <div class="author-name fs-sm">
            <span>{{ article.author.nickname }}</span>
            <span class="lv-icon mar-left-5" :class="[`lv-${article.author.level.level}`]">{{ article.author.level.name }}</span>
            <!-- <el-image
              v-for="item in medals"
              :key="item.medal_id"
              :src="item.img"
              :title="item.name"
              fit="contain"
              class="user-medal-image mar-left-5"
            ></el-image> -->
          </div>
          <div class="author-signature fs-xs text-gray">
            <span>{{ article.author.sign }}</span>
          </div>
          <!-- 勋章 -->
          <div v-if="article.author.medals.length > 0" class="item flex-inline ai-center author-medals">
            <el-image
              v-for="item in article.author.medals"
              :key="item.medal_id"
              :src="item.img"
              lazy
              fit="contain"
              class="item-medal-image mar-right-5"
              :title="item.name"
            ></el-image>
          </div>
          <div class="author-infos flex jc-center ai-center">
            <div class="author-infos-item hover" @click="toFansPage(article.author.uid, true)">
              <span class="fs-sd text-primary">{{ article.author.followers | num2short }}</span>
              <span class="author-infos-item-text fs-sm">{{ $t('detail.fans') }}</span>
            </div>
            <div class="author-infos-item hover" @click="toFansPage(article.author.uid, false)">
              <span class="fs-sd text-primary">{{ article.author.following | num2short }}</span>
              <span class="author-infos-item-text fs-sm">{{ $t('detail.follow') }}</span>
            </div>
            <div class="author-infos-item hover" @click="toPage(`/profile/${article.author.uid}`)">
              <span class="fs-sd text-primary">{{ article.author.articles | num2short }}</span>
              <span class="author-infos-item-text fs-sm">{{ $t('detail.articles') }}</span>
            </div>
          </div>

          <!-- 已经关注: 点击就取消关注 -->
          <button v-if="article.already_follow" class="author-follow-btn bg-gray btn btn-gray" @click="toggleFollow(1)">
            {{ $t('detail.btn_unfollow') }}
          </button>
          <!-- 未关注: 点击就关注 -->
          <button v-else class="author-follow-btn btn btn-primary" @click="toggleFollow(0)">{{ $t('detail.btn_follow') }}</button>
        </div>
        <!-- 其他推荐文章 -->
        <div v-if="otherRecoms.length" class="other-recommends">
          <!-- "其他推荐" 标题 -->
          <div class="fs-ml flex jc-between other-recommends-title">
            <h3>{{ $t('detail.other_recommends') }}</h3>
            <!-- icons -->
            <!-- <div class="icons-text-img-container flex jc-between">
              <svg
                :class="{ active: detail_view_index === 1 }"
                class="hover text-icon svg-icon"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 758.4 549"
                @click="viewChange(0)"
              >
                <circle cx="53.6" cy="53.6" r="53.6" />
                <path d="M729,228.8H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
                <circle cx="53.6" cy="274.5" r="53.6" />
                <circle cx="53.6" cy="495.4" r="53.6" />
                <path d="M729,449.7H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
                <path d="M729,670.6H247.5a49.8,49.8,0,0,1,0-99.6H729a49.8,49.8,0,0,1,0,99.6Z" transform="translate(-20.4 -125.4)" />
              </svg>
              <svg
                :class="{ active: detail_view_index === 1 }"
                class="hover img-icon svg-icon"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 551.5 547"
                @click="viewChange(1)"
              >
                <path
                  d="M303.8,126.9H181.2a57,57,0,0,0-57,57V306.6a57,57,0,0,0,57,57H303.9a57,57,0,0,0,57-57V183.9A57.12,57.12,0,0,0,303.8,126.9Z"
                  transform="translate(-124.2 -126.9)"
                />
                <path
                  d="M618.7,126.9H496a57,57,0,0,0-57,57V306.6a57,57,0,0,0,57,57H618.7a57,57,0,0,0,57-57V183.9A57,57,0,0,0,618.7,126.9Z"
                  transform="translate(-124.2 -126.9)"
                />
                <path
                  d="M303.8,437.2H181.2a57,57,0,0,0-57,57V616.9a57,57,0,0,0,57,57H303.9a57,57,0,0,0,57-57V494.2A57.12,57.12,0,0,0,303.8,437.2Z"
                  transform="translate(-124.2 -126.9)"
                />
                <path
                  d="M618.7,437.2H496a57,57,0,0,0-57,57V616.9a57,57,0,0,0,57,57H618.7a57,57,0,0,0,57-57V494.2A57,57,0,0,0,618.7,437.2Z"
                  transform="translate(-124.2 -126.9)"
                />
              </svg>
            </div> -->
            <!-- 按钮 -->
            <div v-if="isShow" class="btn-box">
              <button @click="toUpPage(otherRecoms, article.aid)">
                <span>&lt;</span>
                <span>{{ $t('detail.last_page') }}</span>
              </button>
              <button @click="toNextPage(otherRecoms, article.aid)">
                <span>{{ $t('detail.next_page') }}</span>
                <span>&gt;</span>
              </button>
            </div>
          </div>

          <div class="other-recommends-article">
            <!-- 文字: 倒序排序 reverse -->
            <div v-if="detail_view_index === 0">
              <div
                v-for="(item, i) in otherRecoms"
                :key="item.aid"
                class="other-recommends-article-item"
                :class="{ active: item.aid === article.aid }"
              >
                <TextRecommendItem
                  :title="item.title"
                  :views="item.hits"
                  :comments="item.comments"
                  :pindex="otherRecoms.length - i"
                  @click="toPage(`/detail/${item.aid}`)"
                />
              </div>
            </div>

            <!-- 图文 -->
            <!-- <div v-if="detail_view_index === 1">
              <div
                v-for="(item, i) in otherRecoms"
                :key="item.aid"
                class="other-recommends-article-item"
                :class="{ active: item.aid === article.aid }"
              >
                <recommendItem
                  :vertical="item.cover_type === 1"
                  :banner="item.cover"
                  :title="item.title"
                  :views="item.hits"
                  :comments="item.comments"
                  :pindex="otherRecoms.length - i"
                  @click="toPage(`/detail/${item.aid}`)"
                />
              </div>
            </div> -->
            <button class="btn btn-block show-more-btn" @click="toPage(`/series/${article.sid}`)">{{ $t('detail.show_more') }}</button>
          </div>
        </div>
        <!-- 其他推荐 -->

        <!-- 广告位置 -->
        <div v-if="ad.length" class="poster">
          <div v-for="(item, index) in ad" :key="index" class="poster-box">
            <el-image :src="item.pic_url" lazy fit="cover" @click="doRecomAction(item)"></el-image>
          </div>
        </div>
      </div>
    </div>

    <!-- 没有权限 -->
    <div v-else class="access-permission-denied-container flex jc-center ai-center">
      <div class="flex flex-cols">
        <div class="img-box1">
          <img src="@/assets/images/401_2.jpg" />
        </div>
        <div class="flex">
          <div class="img-box">
            <img src="@/assets/images/401_1.png" />
          </div>
          <ul>
            <li>{{ $t('write.401-con1') }}</li>
            <li>{{ $t('write.401-con2') }}</li>
            <li>{{ $t('write.401-con3') }}</li>
          </ul>
        </div>
        <div class="return-last" @click="toClosePage()"><span>&lt;</span> {{ $t('components.close_btn') }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
// import recommendItem from '@/components/RecommendItem.vue';
import TextRecommendItem from '@/components/TextRecommendItem.vue';
import CommentInput from '@/components/CommentInput.vue';
import UserAvatar from '@/components/UserAvatar.vue';
import CommentItem from '@/components/CommentItem.vue';
import ReplyItem from '@/components/ReplyItem.vue';
import Loading from '@/components/Loading.vue';
import storage from '@/plugins/mindLocalStorage.js';
export default {
  layout: 'detail',
  components: {
    Loading,
    UserAvatar,
    CommentItem,
    ReplyItem,
    CommentInput,
    // recommendItem,
    TextRecommendItem
  },

  async fetch() {
    await this.$store.dispatch('emoji/getEmojis');
  },

  // 请求数据
  async asyncData({ params, $axios, store, redirect, query }) {
    const data = {};
    const aid = params.id;
    const getDetail = $axios.$post('/api/article/get-detail', {
      aid,
      simple: 0
    });

    const getComments = $axios.$post('/api/discuss/get-topic', {
      aid,
      page: 1
    });

    const [articleRes, commentRes] = await Promise.all([getDetail, getComments]);

    if (articleRes.code !== 0) {
      return redirect('/not_found');
    } else {
      data.article = articleRes.data;
      data.article.content = data.article.content.replace(/<img/g, "<img loading='lazy'");
      if (data.article.author.medals.length > 0 && data.article.author.medals.length >= 5) {
        data.article.author.medals = data.article.author.medals.slice(0, 5);
      }
    }

    if (commentRes.code === 0) {
      data.comments = commentRes.data;
      data.paginate = commentRes.data.page_info || false;
    }

    // 如果有合集
    if (articleRes.code === 0 && articleRes.data.sid) {
      data.sid = articleRes.data.sid;
      const res = await $axios.$post('/api/series/get-article-list', {
        sid: articleRes.data.sid,
        aid: params.id,
        pageSize: 6
      });

      res.code === 0 && (data.otherRecoms = res.data.reverse());
    }

    const ad = await $axios.$post('/api/recom/get-recommends', {
      class: 4
    });
    if (query.tid) {
      const commentAddress = await $axios.$post('/api/discuss/get-discuss-page', {
        aid,
        rid: query.rid || '',
        tid: query.tid || ''
      });
      data.commentAddress = commentAddress.code === 0 ? commentAddress.data : {};
    }
    data.ad = ad.code === 0 && ad.data.length > 0 ? ad.data[0].items : [];
    return data;
  },

  data: () => ({
    sid: 0, // 合集id，如果有
    isHotline: false, // 是否有热门下划线
    isscroll: false, // 是否添加滚动效果
    casted: false, // 是否已经投过票
    repliesLoading: false, // 回复loading
    openedReplyKey: {}, // 已经展开的评论的回复列表key
    openedReplies: {}, // 已经展开的评论的回复列表
    loadingComments: false, // 是否在加载
    otherRecoms: [], // 合集其他帖子
    paginate: {},
    article: {
      author: { level: {}, medals: [] },
      already_follow: false, // 是否关注作者
      already_fav: false, // 是否收藏本文章
      already_coin: false, // 是否投币
      favorites: 0, // 收藏数量
      coins: 0, // 投币数量
      only_passer: 0, // 是否正式会员可见
      only_app: 0 // 是否仅APP可见
    },
    comments: { list: [], hots: [] },
    headShowGiveCoins: false, // 头部的是否显示投币
    showShare: false,
    showGiveCoins: false, // 是否显示投币
    showArticleLock: false, // 是否显示文章锁定
    showCommentPleaseholder: '', // 显示的回复输入框的pleaseholder
    showCommentInputIndex: -1, // 当前评论的回复输入框显示的位置
    commentContent: '', // 评论内容
    commentImgs: [], // 是否回复显示预览图片
    loginStatus: false, // 是否登录
    showEmojiPanel: false, // 是否显示表情面板
    selectedVoteIdx: {}, // 被选中的投票选项的index
    canDeleteDiscuss: false, // 是否显示删除评论按钮
    ad: [],
    commentAddress: {}, // 当前评论的页数
    timer: ''
    // client: {}, // webtorrent对象
    // size: null, // webtorrent尺寸
    // isWebTorrent: false // 是否有webtorrent
  }),

  computed: {
    ...mapState('login', ['isLogin', 'loginUser']),
    ...mapState('viewTab', ['detail_view_index']),
    isShow() {
      if (this.otherRecoms.length === 0) {
        return false;
      } else {
        return true;
      }
    },
    hasAccessPermission() {
      // 仅APP可见的不能访问
      if (this.article.only_app) {
        return false;
      }

      // 没有设置权限所有人可以访问
      if (!this.article.only_passer) {
        return true;
      }

      // 设置了权限, 没有登录不能访问
      if (!this.isLogin) {
        return false;
      }

      // 设置了权限, 但是作者是自己或者登录的人是会员, 可以访问
      if (this.loginUser.uid === this.article.author.uid || this.loginUser.passer) {
        return true;
      }
      return false;
    },
    medals() {
      return this.article.author.medals.length > 0 ? this.article.author.medals.slice(0, 1) : [];
    },
    classSize() {
      switch (this.size) {
        case 's':
          return { small: true };
        case 'm':
          return { mid: true };
        case 'l':
          return { big: true };
        default:
          return false;
      }
    }
  },

  watch: {
    isLogin(val) {
      val && this.addHistory();
      // 判断已登录的用户是否允许删除评论
      if (val) {
        if (this.article.operations && this.article.operations.delete_discuss === 1) {
          this.canDeleteDiscuss = true;
        } else {
          this.canDeleteDiscuss = false;
        }
      } else {
        this.canDeleteDiscuss = false;
      }
    }
  },
  async mounted() {
    // 缓存合集记录，如果大于50条则删除前10条
    if (storage.get('last_read') && storage.get('last_read').length > 50) {
      const lastRead1 = storage.get('last_read');
      lastRead1.splice(0, 10);
      storage.set('last_read', lastRead1);
    }
    // 缓存合集记录
    this.toCache();
    // 处理热区评论数据
    if (this.comments.hots && this.comments.hots.length > 3) {
      this.comments.hots = this.comments.hots.slice(0, 3);
    }
    // 监听滚动条
    window.addEventListener('scroll', this.windowScroll);
    if (this.commentAddress.topic) {
      await this.commentsPageChange(this.commentAddress.topic.page);
      (await this.commentAddress.reply) && this.showMoreReplies(this.$route.query.tid, this.commentAddress.reply.page);
      this.getLocal();
    }
    // 1.增加访问记录
    if (process.browser) {
      this.$store.dispatch('emoji/getEmojis');
      this.isLogin && this.initVotedOptions(this.article.poll);
      this.initContentLinks();
      // 复制添加乱代码
      // document.addEventListener('copy', (item) => {
      //   let copyInfo = '';
      //   try {
      //     copyInfo = document.selection.createRange().text;
      //   } catch (ex) {
      //     copyInfo = window.getSelection().toString();
      //   }
      //   copyInfo = this.fuck(copyInfo, 10);
      //   let clipboardData = window.clipboardData;
      //   if (!clipboardData) {
      //     clipboardData = item.clipboardData;
      //   }
      //   clipboardData.setData('Text', copyInfo);

      //   item.preventDefault();
      //   return true;
      // });
      // this.initWebtorrent();
      this.$nextTick(() => {
        this.initViewPosition();
        this.isLogin && this.addHistory();
      });
    }
  },

  // 组件销毁前去掉监听
  beforeDestroy() {
    window.removeEventListener('scroll', this.windowScroll);
  },

  methods: {
    /* eslint-disable */
    // fuck(str, desc) {
    //   const words = 'ƒé›†å›¢¸Šæ,µ·d,uå»ºå·¥æŠ•,èµæµ·å»ºåˆæ,Š•èµçéªŒè¯ç,æ˜¯å¦ç›¸,åç¦å»º,æ¯”è¾ƒè¾“å…¥çš„éduªŒ,è¯ç ,å’Œå®žé™å®½å®¢ƒ,é›†å›¢¸Šæµ·duå»ºå·¥æ,Š•èµ„æµ·å»ºåˆæŠ•èµ„ç®¡,ç†å»ºä¿¡åŸºé‡‘ç®¡ç†æœ‰é™è´£ä»»å…¬åMè,µ„æœ¬æœ›æ­£è,µ,äº§ç›¸èšèµ„æœ¬å,† é¡,¶èµ„æœ'.split(
    //     ','
    //   );
    //   let result = '';
    //   for (let i = 0; i < str.length; i += desc) {
    //     const rand = words[Math.floor(Math.random() * words.length)];
    //     result += str.substring(i, i + desc);
    //     if (i + desc <= str.length) {
    //       result += rand;
    //     }
    //   }
    //   return result;
    // },
    // 渲染磁力链视频出来,如果有
    // initWebtorrent() {
    //   // console.log(size, 'size');
    //   // console.log(manageUrl[1], 'manageUrl');
    //   const that = this;
    //   const pattern = /\[wt[^>]*]([\s\S]*?)\[\/wt]/;
    //   const manageUrl = that.article.content.match(pattern);
    //   if (!manageUrl) {
    //     return;
    //   }
    //   const client = new window.WebTorrent();
    //   const size = manageUrl[0].substring(9, 10);
    //   // 去掉标签
    //   this.article.content = this.article.content.replace(/\[wt[^>]*]([\s\S]*?)\[\/wt]/gi, '');
    //   if (manageUrl) {
    //     that.isWebTorrent = true;
    //     // 无论能否加载成功，loading应该去掉
    //     setTimeout(() => {
    //       that.isWebTorrent = false;
    //     }, 10000);
    //     client.add(
    //       `${manageUrl[1]}&tr=http%3A%2F%2Ftr.bangumi.moe%3A6969%2Fannounce&tr=http%3A%2F%2Ft.nyaatracker.com%2Fannounce&tr=http%3A%2F%2Fopen.acgtracker.com%3A1096%2Fannounce&tr=http%3A%2F%2Fopen.nyaatorrents.info%3A6544%2Fannounce&tr=http%3A%2F%2Ft2.popgo.org%3A7456%2Fannonce&tr=http%3A%2F%2Fshare.camoe.cn%3A8080%2Fannounce&tr=http%3A%2F%2Fopentracker.acgnx.se%2Fannounce&tr=http%3A%2F%2Ftracker.acgnx.se%2Fannounce&tr=http%3A%2F%2Fnyaa.tracker.wf%3A7777%2Fannounce&tr=https%3A%2F%2Ftr.bangumi.moe%3A9696%2Fannounce&tr=udp%3A%2F%2Ftr.bangumi.moe%3A6969%2Fannounce&tr=http%3A%2F%2Ft.acg.rip%3A6699%2Fannounce&tr=udp%3A%2F%2Ftracker.openbittorrent.com%3A80%2Fannounce&tr=udp%3A%2F%2Ftracker.publicbt.com%3A80%2Fannounce&tr=udp%3A%2F%2Ftracker.prq.to%3A80%2Fannounce&tr=udp%3A%2F%2F104.238.198.186%3A8000%2Fannounce&tr=http%3A%2F%2F104.238.198.186%3A8000%2Fannounce&tr=http%3A%2F%2F94.228.192.98%2Fannounce&tr=http%3A%2F%2Fshare.dmhy.org%2Fannonuce&tr=http%3A%2F%2Ftracker.btcake.com%2Fannounce&tr=http%3A%2F%2Ftracker.ktxp.com%3A6868%2Fannounce&tr=http%3A%2F%2Ftracker.ktxp.com%3A7070%2Fannounce&tr=http%3A%2F%2Fbt.sc-ol.com%3A2710%2Fannounce&tr=http%3A%2F%2Fbtfile.sdo.com%3A6961%2Fannounce&tr=https%3A%2F%2Ft-115.rhcloud.com%2Fonly_for_ylbud&tr=http%3A%2F%2Fexodus.desync.com%3A6969%2Fannounce&tr=udp%3A%2F%2Fcoppersurfer.tk%3A6969%2Fannounce&tr=http%3A%2F%2Ftracker3.torrentino.com%2Fannounce&tr=http%3A%2F%2Ftracker2.torrentino.com%2Fannounce&tr=udp%3A%2F%2Fopen.demonii.com%3A1337%2Fannounce&tr=udp%3A%2F%2Ftracker.ex.ua%3A80%2Fannounce&tr=http%3A%2F%2Fpubt.net%3A2710%2Fannounce&tr=http%3A%2F%2Ftracker.tfile.me%2Fannounce&tr=http%3A%2F%2Fbigfoot1942.sektori.org%3A6969%2Fannounce&tr=udp%3A%2F%2Fbt.sc-ol.com%3A2710%2Fannounce&tr=wss%3A%2F%2Ftracker.btorrent.xyz&tr=wss%3A%2F%2Ftracker.openwebtorrent.com`,
    //       (torrent) => {
    //         // console.log(torrent);
    //         torrent.files.forEach((file) => {
    //           // append file
    //           if (
    //             file.name.endsWith('.mp4') ||
    //             file.name.endsWith('.mp3') ||
    //             file.name.endsWith('.mpeg') ||
    //             file.name.endsWith('.wma') ||
    //             file.name.endsWith('.aac') ||
    //             file.name.endsWith('.realaudio') ||
    //             file.name.endsWith('.flac')
    //           ) {
    //             that.size = size;
    //             that.isWebTorrent = false;
    //             file.appendTo('.log');
    //           }
    //         });
    //       }
    //     );
    //   }
    // },
    // 缓存合集记录
    toCache() {
      if (this.otherRecoms && this.otherRecoms.length > 0 && this.sid) {
        const detailItem = Number(this.$route.params.id);
        let lastRead = [{ seriesId: this.sid, detailId: detailItem }];
        if (storage.get('last_read') && storage.get('last_read').find((item) => item.seriesId === this.sid)) {
          lastRead = storage.get('last_read');
          const idx = lastRead.findIndex((item) => item.seriesId === this.sid);
          lastRead[idx].detailId = detailItem;
          storage.set('last_read', lastRead);
        } else if (storage.get('last_read')) {
          lastRead = storage.get('last_read');
          lastRead.push({ seriesId: this.sid, detailId: detailItem });
          storage.set('last_read', lastRead);
        } else {
          storage.set('last_read', lastRead);
        }
      }
    },
    // 繁体图片 changeTC切换到简体
    changeTC(val) {
      this.$zh_tran(val);
    },
    // 简体图片 changeCN切换到繁体
    changeCN(val) {
      this.$zh_tran(val);
    },
    // 去到对应评论或者回复的位置
    getLocal() {
      if (this.$route.query.tid && this.$route.query.rid) {
        this.timer = setInterval(() => {
          this.render();
        }, 1000);
      } else {
        this.timer = setInterval(() => {
          this.render1();
        }, 1000);
      }
    },
    render() {
      try {
        const comment1 = document.querySelector(`[data-id='reply_${this.$route.query.tid}']`);
        const comment3 = comment1.querySelector(`[data-id='reply_${this.$route.query.rid}']`);
        if (comment3) {
          comment3.scrollIntoView();
          clearInterval(this.timer);
        }
      } catch {}
    },
    render1() {
      try {
        const comment = document.querySelector(`[data-id='reply_${this.$route.query.tid}']`);
        if (comment) {
          comment.scrollIntoView();
          clearInterval(this.timer);
        }
      } catch {}
    },
    // 关闭
    toClosePage() {
      window.close();
    },
    // 前往上一帖
    toUpPage(otherRecoms, aid) {
      otherRecoms.forEach((el, idx) => {
        if (el.aid === aid) {
          if (idx !== otherRecoms.length - 1) {
            this.toPage(`/detail/${otherRecoms[idx + 1].aid}`);
          }
        }
      });
    },
    // 前往下一帖
    toNextPage(otherRecoms, aid) {
      otherRecoms.forEach((el, idx) => {
        if (el.aid === aid) {
          if (idx !== 0) {
            this.toPage(`/detail/${otherRecoms[idx - 1].aid}`);
          }
        }
      });
    },
    windowScroll() {
      // 滚动条距离页面顶部的距离
      // 以下写法原生兼容
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      if (scrollTop >= 42) {
        this.isscroll = true;
      } else {
        this.isscroll = false;
      }
    },
    // 切换其他推荐
    viewChange(index) {
      this.$store.commit('viewTab/setViewIndex', {
        key: 'detail_view_index',
        val: index
      });
    },

    // 格式化回复
    replyRridFormatter(ruserInfo) {
      return ruserInfo ? ruserInfo.nickname : '';
    },

    // 点赞评论
    zanComment({ tid, liked }, index) {
      this.loginNext(() => {
        const curCmt = this.comments.list[index];
        if (curCmt.liked || this.zaning) return;
        this.zaning = true; // 点赞中..
        this.$axios
          .$post('/api/discuss/like', { tid })
          .then((res) => {
            if (res.code === 0) {
              curCmt.likes++;
              curCmt.liked = true;
            }
          })
          .finally(() => (this.zaning = false));
      });
    },

    // 点赞文章
    zanArticle() {
      this.loginNext(() => {
        if (this.zanArting || this.article.already_like) return;
        this.zanArting = true; // 正在点赞
        this.$axios
          .$post('/api/article/like', { aid: this.article.aid })
          .then((res) => {
            if (res.code === 0) {
              this.article.likes++;
              this.article.already_like = true;
            }
          })
          .finally(() => (this.zanArting = false));
      });
    },

    // 如果有 #锚点 就跳到指定位置
    initViewPosition() {
      const anchor = this.$route.hash && document.querySelector(this.$route.hash);
      anchor && anchor.scrollIntoView();
    },

    // 初始化文章内容中的 a 标签, 所有a标签都在新标签页打开
    initContentLinks() {
      const aNodes = document.querySelectorAll('#article-main-contents a');
      aNodes.length && Array.from(aNodes).forEach((item) => (item.target = '_blank'));
    },

    // 初始化被选中的投票选项
    initVotedOptions(poll) {
      if (this.article.has_poll && poll.voted && poll.voted.length) {
        poll.voted.forEach((item) => (this.selectedVoteIdx[item] = item));
      }
    },

    // 查看更多的评论
    showMoreReplies(tid, idx = 1) {
      this.repliesPageChangeHandler(tid, idx, () => {
        this.$set(this.openedReplyKey, tid, true);
      });
    },

    // 回复页面切换
    repliesPageChangeHandler(tid, page, cb = null) {
      this.repliesLoading = true;
      this.$axios
        .$post('/api/discuss/get-reply', { page, tid })
        .then((res) => {
          if (res.code === 0) {
            this.$set(this.openedReplies, tid, {
              list: res.data.reply_list,
              page_info: res.data.page_info
            });
            typeof cb === 'function' && cb();
          } else {
            this.$message.error(this.$t('tips.get_success'));
          }
        })
        .finally(() => (this.repliesLoading = false));
    },

    // curChange
    async commentsPageChange(page, back = false) {
      const aid = this.article.aid;
      this.loadingComments = true;
      const res = await this.$axios.$post('/api/discuss/get-topic', {
        aid,
        page
      });
      if (res.code === 0) {
        this.loadingComments = false;
        this.comments = res.data;
        this.paginate = res.data.page_info;
        this.$nextTick(() => {
          back && document.querySelector('#comments').scrollIntoView();
        });
      }
    },

    // 切换收藏
    toggleCollection() {
      const payload = {
        _class: 1,
        fid: this.article.aid
      };

      if (this.article.already_fav) {
        // 取消收藏
        this.toggleFavoriteState(false, payload, (res) => {
          if (res) {
            this.article.favorites -= 1;
            this.article.already_fav = false;
          }
        });
      } else {
        // 收藏
        this.toggleFavoriteState(true, payload, (res) => {
          if (res) {
            this.article.favorites += 1;
            this.article.already_fav = true;
          }
        });
      }
    },

    // 刷新评论
    async refreshComments(aid, page = 1) {
      const res = await this.$axios.$post('/api/discuss/get-topic', {
        aid,
        page
      });
      if (res.code === 0) {
        this.comments = res.data;
      }
    },

    // 刷新回复(如果是展开的刷新展开页数据, 未展开 refreshComments)
    refreshReplies() {
      const { tid } = this.showCommentReplyIsComment ? this.showCommentReplyParams : this.showCommentReplyParams.reply;
      if (this.openedReplyKey[tid]) {
        this.repliesPageChangeHandler(tid, this.openedReplies[tid].cur);
      } else {
        this.refreshComments(this.article.aid, this.paginate.cur);
      }
    },

    // 文章投币(goods_id:  1:解锁文章 2:文章投币)
    giveCoins(num) {
      this.loginNext(async () => {
        const data = { params: this.article.aid };
        data.price = 1;
        data.total_price = num;
        data.number = num;
        data.goods_id = 2;
        const res = await this.$axios.$post('/api/coin/use', data);
        if (res.code === 0) {
          this.article.already_coin = true;
          this.article.coins += num;
          this.$store.commit('login/setCoins', { num });
          this.$message.success(this.$t('detail.give_coin_success'));
        }
      });
    },

    // 解锁文章内容(goods_id:  1:解锁文章 2:文章投币)
    unlockContent(aid) {
      this.loginNext(async () => {
        const data = { params: aid };
        const { price } = this.article.pay_info;
        data.goods_id = 1;
        data.price = price;
        data.total_price = price;
        data.number = 1;
        const res = await this.$axios.$post('/api/coin/use', data);
        res.code === 0 && window.location.reload(true);
      });
    },

    // 显示评论输入框
    showCommentInput(key, username, isComment, item) {
      this.loginNext(() => {
        this.showCommentInputIndex = key;
        this.showCommentReplyParams = item; // 当前显示评论回复输入框, 的额外参数
        this.showCommentReplyIsComment = true; // 当前显示评论回复输入框, 是否是评论
        this.showCommentPleaseholder = this.$t('detail.publish_replay');
        if (!isComment) {
          this.showCommentReplyIsComment = false;
          this.showCommentPleaseholder += `@${username}`;
        }
      });
    },

    // 发布回复
    async publishReply({ content }) {
      // 发布评论避让需要先调用: showCommentInput 所以,
      // 是回复评论还是回复评论的回复: showCommentReplyIsComment
      // 当前点击那一项的额外参数: showCommentReplyParams
      // 如果正在发送中就停止避免重复发布
      if (this.publishReplyReqing) return;
      if (!content.trim()) {
        this.$message.error(this.$t('detail.please_input_reply'));
        return;
      }
      const params = {
        content,
        aid: this.article.aid
      };
      // console.log(params);
      if (this.showCommentReplyIsComment) {
        // 回复评论:
        params.tid = this.showCommentReplyParams.tid;
      } else {
        // 回复评论的回复:
        params.tid = this.showCommentReplyParams.comment.tid;
        params.r_uid = this.showCommentReplyParams.reply.uid;
        params.r_rid = this.showCommentReplyParams.reply.rid;
      }

      this.publishReplyReqing = true; // 正在发送...
      const res = await this.$axios.$post('/api/discuss/post-reply', params);
      this.publishReplyReqing = false;
      if (res.code === 0) {
        this.showCommentInputIndex = -1;
        this.refreshReplies();
        this.$message.success(this.$t('detail.reply_success'));
      }
    },

    // 发布评论
    publishComment({ content, images }) {
      if (this.publishCommentReqing) {
        return;
      }
      this.loginNext(async () => {
        if (!content.trim()) {
          this.$message.error(this.$t('detail.please_input_comment'));
          return;
        }
        const params = {
          content
        };
        !this.isEmptyObject(images) && (params.images = images);
        params.aid = this.article.aid;
        this.publishCommentReqing = true; // 正在发送...
        const res = await this.$axios.$post('/api/discuss/post-topic', params);
        this.publishCommentReqing = false; // 正在发送...
        if (res.code === 0) {
          this.$message.success(this.$t('detail.reply_success'));
          this.refreshComments(this.article.aid);
          this.$refs.commentinput.clearContent();
        }
      });
    },

    // 增加访问记录
    addHistory() {
      this.$axios.post('/api/history/add-history', {
        fid: this.article.aid,
        class: 1
      });
    },

    // 选择投票选项
    selectVoteOption(id) {
      const index = id;
      const { multiple } = this.article.poll;
      // 单选
      if (!multiple) {
        this.selectedVoteIdx = { [index]: true };
        return;
      }

      // 多选
      const maxChooice = this.article.poll.max_choices;
      if (index in this.selectedVoteIdx) {
        this.selectedVoteIdx[index] = !this.selectedVoteIdx[index];
        return;
      }
      const selectedOptions = {};
      let selectedCount = 0;
      for (const key in this.selectedVoteIdx) {
        if (this.selectedVoteIdx[key]) {
          selectedOptions[key] = true;
          selectedCount++;
        }
      }
      this.selectedVoteIdx = selectedOptions;
      if (selectedCount < maxChooice) {
        this.$set(this.selectedVoteIdx, index, true);
      }
    },

    // 投票
    castVote() {
      this.loginNext(async () => {
        const poll = this.article.poll;
        if (poll.voted && poll.voted.length) {
          this.$message.error(this.$t('detail.already_cost'));
          return;
        }
        if (this.casting) {
          return;
        }
        const selected = Object.keys(this.selectedVoteIdx);
        if (selected.length === 0) {
          this.$message.info(this.$t('detail.least_one_item'));
          return;
        }
        this.casting = true;
        const res = await this.$axios
          .$post('/api/article/poll', {
            aid: this.article.aid,
            oids: selected
          })
          .finally(() => (this.casting = false));
        if (res.code === 0) {
          this.casted = true;
          this.$message.success(this.$t('detail.case_vote_success'));
        }
      });
    },

    // 关闭投币/头部投币/头部分享弹框
    closeGiveCoins() {
      this.showGiveCoins = false;
      this.headShowGiveCoins = false;
      this.showShare = false;
    },

    // 切换关注状态 act: 0：关注, 1：取关
    toggleFollow(act) {
      const uid = this.article.author.uid;
      this.toggleFollowState(uid, act, (flag) => {
        flag && (this.article.already_follow = !this.article.already_follow);
      });
    },

    // 删除评论
    delDiscuss(tid, rid, keyTopic, keyReply) {
      this.loginNext(() => {
        this.$confirm(this.$t('tips.are_you_sure'), this.$t('components.type_warn'), {
          confirmButtonText: this.$t('components.btn_confirm'),
          cancelButtonText: this.$t('components.btn_cancel'),
          type: 'warning'
        })
          .then(() => {
            // 如果是回复
            if (rid) {
              // 如果是已展开
              if (this.openedReplyKey[tid]) {
                this.openedReplies[tid].list.splice(keyReply, 1);
              } else {
                this.comments.list[keyTopic].reply_list.splice(keyReply, 1);
              }
            } else {
              // 去掉已经被删除的评论主题
              this.comments.list.splice(keyTopic, 1);
            }
            const url = rid ? '/api/discuss/del-reply' : '/api/discuss/del-topic';
            this.$axios.$post(url, { tid, rid }).then((res) => {
              // // 某人说不弹通知……
              // if (res.code === 0) {
              //   this.$message.success(this.$t('tips.delete_success'));
              // } else {
              //   this.$message.success(this.$t('tips.delete_fail'));
              // }
            });
          })
          .catch(Function.prototype);
      });
    }
  },

  head() {
    const title = this.article.title + '-' + this.$t('title');
    const baseKeywords = this.$t('base_metas.keywords');
    return {
      title,
      link: [{ rel: 'stylesheet', href: '/css/share.min.css' }],
      script: [{ src: '/js/social-share.min.js' }],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.article.summary
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `${this.article.title}, ${this.article.author.nickname}, ${baseKeywords}`
        },
        {
          hid: 'author',
          name: 'author',
          content: this.article.author.nickname
        }
      ]
    };
  }
};
</script>
<style lang="scss" scope>
@import '@/assets/scss/detail.scss';
.other-recommends-article-item {
  &.active {
    color: $primary;
  }
}
.access-permission-denied-container {
  img {
    width: 100%;
  }
  .return-last {
    cursor: pointer;
    position: absolute;
    color: #73e2e4;
    transform: translateX(-50%);
    font-size: 25px;
    bottom: 198px;
    left: 49%;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    color: #3ad8da;
    line-height: 51px;
  }
}
.author-name {
  display: flex;
  align-items: center;
  .user-medal-image {
    width: 20px;
    height: 20px;
  }
}
iframe {
  display: block;
  margin: 0 auto;
}
.log {
  margin: 0 auto;
  video {
    width: 100%;
    height: 100%;
  }
  audio {
    width: 100%;
    height: 100%;
  }
}
.big {
  width: 100%;
}
.mid {
  width: 65%;
}
.small {
  width: 45%;
}
</style>

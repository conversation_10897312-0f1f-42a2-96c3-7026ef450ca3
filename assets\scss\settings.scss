.form {
  width: 70%;
  padding: 30px 20px 0 20px;
  .form-item {
    margin-bottom: 30px;
    .label {
      display: inline-block;
      font-size: $sm;
      min-width: 80px;
      // background: blue;
    }
    .tips {
      color: $gray;
    }
    .checkbox {
      border: 1px solid $gray-white;
      padding: 5px;
      border-radius: 5px;
      &.selected {
        border-color: $primary;
        color: $primary;
      }
    }
    .btn-submit {
      width: 100px;
      height: 32px;
      border-radius: 32px;
    }
    .send-vcode {
      border-radius: 5px;
      padding: 5px 10px;
    }
  }

  .avatar-box-wrapper {
    .avatar-box {
      display: inline-flex;
      width: 100%;
      height: 120px;
      margin-bottom: 30px;
      .img-size {
        width: 120px;
        height: 100%;
        border-radius: 5px;
        overflow: hidden;
        border: 1px solid $gray-white;
      }
      .avatar-add-btn {
        background-color: $white-fa;
        .svg-icon {
          width: 30%;
          fill: $gray;
        }
      }
      // background: red;
    }
  }
}

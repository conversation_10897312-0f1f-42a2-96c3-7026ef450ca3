<template>
  <!-- 左侧: 侧边栏 -->
  <div class="sidebar">
    <!-- 头像 -->
    <div class="side-item user-avatar-box flex flex-cols ai-center jc-center">
      <user-avatar :avatar-img="avatar" :border-img="avatarBg" />
      <p class="username">
        <span class="fs-sm text-dark">{{ username }}</span>
        <span class="lv-icon fs-xs" :class="[`lv-${level}`]">{{ levelName }}</span>
      </p>
      <!--勋章部分开始-->
      <div v-if="medals.length > 0" class="user-medal-container">
        <el-row v-for="n in 2" :key="`medal-row-${n}`" :gutter="5" type="flex" justify="left">
          <el-col v-for="medal in medals.slice(5 * (n - 1), 5 * n)" :key="medal.id" class="user-medal-col">
            <div>
              <el-image :src="medal.img" lazy :title="medal.name" fit="contain" class="user-medal-image"></el-image>
            </div>
          </el-col>
        </el-row>
      </div>
      <!--勋章部分结束-->
      <button v-if="isMe" class="btn btn-primary" @click="$emit('btn-click', true)">
        <span>{{ $t('components.profile_side.update_infos') }}</span>
      </button>
      <button v-else class="btn btn-primary" @click="$emit('btn-click', false)">{{ btnText }}</button>
    </div>

    <p class="line"></p>

    <!-- 关注信息 -->
    <div class="side-item user-infos flex">
      <div class="infos-item flex-1 flex flex-cols jc-around ai-center hover" @click="toFansPage(uid)">
        <span class="text-primary fs-sm">{{ fans | num2short }}</span>
        <span class="text-gray fs-xs">{{ $t('components.profile_side.fans') }}</span>
      </div>
      <div class="infos-item flex-1 flex flex-cols jc-around ai-center hover" @click="toFansPage(uid, false)">
        <span class="text-primary fs-sm">{{ follows | num2short }}</span>
        <span class="text-gray fs-xs">{{ $t('components.profile_side.follow') }}</span>
      </div>
      <div class="infos-item flex-1 flex flex-cols jc-around ai-center hover" @click="toPage(`/profile/${uid}`)">
        <span class="text-primary fs-sm">{{ articles | num2short }}</span>
        <span class="text-gray fs-xs">{{ $t('components.profile_side.publish') }}</span>
      </div>
    </div>

    <p class="line"></p>

    <!-- 性别+签名 -->
    <div v-if="signature" class="side-item gender-sign">
      <p class="flex">
        <span class="text-gray label">{{ $t('components.profile_side.gender') }}:</span>
        <span>{{ gender }}</span>
      </p>
      <p class="flex">
        <span class="text-gray label">{{ $t('components.profile_side.signature') }}:</span>
        <span>{{ signature }}</span>
      </p>
    </div>

    <div class="side-item qrcode flex ai-center jc-center flex-cols">
      <img src="@/assets/images/qr_code.png" />
      <p class="fs-sm">{{ $t('components.profile_side.scan_download') }}</p>
    </div>
    <p class="line"></p>

    <!-- 广告位置 -->
    <div v-if="ad.length" class="poster">
      <div v-for="(item, index) in ad" :key="index" class="poster-box">
        <el-image :src="item.pic_url" lazy fit="cover" @click="doRecomAction(item)"></el-image>
      </div>
    </div>
  </div>
</template>

<script>
import UserAvatar from '@/components/UserAvatar.vue';
export default {
  components: {
    UserAvatar
  },
  props: {
    uid: {
      // 用户ID
      type: [Number, String],
      required: true
    },
    avatar: {
      // 头像
      type: String,
      required: true
    },
    isMe: {
      // 是否是我看自己的主页
      type: Boolean,
      required: false
    },
    avatarBg: {
      // 头像框
      type: String,
      default: ''
    },
    fans: {
      // 粉丝
      type: [String, Number],
      default: 0
    },
    follows: {
      // 关注
      type: [String, Number],
      default: 0
    },
    articles: {
      // 关注
      type: [String, Number],
      default: 0
    },
    username: {
      // 用户名
      type: [String, Number],
      default: ''
    },
    level: {
      // 等级
      type: [String, Number],
      required: true
    },
    levelName: {
      // 等级名称
      type: [String, Number],
      required: true
    },
    gender: {
      // 性别
      type: [String, Number],
      default: ''
    },
    signature: {
      // 是否显示签名
      type: [String, Number],
      default: ''
    },
    btnText: {
      // 按钮文字(只有不是看我自己的信息的时候,
      // 按钮上的位置才使用这个属性, 因为看自己就只能修改资料)
      type: String,
      default: ''
    },
    medals: {
      // 勋章
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    ad: []
  }),
  mounted() {
    this.getAd();
  },
  methods: {
    getAd() {
      this.$axios.$post('/api/recom/get-recommends', { class: 3 }).then((res) => {
        this.ad = res.code === 0 && res.data.length > 0 ? res.data[0].items : [];
      });
    }
  }
};
</script>

<style lang="scss" scope>
.sidebar {
  width: 240px;
  min-height: 670px;
  background: #fff;
  height: 670px;
  .line {
    height: 10px;
    background-color: $gray-f7;
  }
  .poster {
    background: $gray-f7;
    .poster-box {
      cursor: pointer;
      width: 240px;
      height: 145px;
      margin-bottom: 10px;
      .el-image {
        width: 100%;
        height: 100%;
        border-radius: 10px;
      }
    }
  }
  // 用户头像
  .user-avatar-box {
    padding-top: 15px;
    padding-bottom: 20px;
    .username {
      padding: 15px 0;
    }
    .btn {
      width: 90px;
      height: 30px;
      border-radius: 30px;
    }
  }
  // 用户信息
  .user-infos {
    padding: 15px;
    .infos-item {
      height: 50px;
    }
  }
  // 性别+签名
  .gender-sign {
    padding: 30px 20px;
    font-size: 13px !important;
    p {
      line-height: 2;
    }
    .label {
      display: inline-block;
      min-width: 40px;
    }
  }
  // 签名
  .qrcode {
    padding: 20px 0;
    img {
      width: 110px;
    }
    p {
      margin-top: 10px;
    }
  }

  /** 勋章部分开始 */
  .user-medal-container {
    width: 170px;
    padding-bottom: 10px;
    .user-medal-col {
      width: 20%;
      .user-medal-image {
        width: 30px;
        height: 30px;
        overflow: hidden;
      }
    }
  }
  /** 勋章部分结束 */
}
</style>

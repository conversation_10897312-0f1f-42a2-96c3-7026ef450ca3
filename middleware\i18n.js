/* eslint-disable */
export default function ({ isHMR, app, req, store, route, params, error, redirect }) {
  const defaultLocale = app.i18n.fallbackLocale;

  // 如果是热更新就不用管
  if (isHMR) {
    return;
  }

  // 设置默认语言(根据浏览器设置的语言: accept-language 请求头)
  let locale = params.lang || defaultLocale;
  if (!store.state.locales.includes(locale)) {
    return error({
      message: 'This page could not be found.',
      statusCode: 404
    });
  }

  // 判断浏览器发送请求的 headers 是否是 zh | zh-cn
  if (req && req.headers['accept-language']) {
    const reqLang = req.headers['accept-language'].split(',').shift();
    const curLang = reqLang.toLocaleLowerCase();
    if (['zh', 'zh-cn'].includes(curLang)) {
      locale = 'cn';
    }
  }

  // 设置语言
  store.commit('SET_LANG', locale);
  app.i18n.locale = store.state.locale;

  // 如果不是对应语言就重定向到该语言
  if (locale === defaultLocale && route.fullPath.indexOf('/' + defaultLocale) === 0) {
    const toReplace = '^/' + defaultLocale + (route.fullPath.indexOf('/' + defaultLocale + '/') === 0 ? '/' : '');
    const re = new RegExp(toReplace);
    return redirect(route.fullPath.replace(re, '/'));
  }
}

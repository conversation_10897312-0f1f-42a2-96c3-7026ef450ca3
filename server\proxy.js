import express from 'express';
const { createProxyMiddleware } = require('http-proxy-middleware');

const router = express.Router();

const app = express();

router.use(require('cors')());

// nuxt
router.use((req, res, next) => {
  Object.setPrototypeOf(req, app.request);
  Object.setPrototypeOf(res, app.response);
  req.res = res;
  res.req = req;
  next();
});

// proxy
const proxy = createProxyMiddleware({
  target: 'http://173.249.6.116:36015',
  // target: 'https://api.lightnovel.us',
  // target: 'https://api-dev-lvoq4mx4bnr5.lightnovel.us',
  secure: false,
  changeOrigin: true,
  pathRewrite: {
    '/proxy': '/'
  }
});

router.use('/proxy', proxy);

// Export the server middleware
export default {
  path: '/',
  handler: router
};

<template>
  <div>
    <!--弹窗开始-->
    <el-dialog
      :visible.sync="dialogVisible"
      :lock-scroll="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="65%"
      center
      top="30vh"
      @close="close"
    >
      <span class="download-modal-body">打开LK客户端，升级level!</span>
      <span slot="footer">
        <el-button round class="lk-primary-button" style="min-width: 60%;" @click="downloadApp">打开</el-button>
      </span>
    </el-dialog>
    <!--弹窗结束-->
  </div>
</template>

<script>
// const CallApp = process.browser ? require('callapp-lib') : undefined;

export default {
  name: 'DialogMobileTips',
  data() {
    return {
      dialogVisible: true,
      callLib: {},
      urlPath: ''
    };
  },
  mounted() {
    if (process.browser) {
      // const options = {
      //   scheme: {
      //     protocol: 'qingguo'
      //   },
      //   intent: {
      //     package: 'net.lk.qingguo',
      //     scheme: 'qingguo'
      //   },
      //   // universal: {
      //   //   host: 'www.lightnovel.us/mobile/',
      //   //   pathKey: 'action'
      //   // },
      //   appstore: 'https://apps.apple.com/cn/app/lk/id1495634862',
      //   fallback: 'https://www.lightnovel.us/mobile/LK_latest.apk',
      //   timeout: 2000
      // };
      // this.callLib = new CallApp(options);
      this.urlPath = window.location.pathname;
      if (this.urlPath.includes('cn/')) {
        this.urlPath = this.urlPath.replace('/cn/', '');
      }
      this.urlPath = encodeURI(window.btoa(this.urlPath));
      document.body.classList.add('dialog-mobile-tips-fixed'); // 添加样式修正手机弹窗的宽度错误
    }
  },
  methods: {
    // 下载App
    downloadApp() {
      window.open(`https://obsolete.lightnovel.us/app/?p=${this.urlPath}`, '_blank');
      // this.callLib.open({ path: '' });
      sessionStorage.setItem('close_mobile_tips', '1');
    },
    close() {
      this.closeDialogMobileDo();
      this.$emit('close');
    },

    // 关闭弹窗时移除样式并且在本次浏览器生命周期内不再弹窗
    closeDialogMobileDo() {
      document.body.classList.remove('dialog-mobile-tips-fixed');
      sessionStorage.setItem('close_mobile_tips', '1');
    }
  }
};
</script>

<style lang="scss" scope>
.download-modal-body {
  text-align: center !important;
  font-size: 1.2em;
}
.el-dialog {
  .lk-primary-button {
    font-size: 1em;
    &.is-round {
      padding: 0.72em 1em;
    }
  }
}
</style>

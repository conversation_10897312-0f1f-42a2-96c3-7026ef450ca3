const cookieparser = process.server ? require('cookieparser') : undefined;

export const state = () => ({
  locales: ['tc', 'cn'],
  locale: 'tc'
});

export const mutations = {
  SET_LANG(state, locale) {
    if (state.locales.includes(locale)) {
      state.locale = locale;
    }
  }
};

export const actions = {
  // nuxt 服务端启动时调用
  nuxtServerInit({ commit }, { req }) {
    let token = null;
    if (req.headers.cookie) {
      const parsed = cookieparser.parse(req.headers.cookie);
      try {
        token = JSON.parse(parsed.token);
        commit('login/setToken', token);
      } catch (err) {
        /* handle err */
        commit('login/setToken', null);
      }
    } else {
      commit('login/setToken', null);
    }
  }
};

// 我的收藏数据
export const state = () => ({
  fav_article_articles: [], // 我的收藏-帖子页-帖子
  fav_article_articles_paginate: {}, // 我的收藏-帖子页-帖子-分页

  fav_article_books: [], // 我的收藏-帖子页-图书
  fav_article_books_paginate: {}, // 我的收藏-帖子页-图书-分页

  fav_series_articles: [], // 我的收藏-合集页-帖子
  fav_series_articles_paginate: {}, // 我的收藏-合集页-帖子-分页

  fav_series_books: [], // 我的收藏-合集页-图书
  fav_series_books_paginate: {} // 我的收藏-合集页-图书-分页
});

export const mutations = {
  getFavArticleArticles(state, data) {
    state.fav_article_articles = data.list;
    state.fav_article_articles_paginate = data.page_info;
  },
  getFavArticleBooks(state, data) {
    state.fav_article_books = data.list;
    state.fav_article_books_paginate = data.page_info;
  },
  getFavSeriesArticles(state, data) {
    state.fav_series_articles = data.list;
    state.fav_series_articles_paginate = data.page_info;
  },
  getFavSeriesBooks(state, data) {
    state.fav_series_books = data.list;
    state.fav_series_books_paginate = data.page_info;
  },
  deleteFavorite(state, { idKey, params, dataKey }) {
    if (Array.isArray(params)) {
      // 一次性删除多个
      state[dataKey] = state[dataKey].filter((item) => !params.includes(item.aid));
    } else {
      state[dataKey] = state[dataKey].filter((item) => item[idKey] !== params);
    }
  }
};

export const actions = {
  // 删除收藏 dataInfo: {params, key, data}
  async deleteFavorite({ commit }, { fid, _class, dataKey, idKey }) {
    const res = await this.$axios.$post('/api/history/del-collection', {
      fid,
      class: _class
    });

    if (res.code === 0) {
      commit('deleteFavorite', { dataKey, idKey, params: fid });
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 我的收藏-帖子页-帖子
  async getFavArticleArticles({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/history/get-collections', {
      uid,
      page,
      type: 0,
      class: 1
    });
    if (res.code === 0) {
      commit('getFavArticleArticles', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 我的收藏-帖子页-图书
  async getFavArticleBooks({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/history/get-collections', {
      uid,
      page,
      type: 1,
      class: 1
    });
    if (res.code === 0) {
      commit('getFavArticleBooks', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 我的收藏-合集页-帖子
  async getFavSeriesArticles({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/history/get-collections', {
      uid,
      page,
      type: 0,
      class: 2
    });
    if (res.code === 0) {
      commit('getFavSeriesArticles', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 我的收藏-合集页-图书
  async getFavSeriesBooks({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/history/get-collections', {
      uid,
      page,
      type: 1,
      class: 2
    });
    if (res.code === 0) {
      commit('getFavSeriesBooks', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  }
};

export const getters = {
  fav_article_articles_10(state) {
    const books = state.fav_article_articles;
    return books.length ? books.slice(0, 10) : [];
  },
  fav_article_books_10(state) {
    const books = state.fav_article_books;
    return books.length ? books.slice(0, 10) : [];
  }
};

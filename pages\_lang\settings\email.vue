<template>
  <div>
    <SettingsTopTabbar />
    <div class="form">
      <!-- 当前邮箱 -->
      <div class="form-item flex ai-center">
        <span class="label">{{ $t('settings_email.current_email') }}</span>
        <input type="text" class="input-control" value="118****<EMAIL>" :disabled="true" />
      </div>

      <!-- 验证码 -->
      <div class="form-item flex ai-center">
        <span class="label">{{ $t('settings_email.vcode') }}</span>
        <input type="text" class="input-control flex-1" />
        <button class="btn btn-primary border send-vcode">{{ $t('settings_email.get_vcode') }}</button>
      </div>

      <!-- 验证码 -->
      <div class="form-item">
        <span class="label">&nbsp;</span>
        <button class="btn btn-primary btn-submit">{{ $t('settings_email.next_step') }}</button>
      </div>
    </div>
  </div>
</template>

<script>
import SettingsTopTabbar from '@/components/SettingsTopTabbar';

export default {
  layout: 'settings',
  middleware: 'auth',
  components: {
    SettingsTopTabbar
  }
};
</script>

<style lang="scss" scope>
@import '@/assets/scss/settings.scss';
</style>

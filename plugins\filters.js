// 注册全局过滤器
import Vue from 'vue';
import moment from 'moment';

// date2short 短日期显示
Vue.filter('date2short', (date) => {
  moment.locale('zh-cn');
  return moment(date)
    .startOf('second')
    .fromNow();
});

// date1short 短日期显示
Vue.filter('date1short', (date) => {
  date = new Date(date.replace(/-/g, '/')).getTime();
  moment.locale('zh-cn');
  return moment(date)
    .startOf('second')
    .fromNow();
});

// num2short 短数字显示
Vue.filter('num2short', (num) => {
  num = parseInt(num);
  if (!num) {
    // Nan or 0
    return 0;
  } else if (num >= 10000000) {
    return parseInt(num / 10000000) + 'w';
  } else if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  } else if (num >= 0) {
    return num;
  }
});

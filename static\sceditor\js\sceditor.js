/* eslint-disable */
"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(){function e(e,t){return(void 0===t?"undefined":_typeof(t))===e}function t(e){return!Object.keys(e).length}function n(e,t){for(var o=e===!!e,r=o?2:1,i=o?t:e,a=!!o&&e;r<arguments.length;r++){var l=arguments[r];for(var c in l){var s=l[c];if(!ce(s)){var u=null!==s&&"object"===(void 0===s?"undefined":_typeof(s))&&Object.getPrototypeOf(s)===Object.prototype,d=Array.isArray(s);i[c]=a&&(u||d)?n(!0,i[c]||(d?[]:{}),s):s}}}return i}function o(e,t){var n=e.indexOf(t);n>-1&&e.splice(n,1)}function r(e,t){if(Array.isArray(e)||"length"in e&&ue(e.length))for(var n=0;n<e.length;n++)t(n,e[n]);else Object.keys(e).forEach(function(n){t(n,e[n])})}function i(e){return e=parseFloat(e),isFinite(e)?e:0}function a(e,t,n){var o=(n||document).createElement(e);return r(t||{},function(e,t){"style"===e?o.style.cssText=t:e in o?o[e]=t:o.setAttribute(e,t)}),o}function l(e,t){for(var n=e||{};(n=n.parentNode)&&!/(9|11)/.test(n.nodeType);)if(!t||w(n,t))return n}function c(e,t){return w(e,t)?e:l(e,t)}function s(e){e.parentNode&&e.parentNode.removeChild(e)}function u(e,t){e.appendChild(t)}function d(e,t){return e.querySelectorAll(t)}function f(e,t,n,o,r){t.split(" ").forEach(function(t){var i;le(n)?(i=o["_sce-event-"+t+n]||function(t){for(var r=t.target;r&&r!==e;){if(w(r,n))return void o.call(r,t);r=r.parentNode}},o["_sce-event-"+t+n]=i):(i=n,r=o),e.addEventListener(t,i,r||!1)})}function p(e,t,n,o,r){t.split(" ").forEach(function(t){var i;le(n)?i=o["_sce-event-"+t+n]:(i=n,r=o),e.removeEventListener(t,i,r||!1)})}function m(e,t,n){if(arguments.length<3)return e.getAttribute(t);null==n?h(e,t):e.setAttribute(t,n)}function h(e,t){e.removeAttribute(t)}function g(e){b(e,"display","none")}function v(e){b(e,"display","")}function y(e){_(e)?g(e):v(e)}function b(e,t,n){if(arguments.length<3){if(le(t))return 1===e.nodeType?getComputedStyle(e)[t]:null;r(t,function(t,n){b(e,t,n)})}else{var o=(n||0===n)&&!isNaN(n);e.style[t]=o?n+"px":n}}function x(e,t,n){var o=arguments.length,i={};if(e.nodeType===fe){if(1===o)return r(e.attributes,function(e,t){/^data\-/i.test(t.name)&&(i[t.name.substr(5)]=t.value)}),i;if(2===o)return m(e,"data-"+t);m(e,"data-"+t,String(n))}}function w(e,t){var n=!1;return e&&e.nodeType===fe&&(n=(e.matches||e.msMatchesSelector||e.webkitMatchesSelector).call(e,t)),n}function C(e,t){return e!==t&&e.contains&&e.contains(t)}function k(e,t){var n=e.previousElementSibling;return t&&n?w(n,t)?n:null:n}function S(e,t){return t.parentNode.insertBefore(e,t)}function E(e){return e.className.trim().split(/\s+/)}function T(e,t){return w(e,"."+t)}function D(e,t){var n=E(e);n.indexOf(t)<0&&n.push(t),e.className=n.join(" ")}function M(e,t){var n=E(e);o(n,t),e.className=n.join(" ")}function N(e,t,n){n=ce(n)?!T(e,t):n,n?D(e,t):M(e,t)}function R(e,t){if(ce(t)){var n=getComputedStyle(e),o=i(n.paddingLeft)+i(n.paddingRight),r=i(n.borderLeftWidth)+i(n.borderRightWidth);return e.offsetWidth-o-r}b(e,"width",t)}function F(e,t){if(ce(t)){var n=getComputedStyle(e),o=i(n.paddingTop)+i(n.paddingBottom),r=i(n.borderTopWidth)+i(n.borderBottomWidth);return e.offsetHeight-o-r}b(e,"height",t)}function H(e,t,n){var o;se(window.CustomEvent)?o=new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}):(o=e.ownerDocument.createEvent("CustomEvent"),o.initCustomEvent(t,!0,!0,n)),e.dispatchEvent(o)}function _(e){return!!e.getClientRects().length}function z(e){return e.replace(/^-ms-/,"ms-").replace(/-(\w)/g,function(e,t){return t.toUpperCase()})}function A(e,t,n,o,r){for(e=r?e.lastChild:e.firstChild;e;){var i=r?e.previousSibling:e.nextSibling;if(!n&&!1===t(e)||!o&&!1===A(e,t,n,o,r)||n&&!1===t(e))return!1;e=i}}function B(e,t,n,o){A(e,t,n,o,!0)}function O(e,t){t=t||document;var n=t.createDocumentFragment(),o=a("div",{},t);for(o.innerHTML=e;o.firstChild;)u(n,o.firstChild);return n}function I(e){return e&&(!w(e,"p,div")||e.className||m(e,"style")||!t(x(e)))}function L(e,t){var n=a(t,{},e.ownerDocument);for(r(e.attributes,function(e,t){try{m(n,t.name,t.value)}catch(e){}});e.firstChild;)u(n,e.firstChild);return e.parentNode.replaceChild(n,e),n}function P(e){return!!/11?|9/.test(e.nodeType)&&"|iframe|area|base|basefont|br|col|frame|hr|img|input|wbr|isindex|link|meta|param|command|embed|keygen|source|track|object|".indexOf("|"+e.nodeName.toLowerCase()+"|")<0}function V(e,t){var n,o=(e||{}).nodeType||pe;return o!==fe?o===pe:(n=e.tagName.toLowerCase(),"code"===n?!t:he.indexOf("|"+n+"|")<0)}function W(e,t){t.style.cssText=e.style.cssText+t.style.cssText}function j(e){var t=function(e){for(;V(e.parentNode,!0);)e=e.parentNode;return e};A(e,function(e){var n=!V(e,!0);if(n&&V(e.parentNode,!0)){var o=t(e),r=Y(o,e),i=e;W(o,i),S(r,o),S(i,o)}if(n&&w(e,"ul,ol")&&w(e.parentNode,"ul,ol")){var l=k(e,"li");l||(l=a("li"),S(l,e)),u(l,e)}})}function q(e,t){for(;e=e.parentNode;)if(C(e,t))return e}function U(e,t){return e?(t?e.previousSibling:e.nextSibling)||U(e.parentNode,t):null}function $(e){var t,n,o,r,i,a,l,c=b(e,"whiteSpace"),u=/line$/i.test(c),d=e.firstChild;if(!/pre(\-wrap)?$/i.test(c))for(;d;){if(a=d.nextSibling,t=d.nodeValue,n=d.nodeType,n===fe&&d.firstChild&&$(d),n===pe){for(o=U(d),r=U(d,!0),l=!1;T(r,"sceditor-ignore");)r=U(r,!0);if(V(d)&&r){for(i=r;i.lastChild;)for(i=i.lastChild;T(i,"sceditor-ignore");)i=U(i,!0);l=i.nodeType===pe?/[\t\n\r ]$/.test(i.nodeValue):!V(i)}t=t.replace(/\u200B/g,""),r&&V(r)&&!l||(t=t.replace(u?/^[\t ]+/:/^[\t\n\r ]+/,"")),o&&V(o)||(t=t.replace(u?/[\t ]+$/:/[\t\n\r ]+$/,"")),t.length?d.nodeValue=t.replace(u?/[\t ]+/g:/[\t\n\r ]+/g," "):s(d)}d=a}}function Y(e,t){var n=e.ownerDocument.createRange();return n.setStartBefore(e),n.setEndAfter(t),n.extractContents()}function K(e){for(var t=0,n=0;e;)t+=e.offsetLeft,n+=e.offsetTop,e=e.offsetParent;return{left:t,top:n}}function X(e,t){var n,o,r=e.style;if(de[t]||(de[t]=z(t)),t=de[t],o=r[t],"textAlign"===t){if(n=r.direction,o=o||b(e,t),b(e.parentNode,t)===o||"block"!==b(e,"display")||w(e,"hr,th"))return"";if(/right/i.test(o)&&"rtl"===n||/left/i.test(o)&&"ltr"===n)return""}return o}function G(e,t,n){var o=X(e,t);return!!o&&(!n||o===n||Array.isArray(n)&&n.indexOf(o)>-1)}function J(e){return e.replace(/([\-.*+?^=!:${}()|\[\]\/\\])/g,"\\$1")}function Q(e,t){if(!e)return e;var n={"&":"&amp;","<":"&lt;",">":"&gt;","  ":"&nbsp; ","\r\n":"<br />","\r":"<br />","\n":"<br />"};return!1!==t&&(n['"']="&#34;",n["'"]="&#39;",n["`"]="&#96;"),e=e.replace(/ {2}|\r\n|[&<>\r\n'"`]/g,function(e){return n[e]||e})}function Z(e){var t,n=/^[^\/]*:/i,o=window.location;return e&&n.test(e)&&!Ce.test(e)?(t=o.pathname.split("/"),t.pop(),o.protocol+"//"+o.host+t.join("/")+"/"+e):e}function ee(e,t,n){var o=ke[e];return Object.keys(t).forEach(function(e){o=o.replace(new RegExp(J("{"+e+"}"),"g"),t[e])}),n&&(o=O(o)),o}function te(e){if("mozHidden"in document)for(var t,n=e.getBody();n;){if(t=n,t.firstChild)t=t.firstChild;else{for(;t&&!t.nextSibling;)t=t.parentNode;t&&(t=t.nextSibling)}3===n.nodeType&&/[\n\r\t]+/.test(n.nodeValue)&&(/^pre/.test(b(n.parentNode,"whiteSpace"))||s(n)),n=t}}function ne(e){var t=this,n=[],o=function(e){return"signal"+e.charAt(0).toUpperCase()+e.slice(1)},r=function(t,r){t=[].slice.call(t);var i,a,l=o(t.shift());for(i=0;i<n.length;i++)if(l in n[i]&&(a=n[i][l].apply(e,t),r))return a};t.call=function(){r(arguments,!1)},t.callOnlyFirst=function(){return r(arguments,!0)},t.hasHandler=function(e){var t=n.length;for(e=o(e);t--;)if(e in n[t])return!0;return!1},t.exists=function(e){return e in Te&&("function"==typeof(e=Te[e])&&"object"===_typeof(e.prototype))},t.isRegistered=function(e){if(t.exists(e))for(var o=n.length;o--;)if(n[o]instanceof Te[e])return!0;return!1},t.register=function(o){return!(!t.exists(o)||t.isRegistered(o))&&(o=new Te[o],n.push(o),"init"in o&&o.init.call(e),!0)},t.deregister=function(o){var r,i=n.length,a=!1;if(!t.isRegistered(o))return a;for(;i--;)n[i]instanceof Te[o]&&(r=n.splice(i,1)[0],a=!0,"destroy"in r&&r.destroy.call(e));return a},t.destroy=function(){for(var t=n.length;t--;)"destroy"in n[t]&&n[t].destroy.call(e);n=[],e=null}}function oe(e,t){var n,o,r=t||e.contentDocument||e.document,i="sceditor-start-marker",l="sceditor-end-marker",c=this;c.insertHTML=function(e,t){var n,o;if(!c.selectedRange())return!1;for(t&&(e+=c.selectedHtml()+t),o=a("p",{},r),n=r.createDocumentFragment(),o.innerHTML=e;o.firstChild;)u(n,o.firstChild);c.insertNode(n)},o=function(e,t,o){var s,d=r.createDocumentFragment();if("string"==typeof e?(t&&(e+=c.selectedHtml()+t),d=O(e)):(u(d,e),t&&(u(d,c.selectedRange().extractContents()),u(d,t))),s=d.lastChild){for(;!V(s.lastChild,!0);)s=s.lastChild;if(P(s)?s.lastChild||u(s,document.createTextNode("​")):s=d,c.removeMarkers(),u(s,n(i)),u(s,n(l)),o){var f=a("div");return u(f,d),f.innerHTML}return d}},c.insertNode=function(e,t){var n=o(e,t),r=c.selectedRange(),i=r.commonAncestorContainer;if(!n)return!1;r.deleteContents(),i&&3!==i.nodeType&&!P(i)?S(n,i):r.insertNode(n),c.restoreRange()},c.cloneSelected=function(){var e=c.selectedRange();if(e)return e.cloneRange()},c.selectedRange=function(){var t,n,o=e.getSelection();if(o){if(o.rangeCount<=0){for(n=r.body;n.firstChild;)n=n.firstChild;t=r.createRange(),t.setStartBefore(n),o.addRange(t)}return o.rangeCount>0&&(t=o.getRangeAt(0)),t}},c.hasSelection=function(){var t=e.getSelection();return t&&t.rangeCount>0},c.selectedHtml=function(){var e,t=c.selectedRange();return t?(e=a("p",{},r),u(e,t.cloneContents()),e.innerHTML):""},c.parentNode=function(){var e=c.selectedRange();if(e)return e.commonAncestorContainer},c.getFirstBlockParent=function(e){return function e(t){return V(t,!0)?(t=t?t.parentNode:null,t?e(t):t):t}(e||c.parentNode())},c.insertNodeAt=function(e,t){var n=c.selectedRange(),o=c.cloneSelected();if(!o)return!1;o.collapse(e),o.insertNode(t),c.selectRange(n)},n=function(e){c.removeMarker(e);var t=a("span",{id:e,className:"sceditor-selection sceditor-ignore",style:"display:none;line-height:0"},r);return t.innerHTML=" ",t},c.insertMarkers=function(){var e=c.selectedRange(),t=n(i);c.removeMarkers(),c.insertNodeAt(!0,t),e&&e.collapsed?t.parentNode.insertBefore(n(l),t.nextSibling):c.insertNodeAt(!1,n(l))},c.getMarker=function(e){return r.getElementById(e)},c.removeMarker=function(e){var t=c.getMarker(e);t&&s(t)},c.removeMarkers=function(){c.removeMarker(i),c.removeMarker(l)},c.saveRange=function(){c.insertMarkers()},c.selectRange=function(t){var n,o=e.getSelection(),i=t.endContainer;if(!De&&t.collapsed&&i&&!V(i,!0)){for(n=i.lastChild;n&&w(n,".sceditor-ignore");)n=n.previousSibling;if(w(n,"br")){var a=r.createRange();a.setEndAfter(n),a.collapse(!1),c.compare(t,a)&&(t.setStartBefore(n),t.collapse(!0))}}o&&(c.clear(),o.addRange(t))},c.restoreRange=function(){var e,t=c.selectedRange(),n=c.getMarker(i),o=c.getMarker(l);if(!n||!o||!t)return!1;e=n.nextSibling===o,t=r.createRange(),t.setStartBefore(n),t.setEndAfter(o),e&&t.collapse(!0),c.selectRange(t),c.removeMarkers()},c.selectOuterText=function(e,t){var n,o,r=c.cloneSelected();if(!r)return!1;r.collapse(!1),n=Me(r,!0,e),o=Me(r,!1,t),r.setStart(n.node,n.offset),r.setEnd(o.node,o.offset),c.selectRange(r)},c.getOuterText=function(e,t){var n=c.cloneSelected();return n?(n.collapse(!e),Me(n,e,t).text):""},c.replaceKeyword=function(e,t,n,o,r,i){n||e.sort(function(e,t){return e[0].length-t[0].length});var a,l,s,u,d,f,p,m,h=e.length,g=r?1:0,v=o||e[h-1][0].length;for(r&&v++,i=i||"",a=c.getOuterText(!0,v),d=a.length,a+=i,t&&(a+=c.getOuterText(!1,v));h--;)if(p=e[h][0],m=p.length,u=Math.max(0,d-m-g),s=-1,r?(l=a.substr(u).match(new RegExp("(^|[\\s    ])"+J(p)+"(^|[\\s    ])")))&&(s=l.index+u+l[1].length):s=a.indexOf(p,u),s>-1&&s<=d&&s+m+g>=d)return f=d-s,c.selectOuterText(f,m-f-(/^\S/.test(i)?1:0)),c.insertHTML(e[h][1]),!0;return!1},c.compare=function(e,t){return t||(t=c.selectedRange()),e&&t?0===e.compareBoundaryPoints(Range.END_TO_END,t)&&0===e.compareBoundaryPoints(Range.START_TO_START,t):!e&&!t},c.clear=function(){var t=e.getSelection();t&&(t.removeAllRanges?t.removeAllRanges():t.empty&&t.empty())}}function re(e,t,n){var o=e.ownerDocument,i="(^|\\s| | | | |$)",a=[],c={};l(e,"code")||(r(t,function(e){c[e]=new RegExp(i+J(e)+i),a.push(e)}),a.sort(function(e,t){return t.length-e.length}),function e(r){for(r=r.firstChild;r;){if(r.nodeType!==fe||w(r,"code")||e(r),r.nodeType===pe)for(var i=0;i<a.length;i++){var l=r.nodeValue,s=a[i],u=n?l.search(c[s]):l.indexOf(s);if(u>-1){var d=l.indexOf(s,u),f=O(t[s],o),p=l.substr(d+s.length);f.appendChild(o.createTextNode(p)),r.nodeValue=l.substr(0,d),r.parentNode.insertBefore(f,r.nextSibling)}}r=r.nextSibling}}(e))}function ie(e,t){var n;A(e,function(e){V(e,!0)?(n||(n=a("p",{},t),S(n,e)),e.nodeType===pe&&""===e.nodeValue||u(n,e)):n=null},!1,!0)}function ae(e,t){var i,l,h,x,C,k,E,z,A,O,W,q,U,Y,X,G,J,Z,te,ue,de,he,ve,ye,Ce,ke,Se,Te,De,Me,ze,Ae,Be,Oe,Ie,Le,Pe,Ve,We,je,qe,Ue,$e,Ye,Ke,Xe,Ge,Je,Qe,Ze,et,tt,nt,ot,rt,it,at,lt,ct,st,ut,dt,ft=this,pt={},mt=[],ht=[],gt={},vt={},yt={};ft.commands=n(!0,{},t.commands||Ee);var bt=ft.opts=n(!0,{},ge,t);ft.opts.emoticons=t.emoticons||ge.emoticons,Se=function(){e._sceditor=ft,bt.locale&&"en"!==bt.locale&&Be(),l=a("div",{className:"sceditor-container"}),S(l,e),b(l,"z-index",bt.zIndex),Fe&&D(l,"ie ie"+Fe),ue=e.required,e.required=!1;var t=ae.formats[bt.format];i=t?new t:{},"init"in i&&i.init.call(ft),Ae(),Ve(),Oe(),ze(),Ie(),Le(),we||ft.toggleSourceMode(),Ze();var n=function e(){p(Ne,"load",e),bt.autofocus&&rt(),dt(),tt(),X.call("ready"),"onReady"in i&&i.onReady.call(ft)};f(Ne,"load",n),"complete"===Re.readyState&&n()},Ae=function(){var e=bt.plugins;e=e?e.toString().split(","):[],X=new ne(ft),e.forEach(function(e){X.register(e.trim())})},Be=function(){var e;U=ae.locale[bt.locale],U||(e=bt.locale.split("-"),U=ae.locale[e[0]]),U&&U.dateFormat&&(bt.dateFormat=U.dateFormat)},ze=function(){z=a("textarea"),x=a("iframe",{frameborder:0,allowfullscreen:!0}),bt.startInSourceMode?(D(l,"sourceMode"),g(x)):(D(l,"wysiwygMode"),g(z)),bt.spellcheck||m(l,"spellcheck","false"),"https:"===Ne.location.protocol&&m(x,"src","javascript:false"),u(l,x),u(l,z),ft.dimensions(bt.width||R(e),bt.height||F(e));var t=Fe?"ie ie"+Fe:"";t+=xe?" ios":"",E=x.contentDocument,E.open(),E.write(ee("html",{attrs:' class="'+t+'"',spellcheck:bt.spellcheck?"":'spellcheck="false"',charset:bt.charset,style:bt.style})),E.close(),k=E.body,C=x.contentWindow,ft.readOnly(!!bt.readOnly),(xe||be||Fe)&&(F(k,"100%"),Fe||f(k,"touchend",ft.focus));var n=m(e,"tabindex");m(z,"tabindex",n),m(x,"tabindex",n),Y=new oe(C),g(e),ft.val(e.value);var o=bt.placeholder||m(e,"placeholder");o&&(z.placeholder=o,m(k,"placeholder",o))},Ie=function(){bt.autoUpdate&&(f(k,"blur",ut),f(z,"blur",ut)),null===bt.rtl&&(bt.rtl="rtl"===b(z,"direction")),ft.rtl(!!bt.rtl),bt.autoExpand&&(f(k,"load",dt,me),f(k,"input keyup",dt)),bt.resizeEnabled&&Pe(),m(l,"id",bt.id),ft.emoticons(bt.emoticonsEnabled)},Le=function(){var t=e.form,n="onselectionchange"in E?"selectionchange":"keyup focus blur contextmenu mouseup touchend click";f(Re,"click",Je),t&&(f(t,"reset",Ye),f(t,"submit",ft.updateOriginal,me)),f(k,"keypress",$e),f(k,"keydown",qe),f(k,"keydown",Ue),f(k,"keyup",tt),f(k,"blur",ct),f(k,"keyup",st),f(k,"paste",We),f(k,"compositionstart compositionend",Xe),f(k,n,nt),f(k,"keydown keyup keypress focus blur contextmenu",Ge),bt.emoticonsCompat&&Ne.getSelection&&f(k,"keyup",it),f(k,"blur",function(){ft.val()||D(k,"placeholder")}),f(k,"focus",function(){M(k,"placeholder")}),f(z,"blur",ct),f(z,"keyup",st),f(z,"keydown",qe),f(z,"compositionstart compositionend",Xe),f(z,"keydown keyup keypress focus blur contextmenu",Ge),f(E,"mousedown",Ke),f(E,n,nt),f(E,"beforedeactivate keyup mouseup",Me),f(E,"keyup",tt),f(E,"focus",function(){O=null}),f(l,"selectionchanged",ot),f(l,"selectionchanged",Ze),f(l,"selectionchanged valuechanged nodechanged pasteraw paste",Ge)},Oe=function(){var e,t=ft.commands,n=(bt.toolbarExclude||"").split(","),o=bt.toolbar.split("|");h=a("div",{className:"sceditor-toolbar",unselectable:"on"}),bt.icons in ae.icons&&(ke=new ae.icons[bt.icons]),r(o,function(o,i){e=a("div",{className:"sceditor-group"}),r(i.split(","),function(o,r){var i,a,l=t[r];if(l&&!(n.indexOf(r)>-1)){if(a=l.shortcut,i=ee("toolbarButton",{name:r,dispName:ft._(l.name||l.tooltip||r)},!0).firstChild,ke&&ke.create){ke.create(r)&&(S(ke.create(r),i.firstChild),D(i,"has-icon"))}i._sceTxtMode=!!l.txtExec,i._sceWysiwygMode=!!l.exec,N(i,"disabled",!l.exec),f(i,"click",function(e){T(i,"disabled")||De(i,l),Ze(),e.preventDefault()}),f(i,"mousedown",function(e){ft.closeDropDown(),e.preventDefault()}),l.tooltip&&m(i,"title",ft._(l.tooltip)+(a?" ("+a+")":"")),a&&ft.addShortcut(a,r),l.state?ht.push({name:r,state:l.state}):le(l.exec)&&ht.push({name:r,state:l.exec}),u(e,i),vt[r]=i}}),e.firstChild&&u(h,e)}),u(bt.toolbarContainer||l,h)},Pe=function(){var e,t,n,o,r,i,c=a("div",{className:"sceditor-grip"}),s=a("div",{className:"sceditor-resize-cover"}),d="touchcancel touchend mouseup",m=0,h=0,y=0,b=0,x=0,w=0,C=R(l),k=F(l),S=!1,E=ft.rtl();if(e=bt.resizeMinHeight||k/1.5,t=bt.resizeMaxHeight||2.5*k,n=bt.resizeMinWidth||C/1.25,o=bt.resizeMaxWidth||1.25*C,r=function(r){"touchmove"===r.type?(r=Ne.event,y=r.changedTouches[0].pageX,b=r.changedTouches[0].pageY):(y=r.pageX,b=r.pageY);var i=w+(b-h),a=E?x-(y-m):x+(y-m);o>0&&a>o&&(a=o),n>0&&a<n&&(a=n),bt.resizeWidth||(a=!1),t>0&&i>t&&(i=t),e>0&&i<e&&(i=e),bt.resizeHeight||(i=!1),(a||i)&&ft.dimensions(a,i),r.preventDefault()},i=function(e){S&&(S=!1,g(s),M(l,"resizing"),p(Re,"touchmove mousemove",r),p(Re,d,i),e.preventDefault())},ke&&ke.create){var T=ke.create("grip");T&&(u(c,T),D(c,"has-icon"))}u(l,c),u(l,s),g(s),f(c,"touchstart mousedown",function(e){"touchstart"===e.type?(e=Ne.event,m=e.touches[0].pageX,h=e.touches[0].pageY):(m=e.pageX,h=e.pageY),x=R(l),w=F(l),S=!0,D(l,"resizing"),v(s),f(Re,"touchmove mousemove",r),f(Re,d,i),e.preventDefault()})},Ve=function(){var e=bt.emoticons,t=bt.emoticonsRoot||"";e&&(yt=n({},e.more,e.dropdown,e.hidden)),r(yt,function(e,n){yt[e]=ee("emoticon",{key:e,url:t+(n.url||n),tooltip:n.tooltip||e}),bt.emoticonsEnabled&&mt.push(a("img",{src:t+(n.url||n)}))})},rt=function(){var e,t,n=k.firstChild,o=!!bt.autofocusEnd;if(_(l)){if(ft.sourceMode())return t=o?z.value.length:0,void z.setSelectionRange(t,t);if($(k),o)for((n=k.lastChild)||(n=a("p",{},E),u(k,n));n.lastChild;)n=n.lastChild,!He&&w(n,"br")&&n.previousSibling&&(n=n.previousSibling);e=E.createRange(),P(n)?e.selectNodeContents(n):(e.setStartBefore(n),o&&e.setStartAfter(n)),e.collapse(!o),Y.selectRange(e),Z=e,o&&(k.scrollTop=k.scrollHeight),ft.focus()}},ft.readOnly=function(e){return"boolean"!=typeof e?!z.readonly:(k.contentEditable=!e,z.readonly=!e,Qe(e),ft)},ft.rtl=function(e){var t=e?"rtl":"ltr";return"boolean"!=typeof e?"rtl"===m(z,"dir"):(m(k,"dir",t),m(z,"dir",t),M(l,"rtl"),M(l,"ltr"),D(l,t),ke&&ke.rtl&&ke.rtl(e),ft)},Qe=function(e){var t=ft.inSourceMode()?"_sceTxtMode":"_sceWysiwygMode";r(vt,function(n,o){N(o,"disabled",e||!o[t])})},ft.width=function(e,t){return e||0===e?(ft.dimensions(e,null,t),ft):R(l)},ft.dimensions=function(e,t,n){return e=!(!e&&0!==e)&&e,t=!(!t&&0!==t)&&t,!1===e&&!1===t?{width:ft.width(),height:ft.height()}:(!1!==e&&(!1!==n&&(bt.width=e),R(l,e)),!1!==t&&(!1!==n&&(bt.height=t),F(l,t)),ft)},ft.height=function(e,t){return e||0===e?(ft.dimensions(null,e,t),ft):F(l)},ft.maximize=function(e){return ce(e)?T(l,"sceditor-maximize"):(e=!!e,e&&(ye=Ne.pageYOffset),N(Re.documentElement,"sceditor-maximize",e),N(Re.body,"sceditor-maximize",e),N(l,"sceditor-maximize",e),ft.width(e?"100%":bt.width,!1),ft.height(e?"100%":bt.height,!1),e||Ne.scrollTo(0,ye),dt(),ft)},dt=function(){bt.autoExpand&&!ve&&(ve=setTimeout(ft.expandToContent,200))},ft.expandToContent=function(t){if(!ft.maximize()){if(clearTimeout(ve),ve=!1,!he){var n=bt.resizeMinHeight||bt.height||F(e);he={min:n,max:bt.resizeMaxHeight||2*n}}var o=Re.createRange();o.selectNodeContents(k);var r=o.getBoundingClientRect(),i=E.documentElement.clientHeight-1,a=r.bottom-r.top,l=ft.height()+1+(a-i);t||-1===he.max||(l=Math.min(l,he.max)),ft.height(Math.ceil(Math.max(l,he.min)))}},ft.destroy=function(){if(X){X.destroy(),Y=null,O=null,X=null,A&&s(A),p(Re,"click",Je);var t=e.form;t&&(p(t,"reset",Ye),p(t,"submit",ft.updateOriginal)),s(z),s(h),s(l),delete e._sceditor,v(e),e.required=ue}},ft.createDropDown=function(e,t,o,i){var c,s="sceditor-"+t;ft.closeDropDown(!0),A&&T(A,s)||(!1!==i&&r(d(o,":not(input):not(textarea)"),function(e,t){t.nodeType===fe&&m(t,"unselectable","on")}),c=n({top:e.offsetTop,left:e.offsetLeft,marginTop:e.clientHeight},bt.dropDownCss),A=a("div",{className:"sceditor-dropdown "+s}),b(A,c),u(A,o),u(l,A),f(A,"click focusin",function(e){e.stopPropagation()}),setTimeout(function(){if(A){var e=d(A,"input,textarea")[0];e&&e.focus()}}))},Je=function(e){3!==e.which&&A&&!e.defaultPrevented&&(ut(),ft.closeDropDown())},We=function(e){var t=Fe||be,n=k,o=e.clipboardData;if(o&&!t){var r={},i=o.types,a=o.items;e.preventDefault();for(var l=0;l<i.length;l++){if(Ne.FileReader&&a&&_e.test(a[l].type))return function(e){var t=new FileReader;t.onload=function(e){je({html:'<img src="'+e.target.result+'" />'})},t.readAsDataURL(e)}(o.items[l].getAsFile());r[i[l]]=o.getData(i[l])}r.text=r["text/plain"],r.html=r["text/html"],je(r)}else if(!Ce){var c=n.scrollTop;for(Y.saveRange(),Ce=Re.createDocumentFragment();n.firstChild;)u(Ce,n.firstChild);setTimeout(function(){var e=n.innerHTML;n.innerHTML="",u(n,Ce),n.scrollTop=c,Ce=!1,Y.restoreRange(),je({html:e})},0)}},je=function(e){var t=a("div",{},E);X.call("pasteRaw",e),H(l,"pasteraw",e),e.html?(t.innerHTML=e.html,j(t)):t.innerHTML=Q(e.text||"");var n={val:t.innerHTML};"fragmentToSource"in i&&(n.val=i.fragmentToSource(n.val,E,G)),X.call("paste",n),H(l,"paste",n),"fragmentToHtml"in i&&(n.val=i.fragmentToHtml(n.val,G)),X.call("pasteHtml",n),ft.wysiwygEditorInsertHtml(n.val,null,!0)},ft.closeDropDown=function(e){A&&(s(A),A=null),!0===e&&ft.focus()},ft.wysiwygEditorInsertHtml=function(e,t,n){var o,r,i,a=F(x);ft.focus(),!n&&c(J,"code")||(Y.insertHTML(e,t),Y.saveRange(),Te(),o=d(k,"#sceditor-end-marker")[0],v(o),r=k.scrollTop,i=K(o).top+1.5*o.offsetHeight-a,g(o),(i>r||i+a<r)&&(k.scrollTop=i),lt(!1),Y.restoreRange(),tt())},ft.wysiwygEditorInsertText=function(e,t){ft.wysiwygEditorInsertHtml(Q(e),Q(t))},ft.insertText=function(e,t){return ft.inSourceMode()?ft.sourceEditorInsertText(e,t):ft.wysiwygEditorInsertText(e,t),ft},ft.sourceEditorInsertText=function(e,t){var n,o,r=z.selectionStart,i=z.selectionEnd;n=z.scrollTop,z.focus(),o=z.value,t&&(e+=o.substring(r,i)+t),z.value=o.substring(0,r)+e+o.substring(i,o.length),z.selectionStart=r+e.length-(t?t.length:0),z.selectionEnd=z.selectionStart,z.scrollTop=n,z.focus(),lt()},ft.getRangeHelper=function(){return Y},ft.sourceEditorCaret=function(e){return z.focus(),e?(z.selectionStart=e.start,z.selectionEnd=e.end,this):{start:z.selectionStart,end:z.selectionEnd}},ft.val=function(e,t){return le(e)?(ft.inSourceMode()?ft.setSourceEditorValue(e):(!1!==t&&"toHtml"in i&&(e=i.toHtml(e)),ft.setWysiwygEditorValue(e)),ft):ft.inSourceMode()?ft.getSourceEditorValue(!1):ft.getWysiwygEditorValue(t)},ft.insert=function(e,t,n,o,r){if(ft.inSourceMode())return ft.sourceEditorInsertText(e,t),ft;if(t){var a=Y.selectedHtml();!1!==n&&"fragmentToSource"in i&&(a=i.fragmentToSource(a,E,G)),e+=a+t}return!1!==n&&"fragmentToHtml"in i&&(e=i.fragmentToHtml(e,G)),!1!==n&&!0===r&&(e=e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")),ft.wysiwygEditorInsertHtml(e),ft},ft.getWysiwygEditorValue=function(e){for(var t,n=a("div",{},E),o=k.childNodes,r=0;r<o.length;r++)u(n,o[r].cloneNode(!0));return u(k,n),j(n),s(n),t=n.innerHTML,!1!==e&&i.hasOwnProperty("toSource")&&(t=i.toSource(t,E)),t},ft.getBody=function(){return k},ft.getContentAreaContainer=function(){return x},ft.getSourceEditorValue=function(e){var t=z.value;return!1!==e&&"toHtml"in i&&(t=i.toHtml(t)),t},ft.setWysiwygEditorValue=function(e){e||(e="<p>"+(Fe?"":"<br />")+"</p>"),k.innerHTML=e,Te(),tt(),lt(),dt()},ft.setSourceEditorValue=function(e){z.value=e,lt()},ft.updateOriginal=function(){e.value=ft.val()},Te=function(){bt.emoticonsEnabled&&re(k,yt,bt.emoticonsCompat)},ft.inSourceMode=function(){return T(l,"sourceMode")},ft.sourceMode=function(e){var t=ft.inSourceMode();return"boolean"!=typeof e?t:((t&&!e||!t&&e)&&ft.toggleSourceMode(),ft)},ft.toggleSourceMode=function(){var e=ft.inSourceMode();!we&&e||(e||(Y.saveRange(),Y.clear()),ft.blur(),e?ft.setWysiwygEditorValue(ft.getSourceEditorValue()):ft.setSourceEditorValue(ft.getWysiwygEditorValue()),O=null,y(z),y(x),N(l,"wysiwygMode",e),N(l,"sourceMode",!e),Qe(),Ze())},et=function(){return z.focus(),z.value.substring(z.selectionStart,z.selectionEnd)},De=function(e,t){ft.inSourceMode()?t.txtExec&&(Array.isArray(t.txtExec)?ft.sourceEditorInsertText.apply(ft,t.txtExec):t.txtExec.call(ft,e,et())):t.exec&&(se(t.exec)?t.exec.call(ft,e):ft.execCommand(t.exec,t.hasOwnProperty("execParam")?t.execParam:null))},Me=function(){Fe&&(O=Y.selectedRange())},ft.execCommand=function(e,t){var n=!1,o=ft.commands[e];if(ft.focus(),!c(Y.parentNode(),"code")){try{n=E.execCommand(e,!1,t)}catch(e){}!n&&o&&o.errorMessage&&alert(ft._(o.errorMessage)),Ze()}},nt=function(){function e(){if(C.getSelection()&&C.getSelection().rangeCount<=0)Z=null;else if(Y&&!Y.compare(Z)){if((Z=Y.cloneSelected())&&Z.collapsed){var e=Z.startContainer,t=Z.startOffset;for(t&&e.nodeType!==pe&&(e=e.childNodes[t]);e&&e.parentNode!==k;)e=e.parentNode;e&&V(e,!0)&&(Y.saveRange(),ie(k,E),Y.restoreRange())}H(l,"selectionchanged")}te=!1}te||(te=!0,"onselectionchange"in E?e():setTimeout(e,100))},ot=function(){var e,t=Y.parentNode();G!==t&&(e=G,G=t,J=Y.getFirstBlockParent(t),H(l,"nodechanged",{oldNode:e,newNode:G}))},ft.currentNode=function(){return G},ft.currentBlockNode=function(){return J},Ze=function(){var e,t,n=E,o=ft.sourceMode();if(ft.readOnly())return void r(d(h,"active"),function(e,t){M(t,"active")});o||(t=Y.parentNode(),e=Y.getFirstBlockParent(t));for(var i=0;i<ht.length;i++){var a=0,l=vt[ht[i].name],c=ht[i].state,s=o&&!l._sceTxtMode||!o&&!l._sceWysiwygMode;if(le(c)){if(!o)try{a=n.queryCommandEnabled(c)?0:-1,a>-1&&(a=n.queryCommandState(c)?1:0)}catch(e){}}else s||(a=c.call(ft,t,e));N(l,"disabled",s||a<0),N(l,"active",a>0)}ke&&ke.update&&ke.update(o,t,e)},$e=function(e){if(!e.defaultPrevented&&(ft.closeDropDown(),13===e.which)){if(!w(J,"li,ul,ol")&&I(J)){O=null;var t=a("br",{},E);if(Y.insertNode(t),!He){var n=t.parentNode,o=n.lastChild;o&&o.nodeType===pe&&""===o.nodeValue&&(s(o),o=n.lastChild),!V(n,!0)&&o===t&&V(t.previousSibling)&&Y.insertHTML("<br>")}e.preventDefault()}}},tt=function(){B(k,function(e){if(e.nodeType===fe&&!/inline/.test(b(e,"display"))&&!w(e,".sceditor-nlf")&&I(e)){var t=a("p",{},E);return t.className="sceditor-nlf",t.innerHTML=He?"":"<br />",u(k,t),!1}if(3===e.nodeType&&!/^\s*$/.test(e.nodeValue)||w(e,"br"))return!1})},Ye=function(){ft.val(e.value)},Ke=function(){ft.closeDropDown(),O=null},ft._=function(){var e=arguments;return U&&U[e[0]]&&(e[0]=U[e[0]]),e[0].replace(/\{(\d+)\}/g,function(t,n){return void 0!==e[n-0+1]?e[n-0+1]:"{"+n+"}"})},Ge=function(e){X&&X.call(e.type+"Event",e,ft);var t=(e.target===z?"scesrc":"scewys")+e.type;pt[t]&&pt[t].forEach(function(t){t.call(ft,e)})},ft.bind=function(e,t,n,o){e=e.split(" ");for(var r=e.length;r--;)if(se(t)){var i="scewys"+e[r],a="scesrc"+e[r];n||(pt[i]=pt[i]||[],pt[i].push(t)),o||(pt[a]=pt[a]||[],pt[a].push(t)),"valuechanged"===e[r]&&(lt.hasHandler=!0)}return ft},ft.unbind=function(e,t,n,r){e=e.split(" ");for(var i=e.length;i--;)se(t)&&(n||o(pt["scewys"+e[i]]||[],t),r||o(pt["scesrc"+e[i]]||[],t));return ft},ft.blur=function(e,t,n){return se(e)?ft.bind("blur",e,t,n):ft.sourceMode()?z.blur():k.blur(),ft},ft.focus=function(e,t,n){if(se(e))ft.bind("focus",e,t,n);else if(ft.inSourceMode())z.focus();else{if(d(E,":focus").length)return;var o,r=Y.selectedRange();Z||rt(),!He&&r&&1===r.endOffset&&r.collapsed&&(o=r.endContainer)&&1===o.childNodes.length&&w(o.firstChild,"br")&&(r.setStartBefore(o.firstChild),r.collapse(!0),Y.selectRange(r)),C.focus(),k.focus(),O&&(Y.selectRange(O),O=null)}return Ze(),ft},ft.keyDown=function(e,t,n){return ft.bind("keydown",e,t,n)},ft.keyPress=function(e,t,n){return ft.bind("keypress",e,t,n)},ft.keyUp=function(e,t,n){return ft.bind("keyup",e,t,n)},ft.nodeChanged=function(e){return ft.bind("nodechanged",e,!1,!0)},ft.selectionChanged=function(e){return ft.bind("selectionchanged",e,!1,!0)},ft.valueChanged=function(e,t,n){return ft.bind("valuechanged",e,t,n)},it=function(){},ft.emoticons=function(e){return ft},ft.css=function(e){return de||(de=a("style",{id:"inline"},E),u(E.head,de)),le(e)?(de.styleSheet?de.styleSheet.cssText=e:de.innerHTML=e,ft):de.styleSheet?de.styleSheet.cssText:de.innerHTML},qe=function(e){var t=[],n={"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+",";":": ","'":'"',",":"<",".":">","/":"?","\\":"|","[":"{","]":"}"},o={8:"backspace",9:"tab",13:"enter",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",91:"win",92:"win",93:"select",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scrolllock",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},r={109:"-",110:"del",111:"/",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"},i=e.which,a=o[i]||String.fromCharCode(i).toLowerCase();(e.ctrlKey||e.metaKey)&&t.push("ctrl"),e.altKey&&t.push("alt"),e.shiftKey&&(t.push("shift"),r[i]?a=r[i]:n[a]&&(a=n[a])),a&&(i<16||i>18)&&t.push(a),t=t.join("+"),gt[t]&&!1===gt[t].call(ft)&&(e.stopPropagation(),e.preventDefault())},ft.addShortcut=function(e,t){return e=e.toLowerCase(),le(t)?gt[e]=function(){return De(vt[t],ft.commands[t]),!1}:gt[e]=t,ft},ft.removeShortcut=function(e){return delete gt[e.toLowerCase()],ft},Ue=function(e){var t,n,o;if(!bt.disableBlockRemove&&8===e.which&&(n=Y.selectedRange())&&(t=n.startContainer,0===n.startOffset&&(o=at())&&!w(o,"body"))){for(;t!==o;){for(;t.previousSibling;)if(t=t.previousSibling,t.nodeType!==pe||t.nodeValue)return;if(!(t=t.parentNode))return}ft.clearBlockFormatting(o),e.preventDefault()}},at=function(){for(var e=J;!I(e)||V(e,!0);)if(!(e=e.parentNode)||w(e,"body"))return;return e},ft.clearBlockFormatting=function(e){return!(e=e||at())||w(e,"body")?ft:(Y.saveRange(),e.className="",O=null,m(e,"style",""),w(e,"p,div,td")||L(e,"p"),Y.restoreRange(),ft)},lt=function(e){if(X&&(X.hasHandler("valuechangedEvent")||lt.hasHandler)){var t,n=ft.sourceMode(),o=!n&&Y.hasSelection();W=!1,e=!1!==e&&!E.getElementById("sceditor-start-marker"),q&&(clearTimeout(q),q=!1),o&&e&&Y.saveRange(),t=n?z.value:k.innerHTML,t!==lt.lastVal&&(lt.lastVal=t,H(l,"valuechanged",{rawValue:n?ft.val():t})),o&&e&&Y.removeMarkers()}},ct=function(){q&&lt()},st=function(e){var t=e.which,n=st.lastChar,o=13===n||32===n,r=8===n||46===n;st.lastChar=t,W||(13===t||32===t?o?st.triggerNext=!0:lt():8===t||46===t?r?st.triggerNext=!0:lt():st.triggerNext&&(lt(),st.triggerNext=!1),clearTimeout(q),q=setTimeout(function(){W||lt()},1500))},Xe=function(e){(W=/start/i.test(e.type))||lt()},ut=function(){ft.updateOriginal()},Se()}var le=e.bind(null,"string"),ce=e.bind(null,"undefined"),se=e.bind(null,"function"),ue=e.bind(null,"number"),de={},fe=1,pe=3,me=!0,he="|body|hr|p|div|h1|h2|h3|h4|h5|h6|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|blockquote|center|",ge={
toolbar:"bold,italic,underline,strike,subscript,superscript|left,center,right,justify|font,size,color,removeformat|cut,copy,pastetext|bulletlist,orderedlist,indent,outdent|table|code,quote|horizontalrule,image,email,link,unlink|emoticon,youtube,date,time|ltr,rtl|print,maximize,source",toolbarExclude:null,style:"jquery.sceditor.default.css",fonts:"Arial,Arial Black,Comic Sans MS,Courier New,Georgia,Impact,Sans-serif,Serif,Times New Roman,Trebuchet MS,Verdana",colors:"#000000,#44B8FF,#1E92F7,#0074D9,#005DC2,#00369B,#b3d5f4|#444444,#C3FFFF,#9DF9FF,#7FDBFF,#68C4E8,#419DC1,#d9f4ff|#666666,#72FF84,#4CEA5E,#2ECC40,#17B529,#008E02,#c0f0c6|#888888,#FFFF44,#FFFA1E,#FFDC00,#E8C500,#C19E00,#fff5b3|#aaaaaa,#FFC95F,#FFA339,#FF851B,#E86E04,#C14700,#ffdbbb|#cccccc,#FF857A,#FF5F54,#FF4136,#E82A1F,#C10300,#ffc6c3|#eeeeee,#FF56FF,#FF30DC,#F012BE,#D900A7,#B20080,#fbb8ec|#ffffff,#F551FF,#CF2BE7,#B10DC9,#9A00B2,#9A00B2,#e8b6ef",locale:m(document.documentElement,"lang")||"en",charset:"utf-8",emoticonsCompat:!1,emoticonsEnabled:!0,emoticonsRoot:"",emoticons:{dropdown:{},more:{},hidden:{}},width:null,height:null,resizeEnabled:!0,resizeMinWidth:null,resizeMinHeight:null,resizeMaxHeight:null,resizeMaxWidth:null,resizeHeight:!0,resizeWidth:!0,dateFormat:"year-month-day",toolbarContainer:null,enablePasteFiltering:!1,disablePasting:!1,readOnly:!1,rtl:!1,autofocus:!1,autofocusEnd:!0,autoExpand:!1,autoUpdate:!1,spellcheck:!0,runWithoutWysiwygSupport:!1,startInSourceMode:!1,id:null,plugins:"",zIndex:null,bbcodeTrim:!1,disableBlockRemove:!1,parserOptions:{},dropDownCss:{}},ve=navigator.userAgent,ye=function(){var e=3,t=document,n=t.createElement("div"),o=n.getElementsByTagName("i");do{n.innerHTML="\x3c!--[if gt IE "+ ++e+"]><i></i><![endif]--\x3e"}while(o[0]);return t.documentMode&&t.all&&window.atob&&(e=10),4===e&&t.documentMode&&(e=11),e>4?e:void 0}(),be="-ms-ime-align"in document.documentElement.style,xe=/iPhone|iPod|iPad| wosbrowser\//i.test(ve),we=function(){var e,t,n=document.createElement("div");return n.contentEditable=!0,"contentEditable"in document.documentElement&&"true"===n.contentEditable&&(t=/Opera Mobi|Opera Mini/i.test(ve),/Android/i.test(ve)&&(t=!0,/Safari/.test(ve)&&(e=/Safari\/(\d+)/.exec(ve),t=!e||!e[1]||e[1]<534)),/ Silk\//i.test(ve)&&(e=/AppleWebKit\/(\d+)/.exec(ve),t=!e||!e[1]||e[1]<534),xe&&(t=/OS [0-4](_\d)+ like Mac/i.test(ve)),/Firefox/i.test(ve)&&(t=!1),/OneBrowser/i.test(ve)&&(t=!1),"UCWEB"===navigator.vendor&&(t=!1),ye<=9&&(t=!0),!t)}(),Ce=/^(https?|s?ftp|mailto|spotify|skype|ssh|teamspeak|tel):|(\/\/)|data:image\/(png|bmp|gif|p?jpe?g);/i,ke={html:'<!DOCTYPE html><html{attrs}><head><style>.ie * {min-height: auto !important} .ie table td {height:15px} @supports (-ms-ime-align:auto) { * { min-height: auto !important; } }</style><meta http-equiv="Content-Type" content="text/html;charset={charset}" /><link rel="stylesheet" type="text/css" href="{style}" /></head><body contenteditable="true" {spellcheck}><p></p></body></html>',toolbarButton:'<a class="sceditor-button sceditor-button-{name}" data-sceditor-command="{name}" unselectable="on"><div unselectable="on">{dispName}</div></a>',emoticon:'<img src="{url}" data-sceditor-emoticon="{key}" alt="{key}" title="{tooltip}" />',fontOpt:'<a class="sceditor-font-option" href="#" data-font="{font}"><font face="{font}">{font}</font></a>',sizeOpt:'<a class="sceditor-fontsize-option" data-size="{size}" href="#"><font size="{size}">{size}</font></a>',pastetext:'<div><label for="txt">{label}</label> <textarea cols="20" rows="7" id="txt"></textarea></div><div><input type="button" class="button" value="{insert}" /></div>',table:'<div><label for="rows">{rows}</label><input type="text" id="rows" value="2" /></div><div><label for="cols">{cols}</label><input type="text" id="cols" value="2" /></div><div><input type="button" class="button" value="{insert}" /></div>',image:'<div><label for="link">{url}</label> <input type="text" id="image" dir="ltr" placeholder="https://" /></div><div><label for="width">{width}</label> <input type="text" id="width" size="2" dir="ltr" /></div><div><label for="height">{height}</label> <input type="text" id="height" size="2" dir="ltr" /></div><div><input type="button" class="button" value="{insert}" /></div>',email:'<div><label for="email">{label}</label> <input type="text" id="email" dir="ltr" /></div><div><label for="des">{desc}</label> <input type="text" id="des" /></div><div><input type="button" class="button" value="{insert}" /></div>',link:'<div><label for="link">{url}</label> <input type="text" id="link" dir="ltr" placeholder="https://" /></div><div><label for="des">{desc}</label> <input type="text" id="des" /></div><div><input type="button" class="button" value="{ins}" /></div>',youtubeMenu:'<div><label for="link">{label}</label> <input type="text" id="link" dir="ltr" placeholder="https://" /></div><div><input type="button" class="button" value="{insert}" /></div>',youtube:'<iframe width="560" height="315" frameborder="0" allowfullscreen src="https://www.youtube.com/embed/{id}?wmode=opaque&start={time}" data-youtube-id="{id}"></iframe>'},Se=ye&&ye<11,Ee={bold:{exec:"bold",tooltip:"Bold",shortcut:"Ctrl+B"},italic:{exec:"italic",tooltip:"Italic",shortcut:"Ctrl+I"},underline:{exec:"underline",tooltip:"Underline",shortcut:"Ctrl+U"},strike:{exec:"strikethrough",tooltip:"Strikethrough"},subscript:{exec:"subscript",tooltip:"Subscript"},superscript:{exec:"superscript",tooltip:"Superscript"},left:{state:function(e){if(e&&3===e.nodeType&&(e=e.parentNode),e){var t="ltr"===b(e,"direction"),n=b(e,"textAlign");return"left"===n||n===(t?"start":"end")}},exec:"justifyleft",tooltip:"Align left"},center:{exec:"justifycenter",tooltip:"Center"},right:{state:function(e){if(e&&3===e.nodeType&&(e=e.parentNode),e){var t="ltr"===b(e,"direction"),n=b(e,"textAlign");return"right"===n||n===(t?"end":"start")}},exec:"justifyright",tooltip:"Align right"},justify:{exec:"justifyfull",tooltip:"Justify"},font:{_dropDown:function(e,t,n){var o=a("div");f(o,"click","a",function(t){n(x(this,"font")),e.closeDropDown(!0),t.preventDefault()}),e.opts.fonts.split(",").forEach(function(e){u(o,ee("fontOpt",{font:e},!0))}),e.createDropDown(t,"font-picker",o)},exec:function(e){var t=this;Ee.font._dropDown(t,e,function(e){t.execCommand("fontname",e)})},tooltip:"Font Name"},size:{_dropDown:function(e,t,n){var o=a("div");f(o,"click","a",function(t){n(x(this,"size")),e.closeDropDown(!0),t.preventDefault()});for(var r=1;r<=7;r++)u(o,ee("sizeOpt",{size:r},!0));e.createDropDown(t,"fontsize-picker",o)},exec:function(e){var t=this;Ee.size._dropDown(t,e,function(e){t.execCommand("fontsize",e)})},tooltip:"Font Size"},color:{_dropDown:function(e,t,n){var o=a("div"),r="",i=Ee.color;i._htmlCache||(e.opts.colors.split("|").forEach(function(e){r+='<div class="sceditor-color-column">',e.split(",").forEach(function(e){r+='<a href="#" class="sceditor-color-option" style="background-color: '+e+'" data-color="'+e+'"></a>'}),r+="</div>"}),i._htmlCache=r),u(o,O(i._htmlCache)),f(o,"click","a",function(t){n(x(this,"color")),e.closeDropDown(!0),t.preventDefault()}),e.createDropDown(t,"color-picker",o)},exec:function(e){var t=this;Ee.color._dropDown(t,e,function(e){t.execCommand("forecolor",e)})},tooltip:"Font Color"},removeformat:{exec:"removeformat",tooltip:"Remove Formatting"},cut:{exec:"cut",tooltip:"Cut",errorMessage:"Your browser does not allow the cut command. Please use the keyboard shortcut Ctrl/Cmd-X"},copy:{exec:"copy",tooltip:"Copy",errorMessage:"Your browser does not allow the copy command. Please use the keyboard shortcut Ctrl/Cmd-C"},paste:{exec:"paste",tooltip:"Paste",errorMessage:"Your browser does not allow the paste command. Please use the keyboard shortcut Ctrl/Cmd-V"},pastetext:{exec:function(e){var t,n=a("div"),o=this;u(n,ee("pastetext",{label:o._("Paste your text inside the following box:"),insert:o._("Insert")},!0)),f(n,"click",".button",function(e){t=d(n,"#txt")[0].value,t&&o.wysiwygEditorInsertText(t),o.closeDropDown(!0),e.preventDefault()}),o.createDropDown(e,"pastetext",n)},tooltip:"Paste Text"},bulletlist:{exec:function(){te(this),this.execCommand("insertunorderedlist")},tooltip:"Bullet list"},orderedlist:{exec:function(){te(this),this.execCommand("insertorderedlist")},tooltip:"Numbered list"},indent:{state:function(e,t){var n,o,r;return w(t,"li")?0:w(t,"ul,ol,menu")&&(n=this.getRangeHelper().selectedRange(),o=n.startContainer.parentNode,r=n.endContainer.parentNode,o!==o.parentNode.firstElementChild||w(r,"li")&&r!==r.parentNode.lastElementChild)?0:-1},exec:function(){var e=this,t=e.getRangeHelper().getFirstBlockParent();e.focus(),c(t,"ul,ol,menu")&&e.execCommand("indent")},tooltip:"Add indent"},outdent:{state:function(e,t){return c(t,"ul,ol,menu")?0:-1},exec:function(){c(this.getRangeHelper().getFirstBlockParent(),"ul,ol,menu")&&this.execCommand("outdent")},tooltip:"Remove one indent"},table:{exec:function(e){var t=this,n=a("div");u(n,ee("table",{rows:t._("Rows:"),cols:t._("Cols:"),insert:t._("Insert")},!0)),f(n,"click",".button",function(e){var o=Number(d(n,"#rows")[0].value),r=Number(d(n,"#cols")[0].value),i="<table>";o>0&&r>0&&(i+=Array(o+1).join("<tr>"+Array(r+1).join("<td>"+(Se?"":"<br />")+"</td>")+"</tr>"),i+="</table>",t.wysiwygEditorInsertHtml(i),t.closeDropDown(!0),e.preventDefault())}),t.createDropDown(e,"inserttable",n)},tooltip:"Insert a table"},horizontalrule:{exec:"inserthorizontalrule",tooltip:"Insert a horizontal rule"},code:{exec:function(){this.wysiwygEditorInsertHtml("<code>",(Se?"":"<br />")+"</code>")},tooltip:"Code"},image:{_dropDown:function(e,t,n,o){var r=a("div");u(r,ee("image",{url:e._("URL:"),width:e._("Width (optional):"),height:e._("Height (optional):"),insert:e._("Insert")},!0));var i=d(r,"#image")[0];i.value=n,f(r,"click",".button",function(t){i.value&&o(i.value,d(r,"#width")[0].value,d(r,"#height")[0].value),e.closeDropDown(!0),t.preventDefault()}),e.createDropDown(t,"insertimage",r)},exec:function(e){var t=this;Ee.image._dropDown(t,e,"",function(e,n,o){var r="";n&&(r+=' width="'+n+'"'),o&&(r+=' height="'+o+'"'),t.wysiwygEditorInsertHtml("<img"+r+' src="'+e+'" />')})},tooltip:"Insert an image"},email:{_dropDown:function(e,t,n){var o=a("div");u(o,ee("email",{label:e._("E-mail:"),desc:e._("Description (optional):"),insert:e._("Insert")},!0)),f(o,"click",".button",function(t){var r=d(o,"#email")[0].value;r&&n(r,d(o,"#des")[0].value),e.closeDropDown(!0),t.preventDefault()}),e.createDropDown(t,"insertemail",o)},exec:function(e){var t=this;Ee.email._dropDown(t,e,function(e,n){t.focus(),!t.getRangeHelper().selectedHtml()||n?t.wysiwygEditorInsertHtml('<a href="mailto:'+e+'">'+(n||e)+"</a>"):t.execCommand("createlink","mailto:"+e)})},tooltip:"Insert an email"},link:{_dropDown:function(e,t,n){function o(t){i.value&&n(i.value,d(r,"#des")[0].value),e.closeDropDown(!0),t.preventDefault()}var r=a("div");u(r,ee("link",{url:e._("URL:"),desc:e._("Description (optional):"),ins:e._("Insert")},!0));var i=d(r,"#link")[0];f(r,"click",".button",o),f(r,"keypress",function(e){13===e.which&&i.value&&o(e)},me),e.createDropDown(t,"insertlink",r)},exec:function(e){var t=this;Ee.link._dropDown(t,e,function(e,n){t.focus(),n||!t.getRangeHelper().selectedHtml()?(n=n||e,t.wysiwygEditorInsertHtml('<a href="'+e+'">'+n+"</a>")):t.execCommand("createlink",e)})},tooltip:"Insert a link"},unlink:{state:function(){return c(this.currentNode(),"a")?0:-1},exec:function(){var e=c(this.currentNode(),"a");if(e){for(;e.firstChild;)S(e.firstChild,e);s(e)}},tooltip:"Unlink"},quote:{exec:function(e,t,n){var o="<blockquote>",r="</blockquote>";t?(n=n?"<cite>"+n+"</cite>":"",o=o+n+t+r,r=null):""===this.getRangeHelper().selectedHtml()&&(r=(Se?"":"<br />")+r),this.wysiwygEditorInsertHtml(o,r)},tooltip:"Insert a Quote"},emoticon:{exec:function(e){},tooltip:"Insert an emoticon"},youtube:{_dropDown:function(e,t,n){var o=a("div");u(o,ee("youtubeMenu",{label:e._("Video URL:"),insert:e._("Insert")},!0)),f(o,"click",".button",function(t){var i=d(o,"#link")[0].value,a=i.match(/(?:v=|v\/|embed\/|youtu.be\/)(.{11})/),l=i.match(/[&|?](?:star)?t=((\d+[hms]?){1,3})/),c=0;l&&r(l[1].split(/[hms]/),function(e,t){""!==t&&(c=60*c+Number(t))}),a&&/^[a-zA-Z0-9_\-]{11}$/.test(a[1])&&n(a[1],c),e.closeDropDown(!0),t.preventDefault()}),e.createDropDown(t,"insertlink",o)},exec:function(e){var t=this;Ee.youtube._dropDown(t,e,function(e,n){t.wysiwygEditorInsertHtml(ee("youtube",{id:e,time:n}))})},tooltip:"Insert a YouTube video"},date:{_date:function(e){var t=new Date,n=t.getYear(),o=t.getMonth()+1,r=t.getDate();return n<2e3&&(n=1900+n),o<10&&(o="0"+o),r<10&&(r="0"+r),e.opts.dateFormat.replace(/year/i,n).replace(/month/i,o).replace(/day/i,r)},exec:function(){this.insertText(Ee.date._date(this))},txtExec:function(){this.insertText(Ee.date._date(this))},tooltip:"Insert current date"},time:{_time:function(){var e=new Date,t=e.getHours(),n=e.getMinutes(),o=e.getSeconds();return t<10&&(t="0"+t),n<10&&(n="0"+n),o<10&&(o="0"+o),t+":"+n+":"+o},exec:function(){this.insertText(Ee.time._time())},txtExec:function(){this.insertText(Ee.time._time())},tooltip:"Insert current time"},ltr:{state:function(e,t){return t&&"ltr"===t.style.direction},exec:function(){var e=this,t=e.getRangeHelper(),n=t.getFirstBlockParent();if(e.focus(),n&&!w(n,"body")||(e.execCommand("formatBlock","p"),(n=t.getFirstBlockParent())&&!w(n,"body"))){b(n,"direction","ltr"===b(n,"direction")?"":"ltr")}},tooltip:"Left-to-Right"},rtl:{state:function(e,t){return t&&"rtl"===t.style.direction},exec:function(){var e=this,t=e.getRangeHelper(),n=t.getFirstBlockParent();if(e.focus(),n&&!w(n,"body")||(e.execCommand("formatBlock","p"),(n=t.getFirstBlockParent())&&!w(n,"body"))){b(n,"direction","rtl"===b(n,"direction")?"":"rtl")}},tooltip:"Right-to-Left"},print:{exec:"print",tooltip:"Print"},maximize:{state:function(){return this.maximize()},exec:function(){this.maximize(!this.maximize())},txtExec:function(){this.maximize(!this.maximize())},tooltip:"Maximize",shortcut:"Ctrl+Shift+M"},source:{state:function(){return this.sourceMode()},exec:function(){this.toggleSourceMode()},txtExec:function(){this.toggleSourceMode()},tooltip:"View source",shortcut:"Ctrl+Shift+S"},ignore:{}},Te={};ne.plugins=Te;var De=ye&&ye<11,Me=function(e,t,n){var o,r,i,a,l,c="",s=e.startContainer,u=e.startOffset;for(s&&3!==s.nodeType&&(s=s.childNodes[u],u=0),i=a=u;n>c.length&&s&&3===s.nodeType;)o=s.nodeValue,r=n-c.length,l&&(a=o.length,i=0),l=s,t?(i=Math.max(a-r,0),u=i,c=o.substr(i,a-i)+c,s=l.previousSibling):(a=Math.min(r,o.length),u=i+a,c+=o.substr(i,a),s=l.nextSibling);return{node:l||s,offset:u,text:c}},Ne=window,Re=document,Fe=ye,He=Fe&&Fe<11,_e=/^image\/(p?jpe?g|gif|png|bmp)$/i;ae.locale={},ae.formats={},ae.icons={},ae.command={get:function(e){return Ee[e]||null},set:function(e,t){return!(!e||!t)&&(t=n(Ee[e]||{},t),t.remove=function(){ae.command.remove(e)},Ee[e]=t,this)},remove:function(e){return Ee[e]&&delete Ee[e],this}},window.sceditor={command:ae.command,commands:Ee,defaultOptions:ge,ie:ye,ios:xe,isWysiwygSupported:we,regexEscape:J,escapeEntities:Q,escapeUriScheme:Z,dom:{css:b,attr:m,removeAttr:h,is:w,closest:c,width:R,height:F,traverse:A,rTraverse:B,parseHTML:O,hasStyling:I,convertElement:L,blockLevelList:he,canHaveChildren:P,isInline:V,copyCSS:W,fixNesting:j,findCommonAncestor:q,getSibling:U,removeWhiteSpace:$,extractContents:Y,getOffset:K,getStyle:X,hasStyle:G},locale:ae.locale,icons:ae.icons,utils:{each:r,isEmptyObject:t,extend:n},plugins:ne.plugins,formats:ae.formats,create:function(e,t){t=t||{},l(e,".sceditor-container")||(t.runWithoutWysiwygSupport||we)&&new ae(e,t)},instance:function(e){return e._sceditor}}}();

<template>
  <!-- eslint-disable vue/no-v-html -->
  <div class="card-recommend hover">
    <el-image :src="img" lazy></el-image>
    <div class="mask">
      <!-- <div class="title text-hide-2" :title="title">{{ title }}</div> -->
      <div class="title text-hide-2" :title="title" v-html="title"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    aid: {
      type: Number,
      default: 0
    },
    img: {
      type: String,
      default: '',
      required: true
    },
    title: {
      type: String,
      default: '',
      required: true
    }
    // url: {
    //   type: String,
    //   required: true
    // }
  }
};
</script>
<style lang="scss" scoped>
.card-recommend {
  display: block;
  position: relative;
  width: 200px;
  height: 125px;
  overflow: hidden;
  border-radius: 5px;
  .card-image {
    width: 100%;
    object-fit: cover;
  }
  .mask {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 60px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  }
  .title {
    position: absolute;
    left: 0;
    bottom: 4px;
    font-size: 13px;
    color: white;
    padding: 0 7px;
    line-height: 1.4;
  }
}
</style>

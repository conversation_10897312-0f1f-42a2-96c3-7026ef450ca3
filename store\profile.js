export const state = () => ({
  user: {}, // 用户信息
  articles: [], // 文章
  articles_paginate: {}, // 文章分页
  books: [], // 图书
  books_paginate: [] // 图书
});

export const mutations = {
  setArticles(state, data) {
    state.articles = data.list;
    state.articles_paginate = data.page_info || false;
  },
  setBooks(state, data) {
    state.books = data.list;
    state.books_paginate = data.page_info || false;
  },
  setUserinfo(state, user) {
    if (typeof user.followed === 'undefined') {
      state.user.followed = false;
    }
    state.user = user;
  },
  setUserByKey(state, { key, val }) {
    state[key] = val;
  }
};

export const actions = {
  // 获取文章|图书数据
  async getProfileData({ commit }, { uid, page, type }) {
    const res = await this.$axios.$post('/api/user/get-articles', {
      uid,
      page,
      type
    });

    if (res.code === 0) {
      // type: 0所有, 1图书（小说和漫画）
      if (type === 0) {
        commit('setArticles', res.data);
      } else {
        commit('setBooks', res.data);
      }
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 获取用户信息
  async getUserinfo({ commit }, { uid }) {
    const res = await this.$axios.$post('/api/user/info', { uid });
    if (res.code === 0) {
      commit('setUserinfo', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  }
};

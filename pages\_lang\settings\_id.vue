<template>
  <div>
    <!-- 勋章 dialog -->
    <el-dialog
      :title="$t('settings.got_medal')"
      :visible="showMedalDialog"
      :close-on-click-modal="false"
      width="500px"
      class="medal-dialog"
      @close="toggleMedalDialog(false)"
    >
      <div class="medal-items-container flex flex-wrap">
        <!-- 循环勋章 -->
        <div
          v-for="(item, key) in medals.list"
          :key="key"
          :class="{ active: selectedMedal.includes(item.medal_id) }"
          class="medal-item btn mar-bottom-20 flex flex-cols ai-center jc-between"
          @click="selectMedals(item.medal_id)"
        >
          <div class="img-box">
            <img class="item-medal-image mar-right-10" :src="item.img" />
            <span class="current-icon fs-xs bg-primary hide text-white">
              {{ $t('settings.current') }}
            </span>
          </div>
          <p class="text-hide-1 text-gray" :title="item.name">
            {{ item.name }}
          </p>
        </div>
        <Loading v-show="dialogMedalLoading" />

        <!-- 底部分页 -->
        <div v-if="medals.totalCount > 0" slot="footer" class="flex jc-center ai-center" style="width: 100%">
          <el-pagination
            class="my-pagination"
            layout="pager"
            :prev-text="$t('paginate.prev')"
            :next-text="$t('paginate.next')"
            :hide-on-single-page="true"
            :disabled="dialogMedalLoading"
            :current-page="medals.currentPage"
            :total="medals.totalCount"
            :page-size="15"
            style="padding-bottom: 18px;"
            @current-change="handleChangeMedalsPage"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 用户信息栏 -->
    <div class="user-info-bar-container flex flex-cols">
      <div class="flex-1 flex user-infos">
        <!-- 左边: 用户头像 -->
        <div class="avatar-box">
          <user-avatar :width="105" :avatar-img="loginUser.avatar" />
        </div>

        <!-- 右边: 用户信息 -->
        <div class="info-box pad-left-15 flex-1 flex flex-cols">
          <!-- 用户名称和UID -->
          <div class="info-item">
            <span class="fs-ml">{{ loginUser.nickname }}</span>
            <span class="mar-left-15 text-gray fs-xs"> UID: {{ loginUser.uid }} </span>
          </div>

          <!-- 用户等级经验 -->
          <div class="flex-1 flex flex-cols user-experience">
            <div class="flex jc-between ai-start levels">
              <span v-for="(item, key) in userinfo.all_level" :key="key" class="fs-xs text-gray">
                {{ item.name }}
              </span>
            </div>
            <div class="progress flex ai-center">
              <div class="progress-bar flex bg-gray-white flex-1">
                <p :style="{ width: `${lvExps.progress}%` }" class="cuurret-progress"></p>
              </div>
              <p class="progress-counter fs-xs text-gray text-right">{{ lvExps.currentExp }}/{{ lvExps.maxExp }}</p>
            </div>
          </div>

          <!-- 其他信息 -->
          <div class="info-item fs-xs flex ai-center other-infos">
            <!-- 等级 -->
            <p class="item flex-inline ai-center">
              <span>{{ $t('settings.user_group') }}:&nbsp;</span>
              <span class="text-primary">{{ loginUser.level.name }}</span>
            </p>

            <!-- 金币 -->
            <p class="item flex-inline ai-center">
              <svg class="svg-icon coin" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 755 752.97">
                <path
                  d="M672.66,135.74C526.92-10,284-10,138.29,135.74s-145.73,388.63,0,534.37,388.63,145.73,534.37,0,151.13-388.63,0-534.37ZM494.54,497.38a575.12,575.12,0,0,0-56.78,66.09,38.2,38.2,0,0,1-61,1.11c-17.48-22.4-38-44.8-60.37-67.2-22.54-22.53-45.08-43.15-67.62-60.7-20.15-15.7-19.34-46.35,1.44-61.21a525.18,525.18,0,0,0,66.18-56.2,708.39,708.39,0,0,0,59.29-68.1,38.18,38.18,0,0,1,61.18.82,574.75,574.75,0,0,0,57.65,67.28,575.15,575.15,0,0,0,65.18,56.11,38.21,38.21,0,0,1,0,61.48,415.9,415.9,0,0,0-65.23,60.52Zm0,0"
                  transform="translate(-28.99 -26.44)"
                />
              </svg>
              <span>&nbsp;</span>
              <span>{{ loginUser.balance.coin }}</span>
            </p>

            <!-- 勋章 -->
            <p class="item flex-inline ai-center">
              <span class="mar-right-5"> {{ $t('settings.medal') }}:&nbsp; </span>
              <el-image
                v-for="item in loginUser.medals"
                :key="item.medal_id"
                :src="item.img"
                lazy
                fit="contain"
                class="item-medal-image mar-right-5"
                :title="item.name"
              ></el-image>
              <button v-if="loginUser.medals.length > 0 || medals.totalCount > 0" class="btn text-primary" @click="toggleMedalDialog(true)">
                {{ $t('settings.more') }}
              </button>
              <span v-else>
                {{ $t('settings.goto') }}
                <button class="btn text-primary" @click="toPage(`/settings/medal_center`)">
                  {{ $t('settings_layout.medal_center') }}
                </button>
              </span>
            </p>
          </div>
        </div>
      </div>
      <p class="line"></p>
    </div>

    <!-- ------------ 底部信息Tabbar ---------------- -->
    <div class="tabbars-container">
      <!-- ------------我的发帖------------ -->
      <div class="my-tabbar-item">
        <TitleTabbar
          :title="$t('settings.my_articles')"
          all-count="16"
          book-count="15"
          @click-more="toPage(`/publish_mgr/article/${loginUser.uid}`, 'push')"
          @tab-change="articleTabChange"
        >
          <template v-slot:articles>
            <div v-for="(article, key) in publish_article_articles_10" :key="key" class="tabbar-article-item flex-inline">
              <CardItem
                :img="article.cover"
                :title="article.title"
                :comments="article.comments"
                :views="article.hits"
                :has-icon="article.sid !== 0"
                :gid="article.parent_gid"
                @click="toPage(`/detail/${article.aid}`)"
                @click1="toPage(`/themereply/${article.aid}`)"
              />
            </div>

            <!-- 如果没有帖子显示: 还没有发表过帖子, 立即发帖 -->
            <!-- 加载发布管理loading -->
            <Loading v-show="publishLoading" />
            <div
              v-show="!publishLoading && publish_article_articles_10.length === 0"
              class="empty-article-tips flex ai-center jc-center fs-sm text-gray"
            >
              <span>{{ $t('settings.not_published_and') }}</span>
              <nuxt-link target="_blank" class="btn btn-sm tdn flex ai-center jc-center btn-primary mar-left-5" :to="$i18n.path(`write`)">
                <span>{{ $t('settings.publish') }}</span>
              </nuxt-link>
            </div>
          </template>
          <template v-slot:books>
            <div v-if="publish_article_books_10.length">
              <div v-for="(item, key) in publish_article_books_10" :key="key" class="tabbar-article-item flex-inline jc-center">
                <CardItem
                  :img="item.cover"
                  :has-icon="item.sid > 0"
                  mode="horizontal"
                  :gid="item.parent_gid"
                  @click="toPage(`/detail/${item.aid}`)"
                  @click1="toPage(`/themereply/${item.aid}`)"
                >
                  <template v-slot:title>
                    <span>{{ item.title }}</span>
                  </template>
                  <div class="book-info flex">
                    <span class="flex-1 text-hide-1">{{ item.group_name }}</span>
                    <span class="flex-1 text-right">
                      {{ item.time | date2short }}
                    </span>
                  </div>
                </CardItem>
              </div>
            </div>
            <!-- 暂无数据 -->
            <p v-show="publish_article_books_10.length === 0" class="empty-article-tips flex ai-center jc-center fs-sm text-gray">
              {{ $t('settings.no_publish') }}
            </p>
          </template>
        </TitleTabbar>
      </div>

      <!-- ------------我的收藏------------ -->
      <div class="my-tabbar-item">
        <TitleTabbar
          :title="$t('settings.my_collects')"
          all-count="16"
          book-count="15"
          @click-more="toPage(`/collection/article/${loginUser.uid}`, 'push')"
          @tab-change="collectionTabChange"
        >
          <template v-slot:articles>
            <!-- 如果没有帖子显示: 尚未收藏任何图书 -->
            <div v-for="(collect, key) in fav_article_articles_10" :key="key" class="tabbar-article-item flex-inline jc-center">
              <CardItem
                :img="collect.cover"
                :title="collect.title"
                :comments="collect.comments"
                :views="collect.hits"
                :has-icon="collect.sid > 0"
                :gid="collect.parent_gid"
                @click="toPage(`/detail/${collect.aid}`)"
                @click1="toPage(`/themereply/${collect.aid}`)"
              />
            </div>

            <!-- 加载合集loading -->
            <Loading v-show="collectionLoading" />
            <p
              v-show="!collectionLoading && fav_article_articles_10.length === 0"
              class="empty-article-tips flex ai-center jc-center fs-sm text-gray"
            >
              {{ $t('settings.no_collects') }}
            </p>
          </template>
          <template v-slot:books>
            <div v-if="fav_article_books_10.length">
              <div v-for="(item, key) in fav_article_books_10" :key="key" class="tabbar-article-item flex-inline jc-center">
                <CardItem
                  :img="item.cover"
                  :title="item.title"
                  mode="horizontal"
                  :has-icon="item.sid > 0"
                  :gid="item.parent_gid"
                  @click="toPage(`/detail/${item.aid}`)"
                  @click1="toPage(`/themereply/${item.aid}`)"
                >
                  <div class="book-info flex">
                    <span class="flex-1 text-hide-1">{{ item.group_name }}</span>
                    <span class="flex-1 text-right">
                      {{ item.time | date2short }}
                    </span>
                  </div>
                </CardItem>
              </div>
            </div>

            <!-- 如果没有数据显示: 暂无数据 -->
            <p v-show="fav_article_books_10.length === 0" class="empty-article-tips flex ai-center jc-center fs-sm text-gray">
              {{ $t('settings.no_collects') }}
            </p>
          </template>
        </TitleTabbar>
      </div>

      <!-- ------------我的历史------------ -->
      <div class="my-tabbar-item">
        <TitleTabbar
          :title="$t('settings.my_history')"
          all-count="16"
          book-count="15"
          @click-more="toPage(`/history/article/${loginUser.uid}`, 'push')"
          @tab-change="historyTabChange"
        >
          <template v-slot:articles>
            <div v-if="history_article_articles_10.length">
              <div v-for="(history, key) in history_article_articles_10" :key="key" class="tabbar-article-item flex-inline jc-center">
                <CardItem
                  :img="history.cover"
                  :title="history.title"
                  :comments="history.comments"
                  :views="history.hits"
                  :has-icon="history.sid !== 0"
                  :gid="history.parent_gid"
                  @click="toPage(`/detail/${history.aid}`)"
                  @click1="toPage(`/themereply/${history.aid}`)"
                />
              </div>
            </div>

            <!-- 如果没有帖子显示: 尚未浏览任何文章 -->
            <!-- 加载历史记录loading -->
            <Loading v-if="historyLoading && history_article_articles_10.length === 0" />
            <p v-if="!historyLoading && history_article_articles_10.length === 0" class="empty-article-tips flex ai-center jc-center fs-sm text-gray">
              {{ $t('settings.no_history') }}
            </p>
          </template>
          <template v-slot:books>
            <div v-if="history_article_books_10.length">
              <div v-for="(item, index) in history_article_books_10" :key="index" class="tabbar-article-item flex-inline jc-center">
                <CardItem
                  :img="item.cover"
                  :has-icon="item.sid > 0"
                  :title="item.title"
                  mode="horizontal"
                  :gid="item.parent_gid"
                  @click="toPage(`/detail/${item.aid}`)"
                  @click1="toPage(`/themereply/${item.aid}`)"
                >
                  <div class="book-info flex">
                    <span class="flex-1 text-hide-1">{{ item.group_name }}</span>
                    <span class="flex-1 text-right">
                      {{ item.time | date2short }}
                    </span>
                  </div>
                </CardItem>
              </div>
            </div>

            <p v-show="history_article_books_10.length === 0" class="empty-article-tips flex ai-center jc-center fs-sm text-gray">
              {{ $t('settings.no_history') }}
            </p>
          </template>
        </TitleTabbar>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import UserAvatar from '@/components/UserAvatar.vue';
import TitleTabbar from '@/components/TitleTabbar.vue';
import CardItem from '@/components/CardItem.vue';
import Loading from '@/components/Loading.vue';

export default {
  layout: 'settings',
  middleware: ['auth'],
  components: {
    Loading,
    UserAvatar,
    TitleTabbar,
    CardItem
  },

  async fetch({ store, params }) {
    const uid = params.id;
    await store.dispatch('userPublish/getPublishArticleBooks', { uid });
    // await store.dispatch('userPublish/getPublishArticleArticles', { uid });

    await store.dispatch('userHistory/getHistoryhArticleBooks', { uid });
    // await store.dispatch('userHistory/getHistoryhArticleArticles', { uid });

    await store.dispatch('userFavorite/getFavArticleBooks', { uid });
    // await store.dispatch('userFavorite/getFavArticleArticles', { uid });
  },

  async asyncData({ store, params, $axios }) {
    const uid = params.id;
    const { data: userinfo } = await $axios.$post('/api/user/info', { uid });
    if (!userinfo.all_level) {
      userinfo.all_level = [];
    }
    return {
      userinfo
    };
  },

  data: () => ({
    publishLoading: false, // 记载发布管理loading
    historyLoading: false, // 加载历史loading
    collectionLoading: false, // 加载收藏 loading
    dialogMedalLoading: false, // 加载勋章弹窗 loading
    userinfo: {
      // 用户详细信息
      uid: 0,
      nickname: '',
      avatar: '',
      passer: 0,
      gender: 0,
      sign: '',
      status: 0,
      banner: '',
      ban_end_date: '',
      following: 0,
      comments: 0,
      favorites: 0,
      articles: 0,
      followers: 0,
      level: { exp: 0, level: 0, name: '', next_exp: '' },
      all_level: [],
      security_key: '',
      balance: { coin: 0, balance: 0 }
    },
    showMedalDialog: false, // 是否显示勋章 dialog
    selectedMedalOrig: [], // 原已选择勋章
    selectedMedal: [], // 最多可以选择10个
    availableMedals: [], // 用户可用的勋章
    medals: {}, // 当前已经获得勋章列表
    loading: false // 是否正在提交勋章
  }),

  computed: {
    ...mapGetters('login', ['loginUser']),
    ...mapGetters('userPublish', ['publish_article_articles_10', 'publish_article_books_10']),
    ...mapGetters('userHistory', ['history_article_articles_10', 'history_article_books_10']),
    ...mapGetters('userFavorite', ['fav_article_articles_10', 'fav_article_books_10']),
    lvExps() {
      const lvs = this.userinfo.all_level;
      const data = {
        maxExp: 0,
        currentExp: 0,
        progress: 0
      };
      if (lvs && lvs.length) {
        data.maxExp = lvs[lvs.length - 1].exp;
        data.currentExp = this.userinfo.level.exp;
        if (data.currentExp >= data.maxExp) {
          data.progress = 100;
        } else {
          data.progress = (this.userinfo.level.level + 1) * 10 - 5;
        }
        return data;
      }
      return data;
    }
  },

  created() {
    this.getUserMedals();
  },
  methods: {
    // 文章 Change
    articleTabChange(i) {
      const uid = this.userinfo.uid;
      if (i === 1 && this.publish_article_articles_10.length === 0) {
        this.publishLoading = true;
        // this.$store.dispatch('userPublish/getPublishArticleBooks', { uid }).finally(() => (this.publishLoading = false));
        this.$store.dispatch('userPublish/getPublishArticleArticles', { uid }).finally(() => (this.publishLoading = false));
      }
    },

    // 历史 tabChange
    historyTabChange(i) {
      const uid = this.userinfo.uid;
      if (i === 1 && this.history_article_articles_10.length === 0) {
        this.historyLoading = true;
        // this.$store.dispatch('userHistory/getHistoryhArticleBooks', { uid }).finally(() => (this.historyLoading = false));
        this.$store.dispatch('userHistory/getHistoryhArticleArticles', { uid }).finally(() => (this.historyLoading = false));
      }
    },

    // 收藏切换
    collectionTabChange(i) {
      const uid = this.userinfo.uid;
      if (i === 1 && this.fav_article_articles_10.length === 0) {
        this.collectionLoading = true;
        // this.$store.dispatch('userFavorite/getFavArticleBooks', { uid }).finally(() => (this.collectionLoading = false));
        this.$store.dispatch('userFavorite/getFavArticleArticles', { uid }).finally(() => (this.collectionLoading = false));
      }
    },

    // 切换更多勋章的显示和隐藏
    async toggleMedalDialog(visible) {
      // 正在加载dialog不允许关闭
      if (this.dialogMedalLoading) {
        this.$message.info(this.$t('settings.waiting'));
        return;
      }

      // 关闭的时候提交修改
      if (!visible) {
        this.showMedalDialog = visible;

        // 符合条件才提交修改
        if (
          this.selectedMedal.length >= 0 &&
          this.selectedMedal.length <= 10 &&
          JSON.stringify(this.selectedMedalOrig) !== JSON.stringify(this.selectedMedal) // 比较用户是否有修改过勋章，没改过直接关闭窗口
        ) {
          this.loading = true;
          this.$axios
            .$post('/api/user/equip-medal', { ids: this.selectedMedal })
            .then((res) => {
              this.selectedMedal = [];
              if (res.code === 0) {
                this.$store.dispatch('login/refreshUserinfo');
                this.$message.success(this.$t('settings.equip_medal_succeed'));
              } else {
                this.$message.error(this.$t('settings.equip_medal_error'));
              }
            })
            .finally(() => (this.loading = false));
        }
        return false;
      } else {
        // 正在提交不允许修改
        if (this.loading === true) {
          this.$message.info(this.$t('settings.waiting'));
          return;
        }

        this.showMedalDialog = visible;

        await this.getUserMedals();
      }
    },

    // 获取用户可用勋章列表
    async getUserMedals() {
      this.dialogMedalLoading = true;

      this.medals = [];
      this.availableMedals = [];
      this.selectedMedal = [];

      const res = await this.$axios.$post('/api/user/medals');
      if (res.code === 0) {
        this.availableMedals = res.data;
        await this.availableMedals.forEach((item) => {
          if (item.equip) this.selectMedals(item.medal_id);
        });

        this.selectedMedalOrig = [...this.selectedMedal]; // 保存用户原来装备的勋章
        this.medals = this.generateMedallist(1);

        this.dialogMedalLoading = false;
      }
    },

    // 选择勋章
    selectMedals(medal) {
      if (this.selectedMedal.includes(medal)) {
        this.selectedMedal = this.selectedMedal.filter((i) => i !== medal);
        return;
      }
      if (this.selectedMedal.length === 10) {
        this.$message.error(this.$t('settings.max_select_10'));
        return;
      }
      this.selectedMedal.push(medal);
    },

    // 生成用户可用勋章页面的数据
    generateMedallist(page) {
      const pageSize = 15;
      const data = {};

      data.list = this.pagination(page, pageSize, this.availableMedals);
      data.currentPage = page;
      data.totalCount = this.availableMedals.length;
      return data;
    },

    // 获取手动分页数据
    pagination(currentPage, pageSize, array) {
      const offset = (currentPage - 1) * pageSize;
      return offset + pageSize >= array.length ? array.slice(offset, array.length) : array.slice(offset, offset + pageSize);
    },

    // 切换勋章分页
    handleChangeMedalsPage(page) {
      this.medals = this.generateMedallist(page);
    }
  },

  head() {
    const title = this.$t('settings.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
.item-medal-image {
  width: 100%;
  height: 100%;
}

// 勋章 dialog
.medal-dialog {
  // override element-ui
  .el-dialog__body {
    padding: 25px 20px;
  }

  // item
  .medal-item {
    height: 100px;
    width: 92px;
    &.active {
      .img-box {
        border: 1px solid $primary;
        .current-icon {
          visibility: visible !important;
        }
      }
    }
    .img-box {
      position: relative;
      height: 75px;
      width: 75px;
      border: 1px solid $gray-white;
      padding: 10px;
      border-radius: 5px;
      overflow: hidden;
      .item-medal-image {
        object-fit: contain;
      }
      .current-icon {
        padding: 2px 5px;
        border-radius: 2px;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }
}

// 用户信息-
.user-info-bar-container {
  height: 180px;
  .line {
    height: 10px;
    background: $gray-f7;
  }
  .user-infos {
    padding: 22px 35px 33px 15px;
    .avatar-box {
      width: 105px;
      height: 100%;
    }
    .info-box {
      height: 100%;
      .info-item {
        height: 20px;
        line-height: 20px;
      }

      // 等级 && 经验条
      .user-experience {
        padding: 20px 0;
        $exp-counter-width: 100px;
        $lv-height: 20px;
        .levels {
          height: $lv-height;
          padding-right: $exp-counter-width;
        }
        .progress {
          width: 100%;
          .progress-bar {
            height: $lv-height/2;
            position: relative;
            .cuurret-progress {
              position: absolute;
              top: 0;
              left: 0;
              height: 100%;
              background: $primary;
            }
          }
          .progress-counter {
            width: $exp-counter-width;
          }
        }
      }

      // 底部其他信息
      .other-infos {
        .item {
          margin-right: 40px;
          height: 100%;
          .svg-icon {
            height: 13px;
            &.coin {
              fill: #fadd52;
            }
          }
          .item-medal-image {
            width: 30px;
            height: 30px;
          }
        }
      }
    }
  }
}

.tabbars-container {
  padding: 15px 0;
  .tabbar-article-item {
    .book-info {
      width: 100%;
    }
  }
  .my-tabbar-item {
    padding: 15px;
    .empty-article-tips {
      height: 100px;
      .btn {
        width: 70px;
        height: 30px;
        border-radius: 30px;
      }
    }
  }
}
</style>

<template>
  <div class="bg-gray-f7">
    <Nav />
    <div class="layout-container containner flex jc-between">
      <!-- 左侧: 侧边栏 -->
      <div class="sidebar">
        <!-- 头像 -->
        <div class="side-item user-avatar-box flex flex-cols ai-center jc-center">
          <user-avatar :avatar-img="loginUser.avatar" />
          <p class="username">
            <span class="fs-sm text-dark">{{ loginUser.nickname }}</span>
            <span class="lv-icon fs-xs" :class="[`lv-${loginUser.level.level}`]">
              {{ loginUser.level.name }}
            </span>
          </p>
          <p class="sign flex">
            <span class="text-hide-1 fs-xs text-gray">
              {{ loginUser.sign }}
            </span>
            <!-- <svg
              class="svg-icon btn"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 569.33 566.6"
              @click="toPage('/setttings/infos')"
            >
              <path
                d="M647.4,410.4A34.61,34.61,0,0,0,612.8,445V569.6a44.06,44.06,0,0,1-44,44H228.6a44.06,44.06,0,0,1-44-44V229.4a44.06,44.06,0,0,1,44-44H407.1a34.6,34.6,0,0,0,0-69.2H228.6A113.35,113.35,0,0,0,115.4,229.4V569.6A113.35,113.35,0,0,0,228.6,682.8H568.8A113.35,113.35,0,0,0,682,569.6V445A34.61,34.61,0,0,0,647.4,410.4Z"
                transform="translate(-115.4 -116.2)"
              />
              <path
                d="M382.9,513l-62.3,20.1a10.38,10.38,0,0,1-13.3-12.3l15.2-63.7a11.31,11.31,0,0,1,2.5-4.7L611,143.3a42.52,42.52,0,0,1,60.1-2.3h0a42.52,42.52,0,0,1,2.3,60.1l-286,309.1A10.68,10.68,0,0,1,382.9,513Z"
                transform="translate(-115.4 -116.2)"
              />
            </svg> -->
          </p>
        </div>

        <!-- 分割线 -->
        <p class="side-item line"></p>

        <!-- 关注信息 -->
        <div class="side-item user-infos flex">
          <div class="infos-item flex-1 flex flex-cols jc-around ai-center hover" @click="toFansPage(loginUser.uid, true)">
            <span class="text-primary fs-sm">{{ loginUser.followers }}</span>
            <span class="text-gray fs-xs">
              {{ $t('components.profile_side.fans') }}
            </span>
          </div>
          <div class="infos-item flex-1 flex flex-cols jc-around ai-center hover" @click="toFansPage(loginUser.uid, false)">
            <span class="text-primary fs-sm">{{ loginUser.following }}</span>
            <span class="text-gray fs-xs">
              {{ $t('components.profile_side.follow') }}
            </span>
          </div>
          <div class="infos-item flex-1 flex flex-cols jc-around ai-center hover" @click="toPage(`/profile/${loginUser.uid}`)">
            <span class="text-primary fs-sm">{{ loginUser.articles }}</span>
            <span class="text-gray fs-xs">
              {{ $t('components.profile_side.publish') }}
            </span>
          </div>
        </div>

        <!-- 分割线 -->
        <p class="side-item line"></p>

        <!-- 菜单 -->
        <ul class="sidebar-menus fs-md">
          <!-- 首页 -->
          <li class="menu-item">
            <img class="item-img" src="@/assets/images/home.png" />
            <nuxt-link :to="$i18n.path(`settings/${loginUser.uid}`)" class="item-text" :class="{ active: $route.name.includes('settings-id') }">
              {{ $t('settings_layout.home') }}
            </nuxt-link>
          </li>

          <!-- 发布管理 -->
          <li class="menu-item">
            <img class="item-img" src="@/assets/images/release.png" />
            <span>{{ $t('settings_layout.publish_mgr') }}</span>
            <ul class="sub-menu fs-sm">
              <li class="sub-menu-item text-gray">
                <nuxt-link
                  :to="$i18n.path(`publish_mgr/article/${loginUser.uid}`)"
                  class="item-text"
                  :class="{ active: $route.name.includes('publish_mgr-article-id') }"
                >
                  {{ $t('settings_layout.article') }}
                </nuxt-link>
              </li>
              <li class="sub-menu-item text-gray">
                <nuxt-link
                  :to="$i18n.path(`publish_mgr/series/${loginUser.uid}`)"
                  class="item-text"
                  :class="{ active: $route.name.includes('publish_mgr-series-id') }"
                >
                  {{ $t('settings_layout.series') }}
                </nuxt-link>
              </li>
            </ul>
            <!-- <nuxt-link
              :to="`/publish_mgr/${loginUser.uid}`"
              class="item-text"
              :class="{ active: 'publish_mgr-id' === $route.name }"
            >
              {{ $t('settings_layout.publish_mgr') }}
            </nuxt-link> -->
          </li>

          <!-- 我的收藏 -->
          <li class="menu-item">
            <img class="item-img" src="@/assets/images/collection.png" />
            <span>{{ $t('settings_layout.my_collects') }}</span>
            <ul class="sub-menu fs-sm">
              <li class="sub-menu-item text-gray">
                <nuxt-link
                  :to="$i18n.path(`collection/article/${loginUser.uid}`)"
                  class="item-text"
                  :class="{ active: $route.name.includes('collection-article-id') }"
                >
                  {{ $t('settings_layout.article') }}
                </nuxt-link>
              </li>
              <li class="sub-menu-item text-gray">
                <nuxt-link
                  :to="$i18n.path(`collection/series/${loginUser.uid}`)"
                  class="item-text"
                  :class="{ active: $route.name.includes('collection-series-id') }"
                >
                  {{ $t('settings_layout.series') }}
                </nuxt-link>
              </li>
            </ul>
            <!-- <nuxt-link
              :to="`/collection/${loginUser.uid}`"
              class="item-text"
              :class="{ active: 'collection-id' === $route.name }"
            >
              {{ $t('settings_layout.my_collects') }}
            </nuxt-link> -->
          </li>

          <!-- 历史记录 -->
          <li class="menu-item">
            <img class="item-img" src="@/assets/images/history.png" />
            <span>{{ $t('settings_layout.my_history') }}</span>
            <ul class="sub-menu fs-sm">
              <li class="sub-menu-item text-gray">
                <nuxt-link
                  :to="$i18n.path(`history/article/${loginUser.uid}`)"
                  class="item-text"
                  :class="{ active: $route.name.includes('history-article-id') }"
                >
                  {{ $t('settings_layout.article') }}
                </nuxt-link>
              </li>
              <li class="sub-menu-item text-gray">
                <nuxt-link
                  :to="$i18n.path(`history/series/${loginUser.uid}`)"
                  class="item-text"
                  :class="{ active: $route.name.includes('history-series-id') }"
                >
                  {{ $t('settings_layout.series') }}
                </nuxt-link>
              </li>
            </ul>
            <!-- <nuxt-link
              :to="`/history/${loginUser.uid}`"
              class="item-text"
              :class="{ active: 'history-id' === $route.name }"
            >
              {{ $t('settings_layout.my_history') }}
            </nuxt-link> -->
          </li>

          <!-- 设置 -->
          <li class="menu-item">
            <img class="item-img" src="@/assets/images/release.png" />
            <span class="item-text">
              {{ $t('settings_layout.settings') }}
            </span>
            <ul class="sub-menu fs-sm">
              <li class="sub-menu-item text-gray">
                <nuxt-link :to="$i18n.path(`settings/password`)" class="item-text" :class="{ active: $route.name.includes('settings-password') }">
                  {{ $t('settings_layout.setting_password') }}
                  <!-- {{ $t('settings_layout.deving') }} -->
                </nuxt-link>
              </li>
              <li class="sub-menu-item text-gray">
                <nuxt-link :to="$i18n.path(`settings/infos`)" class="item-text" :class="{ active: $route.name.includes('settings-infos') }">
                  {{ $t('settings_layout.setting_infos') }}
                  <!-- {{ $t('settings_layout.deving') }} -->
                </nuxt-link>
              </li>
              <li class="sub-menu-item text-gray">
                <nuxt-link :to="$i18n.path(`settings/medal_center`)" class="item-text" :class="{ active: $route.name.includes('medal_center') }">
                  {{ $t('settings_layout.medal_center') }}
                </nuxt-link>
              </li>

              <!-- <li class="sub-menu-item text-gray">
                <nuxt-link :to="$i18n.path(`settings/email`)" class="item-text" :class="{ active: $route.name.includes('settings-email') }">
                  {{ $t('settings_layout.setting_email') }}
                </nuxt-link>
              </li> -->
            </ul>
          </li>
        </ul>

        <!-- 分割线 -->
        <p class="side-item line"></p>

        <!-- 广告位置 -->
        <div v-if="ad.length" class="poster">
          <div v-for="(item, index) in ad" :key="index" class="poster-box">
            <el-image :src="item.pic_url" lazy fit="cover" @click="doRecomAction(item)"></el-image>
          </div>
        </div>

        <!-- 分割线 -->
        <p class="side-item line"></p>
      </div>

      <!-- 文章内容不封 -->
      <div class="contents">
        <nuxt />
      </div>
    </div>
    <Footer />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Nav from '@/components/Nav.vue';
import Footer from '@/components/Footer.vue';
import UserAvatar from '@/components/UserAvatar.vue';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  name: 'SettingsLayout',
  components: {
    UserAvatar,
    Nav,
    Footer
  },
  data: () => ({
    ad: []
  }),
  computed: {
    ...mapGetters('login', ['loginUser'])
  },
  mounted() {
    this.getAd();
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  },
  methods: {
    getAd() {
      this.$axios.$post('/api/recom/get-recommends', { class: 3 }).then((res) => {
        this.ad = res.code === 0 && res.data.length > 0 ? res.data[0].items : [];
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.containner {
  padding-top: 30px;
  .contents {
    width: 910px;
    min-height: 900px;
    background: #fff;
  }
  .sidebar {
    background: #fff;
    width: 240px;
    height: 1108px;
    .side-item {
      // margin-bottom: 10px;
      &.line {
        height: 10px;
        background: $gray-f7;
      }
    }
    .poster {
      background: $gray-f7;
      .poster-box {
        cursor: pointer;
        width: 240px;
        height: 145px;
        margin-bottom: 10px;
        .el-image {
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
      }
    }
    // 用户头像
    .user-avatar-box {
      min-height: 170px;
      padding-top: 15px;
      padding-bottom: 20px;
      .username {
        padding: 15px 0;
      }
      .sign {
        padding: 0 30px;
        .svg-icon {
          margin-left: 5px;
          height: 12px;
          max-width: 12px;
          fill: $primary;
        }
      }
    }
    // 用户信息
    .user-infos {
      padding: 15px;
      .infos-item {
        height: 50px;
      }
    }

    // 菜单
    .sidebar-menus {
      min-height: 600px;
      background: #fff;
      padding: 30px;
      padding-bottom: 0;
      .menu-item {
        padding-bottom: 20px;
        &:first-of-type {
          .item-text {
            text-decoration: none;
            color: #424242;
          }
        }
        &:last-of-type {
          span {
            color: #424242;
          }
        }
        .item-img {
          width: 20px;
          margin-right: 20px;
        }
        .item-text {
          text-decoration: none;
          color: $gray;
          &.active {
            color: $primary;
          }
        }
        .sub-menu {
          flex: 1;
          padding-left: 40px;
          padding-top: 15px;
          &-item {
            padding: 15px 0;
            &.active {
              color: $primary;
            }
          }
        }
      }
    }
  }
}
</style>

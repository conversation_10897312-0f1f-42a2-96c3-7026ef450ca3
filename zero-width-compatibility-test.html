<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零宽字符兼容性深度测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .warning {
            color: orange;
            font-weight: bold;
        }
        .test-text {
            background: #fff;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 5px 0;
            font-family: monospace;
        }
        textarea {
            width: 100%;
            height: 60px;
            font-family: monospace;
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>🔬 零宽字符兼容性深度测试</h1>
    <p>这个测试将检查不同零宽字符在各种环境下的兼容性。</p>
    
    <div id="results"></div>
    
    <script>
        function runCompatibilityTest() {
            let output = '';
            
            // 定义不同的零宽字符
            const zeroWidthChars = {
                'ZWSP': '\u200B',      // 零宽空格
                'ZWNJ': '\u200C',      // 零宽非连接符
                'ZWJ': '\u200D',       // 零宽连接符
                'WJ': '\u2060',        // 词连接符
                'BOM': '\uFEFF',       // 字节顺序标记
                'ZWSP_ALT': '\u180E',  // 蒙古文元音分隔符
                'ZWNBSP': '\uFEFF'     // 零宽非断行空格
            };
            
            // 测试1：不同零宽字符的基础测试
            output += '<div class="test-section">';
            output += '<h2>📝 测试1：不同零宽字符类型测试</h2>';
            output += '<table>';
            output += '<tr><th>字符名称</th><th>Unicode</th><th>测试文本</th><th>长度</th><th>复制测试</th></tr>';
            
            const baseText = "测试文本";
            const testResults = {};
            
            Object.entries(zeroWidthChars).forEach(([name, char]) => {
                const testText = baseText + char + "结束";
                const expectedLength = baseText.length + 1 + 2; // 基础文本 + 零宽字符 + "结束"
                
                testResults[name] = {
                    char: char,
                    text: testText,
                    length: testText.length,
                    expected: expectedLength,
                    works: testText.length === expectedLength
                };
                
                output += '<tr>';
                output += '<td>' + name + '</td>';
                output += '<td>' + char.charCodeAt(0).toString(16).toUpperCase() + '</td>';
                output += '<td class="test-text">' + testText + '</td>';
                output += '<td>' + testText.length + '/' + expectedLength + '</td>';
                output += '<td><textarea id="copy_' + name + '">' + testText + '</textarea></td>';
                output += '</tr>';
            });
            
            output += '</table>';
            output += '<p><em>请分别复制上面每个文本框的内容到记事本，然后粘贴回来，看长度是否保持。</em></p>';
            output += '</div>';
            
            // 测试2：组合零宽字符测试
            output += '<div class="test-section">';
            output += '<h2>🔄 测试2：组合零宽字符测试</h2>';
            
            const combinations = [
                { name: '单个ZWSP', text: '文本\u200B内容' },
                { name: '双重ZWSP', text: '文本\u200B\u200B内容' },
                { name: 'ZWSP+ZWNJ', text: '文本\u200B\u200C内容' },
                { name: '三重组合', text: '文本\u200B\u200C\u200D内容' },
                { name: 'WJ字符', text: '文本\u2060内容' },
                { name: 'BOM字符', text: '文本\uFEFF内容' }
            ];
            
            output += '<table>';
            output += '<tr><th>组合类型</th><th>原始长度</th><th>实际长度</th><th>状态</th><th>复制测试</th></tr>';
            
            combinations.forEach((combo, index) => {
                const originalLength = combo.text.length;
                const status = originalLength > 4 ? '✅ 正常' : '❌ 异常';
                
                output += '<tr>';
                output += '<td>' + combo.name + '</td>';
                output += '<td>应该 > 4</td>';
                output += '<td>' + originalLength + '</td>';
                output += '<td>' + status + '</td>';
                output += '<td><textarea id="combo_' + index + '">' + combo.text + '</textarea></td>';
                output += '</tr>';
            });
            
            output += '</table>';
            output += '</div>';
            
            // 测试3：编码方式测试
            output += '<div class="test-section">';
            output += '<h2>🔐 测试3：不同编码方式测试</h2>';
            
            function encodeWithDifferentMethods(text) {
                const methods = {
                    'ZWSP二进制': text.split('').map(char => {
                        const binary = char.charCodeAt(0).toString(2).padStart(8, '0');
                        return binary.split('').map(bit => bit === '1' ? '\u200B' : '\u200C').join('');
                    }).join('\u200D'),
                    
                    'WJ二进制': text.split('').map(char => {
                        const binary = char.charCodeAt(0).toString(2).padStart(8, '0');
                        return binary.split('').map(bit => bit === '1' ? '\u2060' : '\u200C').join('');
                    }).join('\u200D'),
                    
                    'BOM编码': text.split('').map(char => {
                        return char + '\uFEFF';
                    }).join(''),
                    
                    '混合编码': text.split('').map((char, index) => {
                        const chars = ['\u200B', '\u200C', '\u200D', '\u2060'];
                        return char + chars[index % chars.length];
                    }).join('')
                };
                
                return methods;
            }
            
            const testString = "LN";
            const encodedVersions = encodeWithDifferentMethods(testString);
            
            output += '<table>';
            output += '<tr><th>编码方法</th><th>编码长度</th><th>复制测试</th></tr>';
            
            Object.entries(encodedVersions).forEach(([method, encoded], index) => {
                output += '<tr>';
                output += '<td>' + method + '</td>';
                output += '<td>' + encoded.length + '</td>';
                output += '<td><textarea id="encoded_' + index + '">' + encoded + '</textarea></td>';
                output += '</tr>';
            });
            
            output += '</table>';
            output += '</div>';
            
            // 测试4：浏览器环境检测
            output += '<div class="test-section">';
            output += '<h2>🌐 测试4：浏览器环境信息</h2>';
            
            output += '<table>';
            output += '<tr><th>属性</th><th>值</th></tr>';
            output += '<tr><td>浏览器</td><td>' + navigator.userAgent + '</td></tr>';
            output += '<tr><td>平台</td><td>' + navigator.platform + '</td></tr>';
            output += '<tr><td>语言</td><td>' + navigator.language + '</td></tr>';
            output += '<tr><td>字符编码</td><td>' + document.characterSet + '</td></tr>';
            output += '</table>';
            output += '</div>';
            
            // 测试5：实时复制检测
            output += '<div class="test-section">';
            output += '<h2>📋 测试5：实时复制检测</h2>';
            output += '<p>下面的文本包含零宽字符，请复制到记事本并粘贴回来：</p>';
            
            const liveTestText = "这是\u200B实时\u200C测试\u200D文本";
            output += '<div>';
            output += '<p><strong>原始文本（长度: ' + liveTestText.length + '）:</strong></p>';
            output += '<textarea id="liveTest" style="height: 40px;">' + liveTestText + '</textarea>';
            output += '<button onclick="checkLiveTest()">检查复制结果</button>';
            output += '<div id="liveResult"></div>';
            output += '</div>';
            output += '</div>';
            
            // 建议和结论
            output += '<div class="test-section">';
            output += '<h2>💡 测试建议</h2>';
            output += '<ol>';
            output += '<li><strong>复制测试：</strong>将每个文本框的内容复制到记事本，再粘贴回来</li>';
            output += '<li><strong>长度检查：</strong>粘贴后的文本长度是否与原始长度一致</li>';
            output += '<li><strong>兼容性评估：</strong>哪种零宽字符在你的环境下最稳定</li>';
            output += '<li><strong>编码选择：</strong>根据测试结果选择最佳的编码方案</li>';
            output += '</ol>';
            
            output += '<h3>🎯 预期结果分析</h3>';
            output += '<ul>';
            output += '<li><span class="success">如果所有测试都保持长度</span> → 零宽字符完全兼容</li>';
            output += '<li><span class="warning">如果部分测试保持长度</span> → 选择兼容的字符类型</li>';
            output += '<li><span class="error">如果所有测试都丢失长度</span> → 考虑其他水印方案</li>';
            output += '</ul>';
            output += '</div>';
            
            document.getElementById('results').innerHTML = output;
        }
        
        // 实时检测函数
        function checkLiveTest() {
            const textarea = document.getElementById('liveTest');
            const currentText = textarea.value;
            const originalLength = 9; // "这是实时测试文本" + 3个零宽字符
            
            let result = '<h4>检测结果：</h4>';
            result += '<p>当前长度: ' + currentText.length + '</p>';
            result += '<p>预期长度: ' + originalLength + '</p>';
            
            if (currentText.length === originalLength) {
                result += '<p class="success">✅ 零宽字符保持完整！</p>';
            } else if (currentText.length === 6) {
                result += '<p class="error">❌ 零宽字符完全丢失</p>';
            } else {
                result += '<p class="warning">⚠️ 部分零宽字符丢失</p>';
            }
            
            document.getElementById('liveResult').innerHTML = result;
        }
        
        // 页面加载后运行测试
        window.onload = runCompatibilityTest;
    </script>
</body>
</html>

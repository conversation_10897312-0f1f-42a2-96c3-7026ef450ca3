// 历史记录数据
export const state = () => ({
  history_article_articles: [], // 历史记录-帖子页-帖子
  history_article_articles_paginate: {}, // 历史记录-帖子页-帖子-分页

  history_article_books: [], // 历史记录-帖子页-图书
  history_article_books_paginate: [], // 历史记录-帖子页-图书-分页

  history_series_articles: [], // 历史记录-合集页-帖子
  history_series_articles_paginate: [], // 历史记录-帖子页-帖子-分页

  history_series_books: [], // 历史记录-合集页-图书
  history_series_books_paginate: [] // 历史记录-帖子页-帖子-分页
});

export const mutations = {
  getHistoryhArticleArticles(state, data) {
    state.history_article_articles = data.list;
    state.history_article_articles_paginate = data.page_info;
  },
  getHistoryhArticleBooks(state, data) {
    state.history_article_books = data.list;
    state.history_article_books_paginate = data.page_info;
  },
  getHistoryhSeriesArticles(state, data) {
    state.history_series_articles = data.list;
    state.history_series_articles_paginate = data.page_info;
  },
  getHistoryhSeriesBooks(state, data) {
    state.history_series_books = data.list;
    state.history_series_books_paginate = data.page_info;
  },
  deleteHistory(state, { dataKey, id, idKey }) {
    state[dataKey] = state[dataKey].filter((item) => item[idKey] !== id);
  }
};

export const actions = {
  async deleteHistory({ commit }, { _class, fid, dataKey, idKey }) {
    const res = await this.$axios.$post('/api/history/del-history', {
      class: _class,
      fid
    });

    if (res.code === 0) {
      commit('deleteHistory', { id: fid, dataKey, idKey });
      return Promise.resolve();
    } else {
      return Promise.reject(res);
    }
  },

  // 历史记录-帖子页-帖子
  async getHistoryhArticleArticles({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/history/get-history', {
      uid,
      page,
      type: 0,
      class: 1
    });
    if (res.code === 0) {
      commit('getHistoryhArticleArticles', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 历史记录-帖子页-图书
  async getHistoryhArticleBooks({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/history/get-history', {
      uid,
      page,
      type: 1,
      class: 1
    });
    if (res.code === 0) {
      commit('getHistoryhArticleBooks', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 历史记录-合集页-合集
  async getHistoryhSeriesArticles({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/history/get-history', {
      uid,
      page,
      type: 0,
      class: 2
    });
    if (res.code === 0) {
      commit('getHistoryhSeriesArticles', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 历史记录-合集页-图书
  async getHistoryhSeriesBooks({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/history/get-history', {
      uid,
      page,
      type: 1,
      class: 2
    });
    if (res.code === 0) {
      commit('getHistoryhSeriesBooks', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  }
};

// 前10条数据
export const getters = {
  history_article_articles_10(state) {
    const books = state.history_article_articles;
    return books.length ? books.slice(0, 10) : [];
  },
  history_article_books_10(state) {
    const books = state.history_article_books;
    return books.length ? books.slice(0, 10) : [];
  }
};

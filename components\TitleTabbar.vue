<template>
  <div class="articles-container">
    <!-- tabbar 标题 -->
    <div class="tabbar-title">
      <span class="fs-ml text-dark">{{ title }}</span>
      <button class="tabbar-item btn" :class="{ active: tabbarIndex === 0 }" @click="changeTabbar(0)">
        {{ $t('components.title_tabbar.book') }}
      </button>
      <button class="tabbar-item btn" :class="{ active: tabbarIndex === 1 }" @click="changeTabbar(1)">
        {{ $t('components.title_tabbar.all') }}
      </button>

      <!-- 右边: 更多 -->
      <slot name="more">
        <button v-if="showMore" class="btn-more fs-sm btn" @click="more">
          <span>{{ $t('components.title_tabbar.more') }}</span>
          <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 440.2 740.47">
            <path
              class="cls-1"
              d="M518.6,400,192.5,97.8a38.31,38.31,0,0,1,0-56.8c16.1-14.9,42.2-14.9,58.3,0L607.5,371.6a38.31,38.31,0,0,1,0,56.8L250.8,759a43.25,43.25,0,0,1-58.3,0,38.42,38.42,0,0,1,0-56.9Z"
              transform="translate(-179.9 -29.83)"
            />
          </svg>
        </button>
      </slot>
    </div>

    <div class="article-book-container">
      <!-- 内容区域: 全部 -->
      <div v-show="tabbarIndex === 1" class="articles">
        <slot name="articles" />
      </div>

      <!-- 内容区域: 图书 -->
      <div v-show="tabbarIndex === 0" class="articles">
        <slot name="books" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: [String, Number],
      required: true
    },
    showMore: {
      // 是否显示更多
      type: [Boolean, Number, String],
      default: true
    }
  },
  data() {
    return {
      tabbarIndex: 0
    };
  },
  methods: {
    changeTabbar(i) {
      this.tabbarIndex = i;
      this.$emit('tab-change', i);
    },
    more() {
      this.$emit('click-more');
    }
  }
};
</script>

<style lang="scss" scope>
.articles-container {
  width: 100%;
  background: #fff;
  .articles {
    width: 100%;
  }
  .tabbar-item {
    height: 100%;
    margin-right: 20px;
    padding-bottom: 5px;
    &:first-of-type {
      margin-left: 15px;
    }
    &.active {
      color: $primary;
      border-bottom: 2px solid $primary;
    }
    &:hover {
      color: $primary;
    }
  }
  // 更多
  .btn-more {
    float: right;
    color: $primary;
    .svg-icon {
      fill: $primary;
      height: 13px;
    }
  }
  .article-book-container {
    padding-top: 20px;
    .tabbar-article-item {
      width: 176px;
      overflow: hidden;
      padding: 0;
      margin: 0 0 20px 0;
      overflow: hidden;
    }
  }
}
</style>

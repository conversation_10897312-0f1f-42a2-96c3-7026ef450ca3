// 发布管理数据
export const state = () => ({
  publish_article_articles: [], // 发布管理-帖子页-帖子
  publish_article_articles_paginate: {}, // 发布管理-帖子页-帖子-分页信息

  publish_article_books: [], // 发布管理-帖子页-图书
  publish_article_books_paginate: {}, // 发布管理-帖子页-图书-分页信息

  publish_series_articles: [], // 发布管理-合集页-帖子
  publish_series_articles_paginate: {}, // 发布管理-合集页-帖子分页信息

  publish_series_books: [], // 发布管理-合集页-图书
  publish_series_books_paginate: {} // 发布管理-合集页-图书-分页信息
});

export const mutations = {
  setArticleOrder(state, { sid, aid, order }) {
    const all = [...state.publish_series_articles, ...state.publish_series_books];
    const series = all.find((item) => item.sid === sid);
    const articles = series.articles || [];
    const article = articles.find((item) => item.aid === aid);
    if (article && article.order) {
      article.order = order;
    }
  },
  getPublishArticleArticles(state, data) {
    state.publish_article_articles = data.list;
    state.publish_article_articles_paginate = data.page_info;
  },
  getPublishArticleBooks(state, data) {
    state.publish_article_books = data.list;
    state.publish_article_books_paginate = data.page_info;
  },
  getPublishSeriesArticles(state, data) {
    state.publish_series_articles = data.list;
    state.publish_series_articles_paginate = data.page_info;
  },
  getPublishSeriesBooks(state, data) {
    state.publish_series_books = data.list;
    state.publish_series_books_paginate = data.page_info;
  },
  deletePublish(state, { key, id, data }) {
    state[data] = state[data].filter((item) => item[key] !== id);
  }
};

export const actions = {
  // 发布管理-帖子页-帖子
  async getPublishArticleArticles({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/user/get-articles', {
      uid,
      page,
      type: 0
    });
    if (res.code === 0) {
      commit('getPublishArticleArticles', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 发布管理-帖子页-图书
  async getPublishArticleBooks({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/user/get-articles', {
      uid,
      page,
      type: 1
    });
    if (res.code === 0) {
      commit('getPublishArticleBooks', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 发布管理-合集页-(合集)
  async getPublishSeriesArticles({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/user/get-series', {
      uid,
      page,
      type: 0
    });
    if (res.code === 0) {
      commit('getPublishSeriesArticles', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  // 发布管理-合集页-图书
  async getPublishSeriesBooks({ commit }, { uid, page = 1 }) {
    const res = await this.$axios.$post('/api/user/get-series', {
      uid,
      page,
      type: 1
    });
    if (res.code === 0) {
      commit('getPublishSeriesBooks', res.data);
      return Promise.resolve();
    }
    return Promise.reject(res);
  },

  //  _class: 是否是文章(1文章 2合集) 删除文章 dataInfo: {key, id, data}
  async deletePublish({ commit }, { fid, _class, dataInfo }) {
    const res = await this.$axios.$post('/api/history/del-publish', { fid, class: _class });
    if (res.code === 0) {
      commit('deletePublish', dataInfo);
      return Promise.resolve();
    }
    return Promise.reject(res);
  }
};

// 计算出前10条数据
export const getters = {
  publish_article_articles_10(state) {
    const books = state.publish_article_articles;
    return books.length ? books.slice(0, 10) : [];
  },
  publish_article_books_10(state) {
    const books = state.publish_article_books;
    return books.length ? books.slice(0, 10) : [];
  }
};

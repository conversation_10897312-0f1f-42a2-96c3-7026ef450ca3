<template>
  <div class="publish_mgr_container">
    <TitleTabbar :title="$t('history.history')" all-count="123" book-count="435" :show-more="false" @tab-change="tabChange">
      <!-- 内容区域: 全部 -->
      <template v-slot:articles>
        <div v-if="history_article_articles.length">
          <div v-for="(item, key) in history_article_articles" :key="key" class="collect-item">
            <CollectionItem
              :banner="item.cover"
              :title="item.title"
              :date="item.last_time | date2short"
              :author="item.group_name"
              :views="item.hits"
              :comments="item.comments"
              :coins="item.coins"
              :collections="item.favorites"
              :shares="item.shares"
              :zans="item.likes"
              :gid="item.parent_gid"
              @cover-click="toPage(`/detail/${item.aid}`)"
              @cover-click2="toPage(`/themereply/${item.aid}`)"
              @delete-btn-click="deleteHistory(item.aid)"
            />
          </div>
          <div class="pagination">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="articlesLoading"
              :hide-on-single-page="true"
              :current-page="history_article_articles_paginate.cur"
              :total="history_article_articles_paginate.count"
              :page-size="history_article_articles_paginate.size"
              @current-change="articlePageChange"
            />
          </div>
        </div>
        <!-- 如果没有帖子显示: 尚未浏览任何文章 -->
        <Loading v-if="articlesLoading && history_article_articles.length === 0" />
        <p
          v-if="!articlesLoading && history_article_articles.length === 0"
          class="empty-article-tips flex ai-center jc-center fs-sm text-gray"
          style="height: 300px"
        >
          {{ $t('settings.no_history') }}
        </p>
      </template>

      <!-- 内容区域: 图书 -->
      <template v-slot:books>
        <div v-if="history_article_books.length">
          <div v-for="(item, key) in history_article_books" :key="key" class="collect-item">
            <CollectionItem
              :banner="item.cover"
              :title="item.title"
              :date="item.last_time | date2short"
              :author="item.group_name"
              :views="item.hits"
              :comments="item.comments"
              :coins="item.coins"
              :collections="item.favorites"
              :shares="item.shares"
              :gid="item.parent_gid"
              :zans="item.likes"
              mode="vertical"
              @cover-click="toPage(`/detail/${item.aid}`)"
              @cover-click2="toPage(`/themereply/${item.aid}`)"
              @delete-btn-click="deleteHistory(item.aid)"
            />
          </div>
          <div class="pagination">
            <el-pagination
              class="my-pagination"
              layout="prev, pager, next, jumper"
              :prev-text="$t('paginate.prev')"
              :next-text="$t('paginate.next')"
              :disabled="booksLoading"
              :hide-on-single-page="true"
              :current-page="history_article_books_paginate.cur"
              :total="history_article_books_paginate.count"
              :page-size="history_article_books_paginate.size"
              @current-change="booksPageChange"
            />
          </div>
        </div>

        <!-- 如果没有帖子显示: 尚未浏览任何文章 -->
        <p v-if="history_article_books.length === 0" class="empty-article-tips flex ai-center jc-center fs-sm text-gray" style="height: 300px">
          {{ $t('settings.no_history') }}
        </p>
      </template>
    </TitleTabbar>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import TitleTabbar from '@/components/TitleTabbar.vue';
import CollectionItem from '@/components/CollectionItem.vue';
import Loading from '@/components/Loading';

export default {
  layout: 'settings',
  middleware: ['auth'],
  components: {
    TitleTabbar,
    Loading,
    CollectionItem
  },

  async fetch({ store, params }) {
    const uid = params.id;
    // await store.dispatch('userHistory/getHistoryhArticleArticles', { uid });
    await store.dispatch('userHistory/getHistoryhArticleBooks', { uid });
  },

  asyncData({ params }) {
    return {
      uid: params.id
    };
  },

  data: () => ({
    uid: 0,
    tabIndex: 0,
    articlesLoading: false, // 加载文章loading
    booksLoading: false // 加载图书 loading
  }),

  computed: {
    ...mapState('userHistory', [
      'history_article_articles',
      'history_article_articles_paginate',
      'history_article_books',
      'history_article_books_paginate'
    ])
  },

  methods: {
    // 文章分页
    articlePageChange(page) {
      this.articlesLoading = true;
      this.$store
        .dispatch('userHistory/getHistoryhArticleArticles', {
          page,
          uid: this.uid
        })
        .finally(() => {
          this.articlesLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // 图书分页
    booksPageChange(page) {
      this.booksLoading = true;
      this.$store
        .dispatch('userHistory/getHistoryhArticleBooks', {
          // .dispatch('userHistory/getHistoryhArticleArticles', {
          page,
          uid: this.uid
        })
        .finally(() => {
          this.booksLoading = false;
          this.$nextTick(() => this.backtop());
        });
    },

    // 获取图书
    tabChange(i) {
      this.tabIndex = i;
      if (i === 1 && this.history_article_articles.length === 0) {
        this.articlePageChange(1);
      }
    },

    // 删除历史
    deleteHistory(fid) {
      const payload = {
        _class: 1,
        fid,
        idKey: 'aid',
        dataKey: 'history_article_articles'
      };
      if (this.tabIndex === 0) {
        payload.dataKey = 'history_article_books';
      }
      this.$confirm(this.$t('tips.are_you_sure'), this.$t('components.type_warn'), {
        confirmButtonText: this.$t('components.btn_confirm'),
        cancelButtonText: this.$t('components.btn_cancel'),
        type: 'warning'
      })
        .then(() => {
          this.$store
            .dispatch('userHistory/deleteHistory', payload)
            .then(() => {
              this.$message.success(this.$t('tips.delete_success'));
            })
            .catch(() => {
              this.$message.success(this.$t('tips.delete_fail'));
            });
        })
        .catch(Function.prototype);
    }
  },

  head() {
    const title = this.$t('history.title') + '-' + this.$t('title');
    return { title };
  }
};
</script>

<style lang="scss" scope>
@import '@/assets/scss/collect.scss';
</style>

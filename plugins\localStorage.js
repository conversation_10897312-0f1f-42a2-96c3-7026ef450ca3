/* eslint-disable */
// 持久保存vuex数据, 否则一刷新就没有了

import createPersistedState from 'vuex-persistedstate';
import Cookie from 'js-cookie';

export default ({ store }) => {
  window.onNuxtReady(() => {
    createPersistedState({
      storage: window.localStorage,
      reducer(state) {
        const localdata = {
          home: state.home,
          viewTab: state.viewTab,
          msg: state.msg
        };
        try {
          const token = Cookie.get('token');
          JSON.parse(token);
          localdata.login = state.login;
        } catch (e) { }
        return localdata;
      }
    })(store);
  });
};

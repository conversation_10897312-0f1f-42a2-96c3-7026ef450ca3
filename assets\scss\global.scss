// 定义全局工具样式类

// 导入全局变量
@import './variables.scss';

// 设计给的参考: ----------------
// 主色：  #39d7d9
// 字色黑：#424242
// 字色灰：#b2b2b2
// 链接色：#6089b7

// 分割线灰：#eeeeee
// 常用投影：#000000  10% 透明度 大小15
// 常用圆角：5px

// 常用字号：
// 30  内容页大标题
// 25  首页分类标题
// 20
// 18  内容页正文
// 15
// 13  最小字号
// -------------------------

*,
*:before,
*:after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
}

// layout-container
.layout-container {
  width: 1160px;
  margin: 0 auto;
}

// hover hover鼠标变成手指
.hover {
  &:hover {
    cursor: pointer;
  }
}

// text color classes && bg color classes
$colors: (
  'primary': $primary,
  'white': $white,
  'dark': $dark,
  'gray': $gray,
  'gray-f7': $gray-f7,
  'gray-white': $gray-white,
  'link': $link-color
);

@each $colorName, $color in $colors {
  .text-#{$colorName} {
    color: $color;
  }

  .bg-#{$colorName} {
    background-color: $color;
  }
}

// margins & paddings
$vals: (
  '5': 5px,
  '10': 10px,
  '15': 15px,
  '20': 20px,
  '25': 25px,
  '30': 30px
);
$dirs: (left, right, top, bottom);

@each $direction in $dirs {
  @each $key, $value in $vals {
    .mar-#{$direction}-#{$key} {
      margin-#{$direction}: $value;
    }
    .pad-#{$direction}-#{$key} {
      padding-#{$direction}: $value;
    }
  }
}

// font-size classes
$fontSizes: (
  'xs': $xs,
  'sm': $sm,
  'sd': $sd,
  'md': $md,
  'ml': $ml,
  'lg': $lg,
  'content': $md,
  'category': $lg,
  'title': $maxlg
);

@each $sizeName, $size in $fontSizes {
  .fs-#{$sizeName} {
    font-size: $size;
  }
}

// text-align
$text-aligns: (left, center, right);
@each $value in $text-aligns {
  .text-#{$value} {
    text-align: $value;
  }
}

// flex classes
.flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-inline {
  display: inline-flex;
}

.flex-cols {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}
$flex-jcs: (
  'start': flex-start,
  'end': flex-end,
  'center': center,
  'between': space-between,
  'around': space-around
);

@each $key, $val in $flex-jcs {
  .jc-#{$key} {
    justify-content: $val;
  }
}

$flex-ais: (
  'start': flex-start,
  'end': flex-end,
  'center': center,
  'stretch': stretch
);

@each $key, $val in $flex-ais {
  .ai-#{$key} {
    align-items: $val;
  }
}

// text-hide classess
$text-hide-lines: (1, 2, 3, 4, 5);
@each $line in $text-hide-lines {
  .text-hide-#{$line} {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
    word-break: break-all;
  }
}

// btns
.btn {
  border: none;
  background: none;
  &:focus {
    outline: none;
  }

  &-sm {
    width: 100px;
    height: 30px;
    border-radius: 30px;
  }

  &:hover {
    cursor: pointer;
  }

  &-block {
    display: block;
    width: 100%;
  }

  &-gray {
    background-color: $white;
    border: 1px solid $gray-white;
    color: $gray;
  }

  &-primary {
    background-color: $primary;
    color: $white;
    &.border {
      background: #fff !important;
      color: $primary;
      border: 1px solid $primary;
    }
  }
}

// 等级会展
.lv-icon {
  font-size: $xs;
  display: inline-block;
  border-radius: 5px;
  padding: 4px 8px;
  color: $white;
  &.sm {
    padding: 2px 4px;
  }
  &.lv-0 {
    background: $primary;
    &.border {
      background: #fff !important;
      color: $primary;
      border: 1px solid $primary;
    }
  }
  &.lv-1 {
    background: #dedede;
  }
  &.lv-2 {
    background: #cee6c4;
  }
  &.lv-3 {
    background: #fbacc1;
  }
  &.lv-4 {
    background: #bc96c5;
  }
  &.lv-5 {
    background: #a690e5;
  }
  &.lv-6 {
    background: #56d2f6;
  }
  &.lv-7 {
    background: #ffd41b;
  }
  &.lv-8 {
    background: #ffa632;
  }
  &.lv-9 {
    background: #39d7d9;
  }
}

// 顶部三角形
.tail-arrow-top::after {
  border-radius: 5px;
  position: absolute;
  top: -2px;
  left: 50%;
  padding: 8px;
  border: inherit;
  border-right: none;
  border-bottom: none;
  content: '';
  background: inherit;
  transform: rotate(45deg) translate(-50%);
}

// 隐藏或者显示配合 :class 使用, v-show 会改变元素的 display 属性
.hide {
  visibility: hidden;
}
.show {
  visibility: visible !important;
}

// 输入框
.input-control {
  width: 100%;
  height: 30px;
  border-radius: 5px;
  border: none;
  padding: 0 15px;
  border: 1px solid $gray-white;
  margin-right: 10px;
  color: $dark !important;
  &::-webkit-input-placeholder {
    color: $gray;
  }
  &:-moz-placeholder {
    color: $gray;
  }
  &::-moz-placeholder {
    color: $gray;
  }
  &:-ms-input-placeholder {
    color: $gray;
  }
}

// 覆盖 element-ui 组件的样式
.el-message-box {
  .el-button {
    border-radius: 22px;
    &.el-button--primary {
      background-color: $primary;
      border-color: $primary;
      color: #fff;
    }

    &.el-button--primary:hover {
      color: $white;
      border-color: $primary;
    }

    &:hover {
      color: $primary;
      border-color: $primary;
    }
  }
}

// 不能选择
.dis-select {
  user-select: none;
}

// 覆盖 el-pagination 的样式
.my-pagination {
  font-weight: normal !important;
  color: $dark;
  .btn-prev,
  .btn-next {
    border: 1px solid #eee;
    border-radius: 5px;
    text-align: center;
    padding: 0 10px !important;
  }
  .el-pager {
    .number {
      height: 30px;
      border-radius: 5px;
      background-color: #fff;
      border: 1px solid #eeeeee;
      margin: 0 5px;
      font-weight: normal !important;
      &.active {
        color: #fff;
        background-color: $primary;
      }
    }
  }
}

.el-button {
  &.el-button--primary {
    background-color: $primary;
    border-color: $primary;
    color: $white;
    &.is-disabled {
      background-color: $primary;
      border-color: $primary;
      color: $white;
      &:hover {
        background-color: $primary;
        border-color: $primary;
      }
    }
  }
  &.lk-primary-button {
    background-color: $primary;
    border-color: $primary;
    color: $white;
    &:focus,
    &:hover {
      background-color: $primary-hover;
      border-color: $primary-hover;
      color: $white;
    }
  }
}

// nuxt-link
.nuxt-link {
  text-decoration: none;
  color: $dark;
}

// border-radius
.radius-5 {
  border-radius: 5px;
  overflow: hidden;
}

.radius-10 {
  border-radius: 10px;
  overflow: hidden;
}

// 隐藏站长统计的js代码生成的a标签, 因为所有的
// nuxt.js 的生成的代码都会在 body > #nuxt 中,
// 所以 nuxt.js 的代码不会有影响
body > a {
  color: transparent !important;
  opacity: 1 !important;
  display: none !important;
}

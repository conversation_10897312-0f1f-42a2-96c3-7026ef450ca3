(function () {
	'use strict';

	sceditor.locale['nl'] = {
		'Bold': 'Vet',
		'Italic': 'Schuingedrukt',
		'Underline': 'Onderstreept',
		'Strikethrough': 'Doorhalen',
		'Subscript': 'Subscript',
		'Superscript': 'Superscript',
		'Align left': '<PERSON>s uitlijnen',
		'Center': '<PERSON>ren',
		'Align right': 'Rechts uitlijnen',
		'Justify': 'Uitvullen',
		'Font Name': 'Fontnaam',
		'Font Size': 'Fontgrootte',
		'Font Color': 'Fontkleur',
		'Remove Formatting': 'Verwijder opmaak',
		'Cut': 'Knippen',
		'Your browser does not allow the cut command. Please use the keyboard shortcut Ctrl/Cmd-X': 'Je browser staat het knippen commando niet toe. Gebruik de toetsenbord sneltoets Ctrl / Cmd-X',
		'Copy': 'Kopiëren',
		'Your browser does not allow the copy command. Please use the keyboard shortcut Ctrl/Cmd-C': 'Je browser staat het kopieer commando niet toe. Gebruik de toetsenbord sneltoets Ctrl / Cmd-C',
		'Paste': 'Plakken',
		'Your browser does not allow the paste command. Please use the keyboard shortcut Ctrl/Cmd-V': 'Je browser staat het plakken commando niet toe. Gebruik de toetsenbord sneltoets  Ctrl / Cmd-V',
		'Paste your text inside the following box:': 'Plak je tekst in de volgende locatie:',
		'Paste Text': 'Tekst plakken',
		'Bullet list': 'Opsomming',
		'Numbered list': 'Genummerde lijst',
		'Undo': 'Ongedaan maken',
		'Redo': 'Opnieuw uitvoeren',
		'Rows:': 'Rijen',
		'Cols:': 'Kolommen',
		'Insert a table': 'Tabel',
		'Insert a horizontal rule': 'Horizontale regel',
		'Code': 'Code',
		'Insert a Quote': 'Citeren',
		'Width (optional):': 'Breedte (optioneel):',
		'Height (optional):': 'Hoogte (optioneel):',
		'Insert an image': 'Afbeelding',
		'E-mail:': 'E-mail',
		'Insert an email': 'E-mail',
		'URL:': 'URL:',
		'Insert a link': 'Link',
		'Unlink': 'Link verwijderen',
		'More': 'Meer',
		'Insert an emoticon': 'Emoticon',
		'Video URL:': 'Video URL',
		'Insert': 'Invoegen',
		'Insert a YouTube video': 'YouTube-video',
		'Insert current date': 'Huidige datum',
		'Insert current time': 'Huidige tijd',
		'Print': 'Print',
		'View source': 'Bron bekijken',

		dateFormat: 'day.month.year'
	};
})();

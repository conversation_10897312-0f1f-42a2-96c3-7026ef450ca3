// state
export const state = () => ({
  articleConfigs: []
});

// actions
export const actions = {
  // 获取发文章的配置
  async getArticleConfigs({ commit }) {
    const res = await this.$axios.$post('/api/article/get-config');
    if (res.code === 0) {
      commit('getArticleConfigs', res.data);
      return Promise.resolve();
    } else {
      return Promise.reject(res);
    }
  }
};

// mutations
export const mutations = {
  // 设置发布文章信息的config
  getArticleConfigs(state, configs) {
    state.articleConfigs = configs;
  }
};

// getters
export const getters = {
  // 获取文章配置信息
  articleConfigs(state) {
    return {
      groups: state.articleConfigs.groups,
      series: state.articleConfigs.series || []
    };
  }
};

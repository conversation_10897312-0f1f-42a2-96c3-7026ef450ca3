<template>
  <div class="fans-item-wrapper flex ai-center jc-between">
    <img :src="avatar" class="avatar" />
    <div class="infos flex-1 flex flex-cols">
      <span class="lh fs-md">{{ nickname }}</span>
      <span class="lh mar-top-5 text-gray fs-xs text-hide-1">{{ sign }}</span>
    </div>
    <button v-if="isMe && followed" class="btn btn-info" @click="$emit('follow-click', 1)">{{ $t('fans.already_follow') }}</button>
    <button v-if="isMe && !followed" class="btn btn-primary" @click="$emit('follow-click', 0)">{{ $t('fans.follow') }}</button>
  </div>
</template>

<script>
export default {
  props: {
    avatar: {
      // 用户头像
      type: String,
      default: ''
    },
    nickname: {
      // 用户名
      type: [String, Number],
      default: ''
    },
    sign: {
      // 签名
      type: String,
      default: ''
    },
    followed: {
      // 是否已经关注
      type: [<PERSON><PERSON><PERSON>, Number],
      default: false
    },
    isMe: {
      type: [<PERSON><PERSON><PERSON>, Number],
      default: false
    }
  }
};
</script>

<style lang="scss" scoped>
.fans-item-wrapper {
  .avatar {
    height: 50px;
    width: 50px;
    border-radius: 100%;
    overflow: hidden;
    object-fit: cover;
  }
  .infos {
    height: 100%;
    padding: 0 15px;
    .lh {
      line-height: 1.5;
    }
  }
  .btn {
    width: 90px;
    height: 30px;
    border-radius: 30px;
    &.btn-info {
      background-color: $gray-f7;
      color: $gray;
    }
  }
}
</style>

<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="rank">
    <div class="title">{{ this.$t('components.rank.rank') }}</div>
    <div class="rank-list">
      <a v-for="item in topRanks" :key="item.aid" :href="$i18n.path(`detail/${item.aid}`)" class="rank-item">
        <div class="rank-num">{{ item.rank }}</div>
        <!-- <div class=""> -->
        <el-image class="rank-cover" :src="item.cover" lazy></el-image>
        <!-- </div> -->
        <div class="rank-detail">
          <div class="rank-title" :title="item.title" v-html="item.title"></div>
          <div class="card-info">{{ item.time | date2short }}</div>
        </div>
      </a>
      <a v-for="item in bottomRanks" :key="item.aid" :href="$i18n.path(`detail/${item.aid}`)" target="_blank" class="rank-item other">
        <div class="rank-num other">{{ item.rank }}</div>
        <!-- <div class="rank-title other" :title="item.title">{{ item.title }}</div> -->
        <div class="rank-title other" :title="item.title" v-html="item.title"></div>
      </a>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    ranks: {
      type: Array,
      required: true,
      default: () => []
    },
    covers: {
      // 有图的排名数据(不能大于 maxNum)
      type: Number,
      default: 1
    },
    maxNum: {
      // 最多显示几个排名数据
      type: Number,
      default: 15 // 最多显示6个
    }
  },
  computed: {
    topRanks() {
      // 有图
      return this.ranks.slice(0, this.covers);
    },
    bottomRanks() {
      // 无图
      return this.ranks.slice(this.covers, this.maxNum);
    }
  }
};
</script>
<style lang="scss" scoped>
.rank {
  position: relative;
  .title {
    line-height: 20px;
    font-size: 20px;
    color: #424242;
    display: flex;
    align-items: flex-end;
    p {
      margin: 0;
    }
  }
  .rank-list {
    padding-top: 5px;
    a.rank-item {
      margin-top: 20px;
      position: relative;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      text-decoration: none;
      width: 255px;
      height: 124px;
      overflow: hidden;

      &.other {
        height: auto;
        margin-top: 15px;
      }

      .rank-num {
        position: absolute;
        top: 0;
        left: 0;
        min-width: 15px;
        height: 15px;
        line-height: 15px;
        background: #39d7d9;
        border-radius: 50%;
        text-align: center;
        font-size: 13px;
        color: white;

        &.other {
          position: relative;
          background-color: #d7d7d7;
        }
      }
      .rank-cover {
        width: 88px;
        height: 124px;
        border-radius: 5px;
        overflow: hidden;
        object-fit: cover;
      }
      .rank-title {
        font-size: 13px;
        line-height: 18px;
        color: #424242;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        &.other {
          padding-left: 10px;
          display: block;
          height: auto;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .rank-detail {
        position: relative;
        width: 155px;
        height: 100%;
        margin-left: 10px;
        .card-info {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 20px;
          display: flex;
          color: #b4b1b4;
          font-size: 13px;
          line-height: 13px;
          align-items: flex-end;
        }
      }
    }
  }
}
</style>

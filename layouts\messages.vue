<template>
  <div class="message-page bg-gray-f7">
    <Nav />
    <div class="message-container layout-container flex jc-between">
      <div class="sidebar-menu">
        <div class="flex menu-item menu-box">
          <div>
            <img src="@/assets/images/msg_comment.png" />
            <span class="menu-text"> {{ $t('messages_layout.comment') }} </span>
          </div>
          <div class="box-item">
            <nuxt-link :to="{ path: $i18n.path(`messages/${loginUser.uid}`), query: { type: 1 } }" class="menu-active">
              <span class="menu-text"> {{ $t('messages_layout.txt_comment') }} {{ $t('messages_layout.comment') }}</span>
            </nuxt-link>
            <nuxt-link :to="{ path: $i18n.path(`messages/${loginUser.uid}`), query: { type: 0 } }" class="menu-active">
              <span class="menu-text"> {{ $t('messages_layout.gulp_comment') }} {{ $t('messages_layout.comment') }}</span>
            </nuxt-link>
          </div>
        </div>
        <!-- <nuxt-link to="" class="flex menu-item">
          <img src="@/assets/images/msg_at.png" />
          <span class="menu-text"> @{{ $t('messages_layout.my') }} </span>
        </nuxt-link>
        <nuxt-link to="" class="flex menu-item">
          <img src="@/assets/images/msg_zan.png" />
          <span class="menu-text"> {{ $t('messages_layout.zan') }} </span>
          <span>99+</span>
        </nuxt-link> -->
        <nuxt-link :to="$i18n.path(`messages/system_notification`)" class="flex ai-center menu-item">
          <img src="@/assets/images/msg_system.png" />
          <span class="menu-text">
            {{ $t('messages_layout.system_notification') }}
          </span>
        </nuxt-link>
        <p class="line"></p>

        <!-- 广告位置 -->
        <div v-if="ad.length" class="poster">
          <div v-for="(item, index) in ad" :key="index" class="poster-box">
            <el-image :src="item.pic_url" lazy fit="cover" @click="doRecomAction(item)"></el-image>
          </div>
        </div>

        <!-- 分割线 -->
        <p class="line"></p>
      </div>
      <div class="message-contents">
        <nuxt />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Nav from '@/components/Nav.vue';
import storage from '@/plugins/mindLocalStorage.js';

export default {
  components: {
    Nav
  },
  data: () => ({
    ad: [],
    type: ''
  }),
  computed: {
    ...mapGetters('login', ['loginUser'])
  },
  mounted() {
    this.getAd();
    if (storage.get('mode')) {
      this.$includeLinkStyle(require('@/static/css/user.css'));
    }
  },
  methods: {
    getAd() {
      this.$axios.$post('/api/recom/get-recommends', { class: 3 }).then((res) => {
        this.ad = res.code === 0 && res.data.length > 0 ? res.data[0].items : [];
      });
    }
  }
};
</script>

<style lang="scss" scope>
.message-page {
  height: 100vh;
}
.message-container {
  position: fixed;
  top: 80px; // nav(50px) + 30px
  left: 50%;
  transform: translate(-50%);
  height: calc(100vh - 80px);
  overflow: hidden;
  .sidebar-menu {
    width: 240px;
    background: #fff;
    height: 314px;
    .line {
      height: 10px;
      background: $gray-f7;
    }
    .poster {
      background: $gray-f7;
      .poster-box {
        cursor: pointer;
        width: 240px;
        height: 145px;
        margin-bottom: 10px;
        .el-image {
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
      }
    }
    .menu-item {
      padding: 30px 32px;
      text-decoration: none;
      color: $primary;
      img {
        vertical-align: middle;
      }
      .menu-text {
        margin-left: 20px;
      }
    }
    .menu-box {
      flex-direction: column;
      padding-bottom: 15px;
      .box-item {
        display: flex;
        flex-direction: column;
        margin-top: 30px;
        .menu-active {
          text-decoration: none;
          color: $primary;
          &:last-of-type {
            margin-top: 29px;
          }
          span {
            margin-left: 45px;
          }
        }
      }
    }
  }
  .message-contents {
    width: 910px;
    height: 100%;
    // overflow-y: auto;
    overflow: hidden;
  }
}
</style>

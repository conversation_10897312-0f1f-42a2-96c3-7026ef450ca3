<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script src="./ckeditor.js"></script>
  </head>
  <div id="editor"></div>

  <body>
    <script>
      const editor = CKEDITOR.replace('editor', {
        height: 500,
        language: 'zh-cn',
        extraPlugins: 'bbcode,colorbutton,justify,myupload,myvotes,myemoji,myruby,myfujian',
        // removePlugins: 'elementspath,about,filebrowser,format,horizontalrule,pastetext,pastefromword,scayt,showborders,stylescombo,table,tabletools,tableselection,wsc',
        // removeButtons: 'Copy,Cut,Paste,SpecialChar,Image,Anchor,BGColor,Font,Strike,Subscript,Superscript',
        disableObjectResizing: true,
        fontSize_sizes: '小/50%;正常/100%;中等/150%;大/200%;特大/250%',
        filebrowserImageUploadUrl: '/ckeditor-upload-image'
      });

      window.addEventListener('ckeditormyuploadchange', ({ detail }) => {
        editor.insertHtml('<img src="https://upload.jianshu.io/users/upload_avatars/4263857/34d7b217-7338-48fe-81a1-98367fecdbee.jpg"/>');
      });
      window.addEventListener('ckeditormyvotesclick', () => {
        console.log('hello world');
      });
      window.addEventListener('ckeditormyemojiclick', () => {
        console.log('hello world, ckeditormyemojiclick');
      });
      window.addEventListener('ckeditormyrubyclick', () => {
        console.log('hello world, ckeditormyrubyclick');
      });
      window.addEventListener('ckeditormyfujianclick', () => {
        console.log('hello world, ckeditormyfujianclick');
      });
    </script>
  </body>
</html>

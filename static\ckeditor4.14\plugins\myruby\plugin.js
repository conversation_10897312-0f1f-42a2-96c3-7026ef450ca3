// eslint-disable-next-line no-undef
CKEDITOR.plugins.add('myruby', {
  icons: 'myruby',
  init(editor) {
    editor.addCommand('myruby', {
      exec() {
        // 在 window 上触发事件
        const myEvent = new CustomEvent('ckeditormyrubyclick');
        if (window.dispatchEvent) {
          window.dispatchEvent(myEvent);
        } else {
          window.fireEvent(myEvent);
        }
      }
    });
    editor.ui.addButton('myruby', {
      label: '上标',
      command: 'myruby',
      toolbar: 'insert'
    });
  }
});
